import React, { useEffect, useState } from 'react';
import axios from 'axios';

const About = () => {
  const [aboutItems, setAboutItems] = useState([]);
  const [details, setDetails] = useState('');

  useEffect(() => {
    axios.get('/api/about')
      .then(response => {
        setAboutItems(response.data);
        if (response.data.length > 0) {
          setDetails(response.data[0].details); // Assuming you want to show the first one initially
        }
      })
      .catch(error => {
        console.error('Error fetching about data:', error);
      });
  }, []);
  const aboutTitle = 'About';
  const chars = aboutTitle.split('');
  const totalChars = chars.length;
  const totalDuration = 0.5; // seconds
  const delayPerChar = totalDuration / totalChars;

  return (
    <li className="nav-item dropdown">
      <div className="container-one single-menu-btn">
        <div className="single-menu">
          <a className="nav-link animated-title" data-title={aboutTitle} href="#">
            {chars.map((char, i) => (
              <span
                key={i}
                className="fade-in-char"
                style={{ animationDelay: `${i * delayPerChar}s` }}
              >
                {char}
              </span>
            ))}
          </a>
        </div>

      </div>
      {aboutItems.map((item, index) => {
        return (
          <div className="menucontent" key={index}>
            <div className="description about">
              <div className="container-one">
                <div className="description-wrap" dangerouslySetInnerHTML={{ __html: details }} />
              </div>
            </div>
          </div>
        )
      })}
    </li>
  );
};

export default About;
