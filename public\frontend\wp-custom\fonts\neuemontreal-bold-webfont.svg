<?xml version="1.0" standalone="no"?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN" "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd" >
<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" version="1.1">
<metadata></metadata>
<defs>
<font id="neue_montrealbold" horiz-adv-x="1427" >
<font-face units-per-em="2048" ascent="1577" descent="-471" />
<missing-glyph horiz-adv-x="348" />
<glyph horiz-adv-x="0" />
<glyph horiz-adv-x="682" />
<glyph unicode=" "  horiz-adv-x="348" />
<glyph unicode="&#x09;" horiz-adv-x="348" />
<glyph unicode="&#xa0;" horiz-adv-x="348" />
<glyph unicode="!" horiz-adv-x="540" d="M96 156q0 77 49.5 125.5t124.5 48.5q73 0 123.5 -48.5t50.5 -125.5q0 -78 -50 -126t-124 -48q-75 0 -124.5 48t-49.5 126zM96 1145v319h348v-319l-92 -666h-164z" />
<glyph unicode="&#x22;" horiz-adv-x="864" d="M84 1464h287l-41 -551h-205zM494 1464h286l-41 -551h-204z" />
<glyph unicode="#" horiz-adv-x="1228" d="M49 305l51 246h156l53 252h-155l53 246h155l78 366h246l-78 -366h189l77 366h246l-78 -366h146l-53 -246h-146l-53 -252h145l-51 -246h-147l-66 -305h-246l66 305h-188l-66 -305h-246l66 305h-154zM502 551h188l53 252h-188z" />
<glyph unicode="$" horiz-adv-x="1280" d="M33 461h348q0 -170 194 -205v307q-9 2 -59 17q-85 25 -144 47.5t-119.5 58.5t-96.5 79t-59 105.5t-23 141.5q0 186 138 300.5t363 123.5v120h123v-124q221 -22 357 -143.5t143 -309.5h-348q-9 60 -49.5 102.5t-102.5 61.5v-277q67 -19 115 -34.5t106.5 -39.5t98.5 -48.5 t81 -60.5t65 -77.5t39.5 -96.5t15.5 -120q0 -179 -140 -297t-381 -125v-178h-123v182q-252 19 -397 145t-145 345zM422 1038q0 -51 35 -80.5t118 -56.5v252q-153 -10 -153 -115zM698 248q172 9 172 127q0 55 -42.5 88.5t-129.5 62.5v-278z" />
<glyph unicode="%" horiz-adv-x="1777" d="M41 1036q0 85 23.5 158.5t68.5 130.5t118.5 90t166.5 33q94 0 167 -32.5t117.5 -90t67.5 -131t23 -158.5t-23 -157.5t-67.5 -129.5t-117.5 -89.5t-167 -32.5q-93 0 -166.5 32.5t-118.5 89.5t-68.5 130t-23.5 157zM307 1036q0 -120 24.5 -172.5t86.5 -52.5q61 0 84.5 52 t23.5 173t-23.5 174.5t-84.5 53.5t-86 -54t-25 -174zM416 0l743 1415h205l-743 -1415h-205zM987 377q0 85 23.5 158.5t68.5 130.5t118.5 89.5t166.5 32.5q94 0 167 -32.5t117.5 -89.5t67.5 -130.5t23 -158.5t-23 -157.5t-67.5 -130t-117.5 -90t-167 -32.5q-93 0 -166.5 32.5 t-118.5 89.5t-68.5 130.5t-23.5 157.5zM1253 377q0 -120 24.5 -172.5t86.5 -52.5q61 0 85 52t24 173t-24 174t-85 53q-62 0 -86.5 -53.5t-24.5 -173.5z" />
<glyph unicode="&#x26;" d="M45 360q0 66 19 122.5t47.5 96t75.5 77.5t87.5 62t98.5 54q-46 55 -70.5 87t-51.5 78t-38.5 90.5t-11.5 96.5q0 71 29.5 137t83 118.5t139 84.5t188.5 33q192 1 315 -112t125 -261q1 -119 -73.5 -202.5t-233.5 -165.5l201 -232q51 109 69 209h287q-26 -232 -164 -434 l256 -299h-409l-68 82q-174 -115 -395 -115q-245 0 -375.5 109t-130.5 284zM393 426q0 -77 51 -127.5t148 -50.5q99 0 178 45l-264 321q-113 -82 -113 -188zM508 1104q0 -24 7.5 -45t29 -48t36 -42.5t52.5 -53.5q141 93 141 189q0 48 -33.5 80.5t-99.5 32.5 q-64 0 -98.5 -32.5t-34.5 -80.5z" />
<glyph unicode="'" horiz-adv-x="454" d="M84 1464h287l-41 -551h-205z" />
<glyph unicode="(" horiz-adv-x="698" d="M117 649q0 266 75 509t185 419h287q-199 -453 -199 -928q0 -477 199 -930h-287q-108 174 -184 422.5t-76 507.5z" />
<glyph unicode=")" horiz-adv-x="698" d="M35 -281q198 450 198 930q0 478 -198 928h287q110 -176 185 -419t75 -509q0 -259 -76 -507.5t-184 -422.5h-287z" />
<glyph unicode="*" horiz-adv-x="892" d="M63 1110l64 195l219 -91v250h205v-250l217 91l63 -195l-229 -59l146 -197l-166 -121l-136 207l-135 -207l-166 121l146 197z" />
<glyph unicode="+" horiz-adv-x="1159" d="M82 532v283h356v371h283v-371h356v-283h-356v-370h-283v370h-356z" />
<glyph unicode="," horiz-adv-x="499" d="M66 156q0 81 51.5 127.5t130.5 46.5q92 0 139 -64.5t47 -181.5q0 -178 -92.5 -291t-255.5 -135v143q75 10 124.5 63.5t49.5 117.5q-88 0 -141 46t-53 128z" />
<glyph unicode="-" horiz-adv-x="686" d="M66 377v280h557v-280h-557z" />
<glyph unicode="." horiz-adv-x="479" d="M66 156q0 77 49.5 125.5t124.5 48.5q73 0 123.5 -48.5t50.5 -125.5q0 -78 -50 -126t-124 -48q-75 0 -124.5 48t-49.5 126z" />
<glyph unicode="/" horiz-adv-x="858" d="M25 0l567 1464h266l-567 -1464h-266z" />
<glyph unicode="0" horiz-adv-x="1339" d="M49 707q0 122 23.5 231t74 203t123 162t175.5 106.5t227 38.5t227 -38.5t175 -106.5t121.5 -162t73 -203t23.5 -231t-23.5 -231t-73 -202.5t-121.5 -161.5t-175 -106.5t-227 -38.5q-154 0 -275.5 59t-196 161.5t-113 234.5t-38.5 285zM397 707q0 -224 63.5 -341.5 t211.5 -117.5q76 0 129.5 31.5t84.5 93.5t44.5 143t13.5 191q0 148 -24 246t-85.5 156t-162.5 58q-148 0 -211.5 -118t-63.5 -342z" />
<glyph unicode="1" horiz-adv-x="831" d="M53 874v246q97 0 169 24t112.5 66t60 93.5t19.5 111.5h286v-1415h-348v956q-28 -29 -63.5 -47t-81 -25t-74.5 -8.5t-80 -1.5z" />
<glyph unicode="2" horiz-adv-x="1189" d="M45 0q0 85 20.5 159.5t58 134t77.5 104t92 87.5q60 50 146.5 114t139 104t105 89.5t77.5 98.5t25 102q0 82 -44.5 128t-127.5 46q-221 0 -221 -268h-327q0 257 140 403t419 146q232 0 371 -115t139 -315q0 -89 -32.5 -171.5t-82 -144t-118 -123.5t-130.5 -106.5 t-130.5 -97t-106.5 -90.5v-4h600v-281h-1090z" />
<glyph unicode="3" horiz-adv-x="1224" d="M29 500h327q0 -126 62 -189t196 -63q108 0 162.5 44.5t54.5 125.5q0 99 -66 143.5t-194 44.5h-71v240h63q228 0 228 155q0 166 -185 166q-90 0 -149.5 -45.5t-59.5 -140.5h-327q0 208 149 337.5t414 129.5q100 0 190.5 -26t161 -74t112.5 -123t42 -166q0 -204 -205 -309 v-5q246 -78 246 -348q0 -136 -70.5 -234.5t-191.5 -147t-279 -48.5q-297 0 -453.5 138t-156.5 395z" />
<glyph unicode="4" horiz-adv-x="1275" d="M43 299v342l641 774h348v-835h209v-281h-209v-299h-348v299h-641zM299 580h385v456z" />
<glyph unicode="5" horiz-adv-x="1234" d="M27 438h348q8 -81 71.5 -135.5t169.5 -54.5q102 0 163 60.5t61 174.5q0 113 -59 175.5t-171 62.5q-133 0 -215 -103h-327l163 797h895v-280h-657l-53 -271v-4q129 101 303 101q213 0 341 -128.5t128 -353.5q0 -125 -42 -223t-116.5 -161t-174 -95.5t-218.5 -32.5 q-288 0 -445 127.5t-165 343.5z" />
<glyph unicode="6" horiz-adv-x="1265" d="M51 688q0 374 163 567t443 193q203 0 348 -109.5t179 -281.5h-348q-10 49 -54.5 90t-130.5 41q-126 0 -187.5 -93.5t-64.5 -254.5v-4q136 129 338 129q143 0 255.5 -63.5t172.5 -172.5t60 -244q0 -236 -150.5 -377t-404.5 -141q-136 0 -243.5 40t-176.5 107t-115 161 t-65 196t-19 217zM399 467q0 -105 68 -172.5t182 -67.5q112 0 180 67.5t68 172.5q0 103 -68 170.5t-180 67.5q-113 0 -181.5 -67.5t-68.5 -170.5z" />
<glyph unicode="7" horiz-adv-x="1116" d="M35 1135v280h1038v-260q-117 -94 -204.5 -227t-139 -286t-76 -313t-24.5 -329h-369q0 335 126.5 650.5t318.5 479.5v5h-670z" />
<glyph unicode="8" horiz-adv-x="1247" d="M39 406q0 240 266 356v4q-205 109 -205 299q0 119 71 207t189 132t265 44t264.5 -44t188.5 -132t71 -207q0 -188 -205 -299v-4q266 -118 266 -356q0 -141 -79.5 -242t-210 -149t-295.5 -48t-295.5 48t-210.5 149t-80 242zM387 438q0 -90 64.5 -150.5t173.5 -60.5 t173 60.5t64 150.5q0 87 -64 147t-173 60t-173.5 -60t-64.5 -147zM408 1034q0 -76 57 -126t160 -50t160 50t57 126q0 73 -57.5 123.5t-159.5 50.5t-159.5 -50.5t-57.5 -123.5z" />
<glyph unicode="9" horiz-adv-x="1265" d="M43 930q0 236 150.5 377t404.5 141q136 0 243.5 -40t176.5 -107t115 -161t65 -196t19 -217q0 -374 -163.5 -567t-443.5 -193q-203 0 -347.5 109.5t-178.5 281.5h348q10 -49 54 -90t130 -41q126 0 187.5 93.5t64.5 254.5v5q-136 -129 -338 -129q-143 0 -255 63.5t-172 172 t-60 243.5zM371 948q0 -103 67.5 -170t179.5 -67q113 0 181.5 67t68.5 170q0 105 -68 172.5t-182 67.5q-112 0 -179.5 -67.5t-67.5 -172.5z" />
<glyph unicode=":" horiz-adv-x="491" d="M72 156q0 77 49.5 125.5t124.5 48.5q73 0 123.5 -48.5t50.5 -125.5q0 -78 -50 -126t-124 -48q-75 0 -124.5 48t-49.5 126zM72 791q0 77 49.5 125.5t124.5 48.5q73 0 123.5 -48.5t50.5 -125.5q0 -78 -50 -126.5t-124 -48.5q-75 0 -124.5 48.5t-49.5 126.5z" />
<glyph unicode=";" horiz-adv-x="512" d="M72 156q0 81 51.5 127.5t130.5 46.5q92 0 139 -64.5t47 -181.5q0 -178 -92.5 -291t-255.5 -135v143q75 10 124.5 63.5t49.5 117.5q-88 0 -141 46t-53 128zM82 791q0 77 49.5 125.5t124.5 48.5q73 0 123.5 -48.5t50.5 -125.5q0 -78 -50 -126.5t-124 -48.5 q-75 0 -124.5 48.5t-49.5 126.5z" />
<glyph unicode="&#x3c;" horiz-adv-x="1062" d="M88 479v307l844 353v-281l-567 -223v-4l567 -223v-281z" />
<glyph unicode="=" horiz-adv-x="1153" d="M104 276v281h959v-281h-959zM104 711v280h959v-280h-959z" />
<glyph unicode="&#x3e;" horiz-adv-x="1028" d="M96 127v281l568 223v4l-568 223v281l844 -353v-307z" />
<glyph unicode="?" horiz-adv-x="1132" d="M29 1010q0 103 36 190.5t105.5 154t179.5 104.5t250 38q215 0 352.5 -110t137.5 -287q0 -119 -56 -200t-168 -144q-115 -64 -155.5 -117t-40.5 -160h-267q0 23 -0.5 62t-0.5 57t1.5 48.5t5.5 47t12.5 41t21 42t31.5 37.5t44 40q29 22 79 53.5t75.5 49.5t47 51.5 t21.5 74.5q0 56 -41.5 95t-119.5 39q-99 0 -151 -53.5t-52 -153.5h-348zM365 156q0 77 49.5 125.5t124.5 48.5q73 0 123.5 -48.5t50.5 -125.5q0 -78 -50 -126t-124 -48q-75 0 -124.5 48t-49.5 126z" />
<glyph unicode="@" horiz-adv-x="1861" d="M72 621q0 173 66 328t181 268.5t281 180t356 66.5q203 0 366 -65.5t266 -177t157.5 -255t54.5 -304.5q0 -135 -30.5 -233.5t-87.5 -155.5t-130.5 -84t-167.5 -27q-105 0 -202.5 45.5t-137.5 120.5h-4q-67 -158 -241 -158q-139 0 -231.5 115t-92.5 319q0 206 106 330.5 t259 124.5q90 0 139.5 -35.5t81.5 -101.5h4l14 122h281l-66 -514q-24 -184 111 -184q190 0 190 309q0 99 -25.5 191t-78.5 171t-127.5 137.5t-179.5 92t-228 33.5q-187 0 -339 -89t-236 -239.5t-84 -330.5q0 -304 169.5 -481t469.5 -177q199 0 367 66l65 -162 q-95 -51 -198.5 -69.5t-233.5 -18.5q-266 0 -462 105t-299 295t-103 442zM752 584q0 -189 118 -189q93 0 130.5 65.5t37.5 184.5q0 95 -30 142t-97 47q-71 0 -115 -65t-44 -185z" />
<glyph unicode="A" d="M-2 0l530 1464h369l528 -1464h-368l-94 305h-502l-94 -305h-369zM545 586h336l-166 540h-4z" />
<glyph unicode="B" horiz-adv-x="1374" d="M100 0v1464h711q465 0 465 -368q0 -126 -63 -202t-164 -110v-4q126 -22 205 -108t79 -240q0 -432 -502 -432h-731zM469 301h264q121 0 176.5 37.5t55.5 136.5q0 98 -55 135t-177 37h-264v-346zM469 872h254q87 0 135.5 31t48.5 115q0 44 -14 73.5t-40.5 44.5t-57 21 t-72.5 6h-254v-291z" />
<glyph unicode="C" d="M41 733q0 166 46.5 305t134.5 241.5t224 160t306 57.5q147 0 264 -42t196.5 -119t126.5 -182t60 -234h-369q-22 132 -90 204t-205 72q-89 0 -153 -33t-101 -96.5t-54 -145t-17 -188.5t17 -189.5t54 -146t101 -96.5t153 -33q139 0 207 73.5t88 207.5h369 q-13 -131 -59.5 -236.5t-125.5 -183t-197 -120t-265 -42.5q-170 0 -306 57.5t-224 160t-134.5 242t-46.5 306.5z" />
<glyph unicode="D" horiz-adv-x="1443" d="M100 0v1464h650q308 0 480.5 -191t172.5 -542q0 -353 -163.5 -542t-469.5 -189h-670zM469 301h244q321 0 321 430q0 432 -321 432h-244v-862z" />
<glyph unicode="E" horiz-adv-x="1286" d="M100 0v1464h1110v-301h-741v-260h639v-301h-639v-301h754v-301h-1123z" />
<glyph unicode="F" horiz-adv-x="1232" d="M100 0v1464h1090v-301h-721v-321h588v-301h-588v-541h-369z" />
<glyph unicode="G" horiz-adv-x="1509" d="M43 733q0 222 80 393t242 271t387 100q146 0 266.5 -41t201 -113t129.5 -164.5t62 -199.5h-369q-59 217 -297 217q-89 0 -154 -33.5t-103.5 -96t-57 -145t-18.5 -188.5q0 -465 348 -465q127 0 208.5 69t84.5 175h-279v266h660v-778h-265l-36 186h-5q-115 -219 -421 -219 q-153 0 -278.5 57.5t-210 159.5t-130 243t-45.5 306z" />
<glyph unicode="H" horiz-adv-x="1439" d="M100 0v1464h369v-573h502v573h368v-1464h-368v590h-502v-590h-369z" />
<glyph unicode="I" horiz-adv-x="571" d="M102 0v1464h369v-1464h-369z" />
<glyph unicode="J" horiz-adv-x="1116" d="M27 487v123h327v-123q0 -111 34 -165t120 -54t120 54t34 165v977h368v-977q0 -90 -13.5 -163.5t-48 -141.5t-90 -114t-144.5 -73.5t-206 -27.5t-206 27.5t-144 73.5t-89.5 114t-48 141.5t-13.5 163.5z" />
<glyph unicode="K" d="M100 0v1464h369v-594l510 594h430l-557 -645l578 -819h-431l-383 555l-147 -168v-387h-369z" />
<glyph unicode="L" horiz-adv-x="1157" d="M100 0v1464h369v-1163h651v-301h-1020z" />
<glyph unicode="M" horiz-adv-x="1708" d="M100 0v1464h451l301 -934h4l301 934h451v-1464h-348v911h-5l-272 -911h-258l-272 911h-4v-911h-349z" />
<glyph unicode="N" horiz-adv-x="1454" d="M100 0v1464h369l512 -870h4v870h369v-1464h-369l-512 870h-4v-870h-369z" />
<glyph unicode="O" horiz-adv-x="1531" d="M41 733q0 222 83 393t249 271t393 100q225 0 391 -99.5t250 -271.5t84 -393q0 -166 -49 -306.5t-140 -242.5t-228.5 -159.5t-307.5 -57.5q-171 0 -309 57.5t-228.5 160t-139 242.5t-48.5 306zM410 733q0 -100 18.5 -181t58.5 -146.5t111 -101.5t168 -36t167.5 36 t111 101.5t59 146.5t18.5 181q0 99 -18.5 180t-59 146t-111 101t-167.5 36t-168 -36t-111 -101t-58.5 -145.5t-18.5 -180.5z" />
<glyph unicode="P" horiz-adv-x="1335" d="M100 0v1464h711q233 0 364.5 -120t131.5 -347t-131.5 -347t-364.5 -120h-342v-530h-369zM469 831h264q100 0 152.5 38.5t52.5 127.5t-52.5 127.5t-152.5 38.5h-264v-332z" />
<glyph unicode="Q" horiz-adv-x="1531" d="M41 733q0 222 83 393t249 271t393 100q225 0 391 -99.5t250 -271.5t84 -393q0 -298 -152 -504l150 -149l-199 -199l-166 166q-157 -80 -358 -80q-171 0 -309 57.5t-228.5 160t-139 242.5t-48.5 306zM410 733q0 -100 18.5 -181t58.5 -146.5t111 -101.5t168 -36 q66 0 121 19l-180 180l198 199l180 -181q37 107 37 248q0 99 -18.5 180t-59 146t-111 101t-167.5 36t-168 -36t-111 -101t-58.5 -145.5t-18.5 -180.5z" />
<glyph unicode="R" horiz-adv-x="1374" d="M100 0v1464h691q247 0 384.5 -110.5t137.5 -315.5q0 -143 -78.5 -235t-218.5 -123v-4q86 -15 144.5 -69t82.5 -124t37.5 -149.5t14.5 -146.5t10 -114t28 -53v-20h-383q-16 14 -22 59q-6 42 -6 92v9q0 3 1 5q0 53 -9 113q-9 62 -27.5 113.5t-66.5 85t-122 33.5h-229v-510 h-369zM469 811h250q225 0 225 176t-225 176h-250v-352z" />
<glyph unicode="S" horiz-adv-x="1325" d="M31 481h368q0 -110 82.5 -161.5t216.5 -51.5q100 0 155.5 35t55.5 92q0 19 -5 35.5t-17.5 30.5t-25.5 25.5t-36.5 22.5t-42 19t-51.5 18t-56 16.5t-63.5 18t-66.5 19.5q-83 24 -146.5 49.5t-128 64t-105.5 84.5t-67 111t-26 144q0 93 28.5 168t79.5 126t122.5 85 t154.5 49.5t180 15.5q172 0 310 -56.5t221.5 -166t88.5 -254.5h-368q-12 83 -79 129.5t-173 46.5q-88 0 -142.5 -29t-54.5 -88q0 -27 12.5 -48t30.5 -36t58 -30.5t75 -26.5t101 -29q72 -20 119 -34.5t112 -39t107 -48.5t88 -60.5t73 -78t44.5 -97.5t17.5 -121 q0 -213 -153.5 -338t-426.5 -125q-192 0 -342 59.5t-237.5 177.5t-87.5 277z" />
<glyph unicode="T" horiz-adv-x="1288" d="M35 1163v301h1218v-301h-424v-1163h-368v1163h-426z" />
<glyph unicode="U" horiz-adv-x="1404" d="M90 528v936h369v-936q0 -139 59 -199.5t184 -60.5t184.5 60.5t59.5 199.5v936h369v-936q0 -281 -144.5 -421t-468.5 -140q-323 0 -467.5 139.5t-144.5 421.5z" />
<glyph unicode="V" horiz-adv-x="1333" d="M-4 1464h391l277 -1026h4l278 1026h389l-475 -1464h-389z" />
<glyph unicode="W" horiz-adv-x="1925" d="M0 1464h369l188 -993h4l227 993h349l227 -993h4l188 993h369l-373 -1464h-368l-219 913h-4l-220 -913h-368z" />
<glyph unicode="X" horiz-adv-x="1415" d="M6 0l486 748l-445 716h389l269 -446h4l270 446h389l-444 -712l485 -752h-410l-290 475h-4l-289 -475h-410z" />
<glyph unicode="Y" horiz-adv-x="1357" d="M2 1464h389l285 -602h4l287 602h389l-494 -923v-541h-368v541z" />
<glyph unicode="Z" horiz-adv-x="1249" d="M53 0v301l670 858v4h-629v301h1080v-301l-670 -858v-4h690v-301h-1141z" />
<glyph unicode="[" horiz-adv-x="815" d="M119 -281v1858h629v-281h-281v-1296h281v-281h-629z" />
<glyph unicode="\" horiz-adv-x="894" d="M25 1464h266l567 -1464h-266z" />
<glyph unicode="]" horiz-adv-x="815" d="M70 0h280v1296h-280v281h628v-1858h-628v281z" />
<glyph unicode="^" horiz-adv-x="954" d="M12 901l342 563h246l342 -563h-307l-158 277h-2l-156 -277h-307z" />
<glyph unicode="_" horiz-adv-x="880" d="M-6 -113h893v-280h-893v280z" />
<glyph unicode="`" horiz-adv-x="1267" d="M172 1475h369l88 -287h-246z" />
<glyph unicode="a" horiz-adv-x="1132" d="M31 268q0 150 101.5 233t297.5 122q145 29 187 45q80 34 81 90q0 98 -133 98q-89 0 -133.5 -35t-52.5 -110h-307q4 78 34 143t88 117t153.5 81t219.5 29q230 0 344.5 -102.5t114.5 -341.5v-481q0 -78 61 -78v-78q-87 -33 -190 -33q-102 0 -145 40.5t-43 103.5h-4 q-54 -62 -142.5 -103t-187.5 -41q-162 0 -253 83t-91 218zM358 315q0 -122 131 -122q100 0 154.5 55t54.5 135v127q-65 -25 -168 -47q-172 -39 -172 -148z" />
<glyph unicode="b" horiz-adv-x="1204" d="M80 0v1464h328v-520h4q38 51 119 94t184 43q210 0 333 -150t123 -407t-123 -407t-333 -150q-103 0 -184 43t-119 94h-4v-104h-328zM414 524q0 -151 57 -231t158 -80t158 80t57 231q0 152 -57 232t-158 80t-158 -80t-57 -232z" />
<glyph unicode="c" horiz-adv-x="1105" d="M33 524q0 247 145.5 402t386.5 155q222 0 357.5 -117.5t156.5 -306.5h-321q-4 34 -15.5 64t-32.5 57t-56.5 42.5t-82.5 15.5q-101 0 -156 -82t-55 -230q0 -149 54.5 -230t156.5 -81q91 0 134.5 51t52.5 131h321q-12 -190 -148 -309t-358 -119q-247 0 -393.5 154 t-146.5 403z" />
<glyph unicode="d" horiz-adv-x="1204" d="M33 524q0 257 123 407t333 150q103 0 184.5 -43t119.5 -94h4v520h327v-1464h-327v104h-4q-38 -51 -119.5 -94t-184.5 -43q-210 0 -333 150t-123 407zM360 524q0 -151 57 -231t158 -80t158.5 80t57.5 231t-57.5 231.5t-158.5 80.5t-158 -80t-57 -232z" />
<glyph unicode="e" horiz-adv-x="1146" d="M33 524q0 244 147 400.5t393 156.5q175 0 298.5 -82t183 -225t59.5 -336h-754q0 -105 66.5 -165t159.5 -60q78 0 123.5 30t62.5 74h326q-44 -164 -171.5 -257t-334.5 -93q-258 0 -408.5 154t-150.5 403zM360 637h426q0 106 -58 162.5t-155 56.5t-155 -56.5t-58 -162.5z " />
<glyph unicode="f" horiz-adv-x="714" d="M23 803v246h155v71q0 187 67 275t240 88q100 0 203 -21v-245h-43.5t-35 -1t-30.5 -3.5t-23.5 -6.5t-20 -10.5t-13 -15t-10 -21t-4.5 -28.5t-2 -37v-45h182v-246h-182v-803h-328v803h-155z" />
<glyph unicode="g" horiz-adv-x="1171" d="M33 565q0 229 120.5 372.5t319.5 143.5q103 0 175 -41.5t112 -95.5h4v105h328v-947q0 -127 -35.5 -218t-104.5 -144t-161.5 -77.5t-215.5 -24.5q-96 0 -177.5 19.5t-139 51.5t-101 76.5t-68 92t-36.5 99.5h328q36 -104 186 -104q108 0 152.5 59.5t44.5 192.5v61h-4 q-40 -54 -112 -95.5t-175 -41.5q-199 0 -319.5 143.5t-120.5 372.5zM360 565q0 -129 50.5 -199.5t148.5 -70.5t148.5 70.5t50.5 199.5q0 130 -50.5 200.5t-148.5 70.5t-148.5 -70.5t-50.5 -200.5z" />
<glyph unicode="h" horiz-adv-x="1167" d="M80 0v1464h328v-567h4q46 81 127 132.5t198 51.5q168 0 263.5 -97t95.5 -275v-709h-328v604q0 117 -39.5 164t-130.5 47q-83 0 -136.5 -53t-53.5 -156v-606h-328z" />
<glyph unicode="i" horiz-adv-x="487" d="M80 0v1049h328v-1049h-328zM80 1319q0 68 46 106.5t118 38.5t118 -38.5t46 -106.5q0 -69 -46 -107t-118 -38t-118 38t-46 107z" />
<glyph unicode="j" horiz-adv-x="493" d="M-96 -94h48t38.5 1.5t32.5 4.5t23.5 9t19 14t11 21t8 29.5t1.5 39.5v1024h328v-1049q0 -186 -67.5 -274t-240.5 -88q-94 0 -202 22v246zM86 1319q0 68 46 106.5t118 38.5t118 -38.5t46 -106.5q0 -69 -46 -107t-118 -38t-118 38t-46 107z" />
<glyph unicode="k" horiz-adv-x="1146" d="M80 0v1464h328v-770l307 355h391l-371 -394l414 -655h-389l-248 418l-104 -109v-309h-328z" />
<glyph unicode="l" horiz-adv-x="491" d="M82 0v1464h328v-1464h-328z" />
<glyph unicode="m" horiz-adv-x="1777" d="M80 0v1049h328v-152h4q45 79 130.5 131.5t196.5 52.5q100 0 171.5 -50t103.5 -134h4q52 60 94.5 96t109.5 62t148 26q159 0 247.5 -96.5t88.5 -275.5v-709h-332v604q0 120 -31 165.5t-118 45.5q-81 0 -125.5 -51.5t-44.5 -157.5v-606h-328v604q0 120 -31 165.5t-118 45.5 q-80 0 -125 -52t-45 -157v-606h-328z" />
<glyph unicode="n" horiz-adv-x="1167" d="M80 0v1049h328v-152h4q105 184 344 184q155 0 247.5 -97.5t92.5 -274.5v-709h-328v604q0 115 -39.5 163t-124.5 48q-196 0 -196 -209v-606h-328z" />
<glyph unicode="o" horiz-adv-x="1185" d="M33 524q0 -248 151 -402.5t410 -154.5q257 0 408 154.5t151 402.5t-151 402.5t-408 154.5q-259 0 -410 -154.5t-151 -402.5zM360 524q0 150 62 231t172 81q109 0 170 -81t61 -231t-61 -230.5t-170 -80.5q-110 0 -172 80.5t-62 230.5z" />
<glyph unicode="p" horiz-adv-x="1212" d="M80 -362v1411h328v-105h4q38 51 119 94t184 43q210 0 333 -150t123 -407t-123 -407t-333 -150q-103 0 -184 43t-119 94h-4v-466h-328zM414 524q0 -151 57 -231t158 -80t158 80t57 231q0 152 -57 232t-158 80t-158 -80t-57 -232z" />
<glyph unicode="q" horiz-adv-x="1212" d="M41 524q0 257 123.5 407t333.5 150q103 0 184 -43t119 -94h4v105h328v-1411h-328v466h-4q-38 -51 -119 -94t-184 -43q-210 0 -333.5 150t-123.5 407zM369 524q0 -151 57 -231t158 -80t158 80t57 231q0 152 -57 232t-158 80t-158 -80t-57 -232z" />
<glyph unicode="r" horiz-adv-x="784" d="M80 0v1049h328v-172h4q39 89 105 139.5t151 50.5q53 0 90 -8v-287h-4q-31 6 -62 6q-32 0 -63 -6q-61 -12 -110.5 -46.5t-80 -96t-30.5 -142.5v-487h-328z" />
<glyph unicode="s" horiz-adv-x="1032" d="M16 348h308q15 -155 208 -155q69 0 106.5 23t37.5 67q0 39 -30.5 64t-66 35.5t-122.5 29.5q-10 2 -15.5 3.5t-14 3t-15.5 3.5q-62 14 -108.5 27.5t-99.5 40t-87 58.5t-57 83t-23 114q0 147 122 241.5t337 94.5q121 0 214 -29t147.5 -79.5t83 -112t32.5 -133.5h-307 q-8 65 -46 97t-116 32q-68 0 -103.5 -22.5t-35.5 -61.5q0 -12 2 -22t8.5 -18t12 -13.5t19 -11t24 -9.5t31 -8.5t36 -8t44.5 -9t50 -10.5q71 -16 123 -31.5t110 -43.5t94.5 -63.5t60.5 -90.5t24 -124q0 -165 -120 -253.5t-339 -88.5q-137 0 -239 29t-163 82t-92 120t-35 150z " />
<glyph unicode="t" horiz-adv-x="718" d="M23 803v246h145v292h328v-292h182v-246h-182v-434q0 -23 1.5 -39.5t8 -29.5t11 -21t19 -14t23.5 -9t32.5 -4.5t38.5 -1.5h48v-246q-108 -22 -203 -22q-173 0 -240 87.5t-67 274.5v459h-145z" />
<glyph unicode="u" horiz-adv-x="1167" d="M72 340v709h327v-605q0 -115 39.5 -163t124.5 -48q197 0 197 209v607h327v-1049h-327v152h-4q-106 -185 -344 -185q-155 0 -247.5 98t-92.5 275z" />
<glyph unicode="v" horiz-adv-x="1073" d="M-6 1049h328l213 -684h4l213 684h327l-362 -1049h-361z" />
<glyph unicode="w" horiz-adv-x="1603" d="M-4 1049h328l157 -639h4l172 639h287l172 -639h4l158 639h328l-293 -1049h-307l-203 664h-4l-203 -664h-307z" />
<glyph unicode="x" horiz-adv-x="1146" d="M14 0l361 535l-320 514h348l168 -304h4l168 304h349l-320 -514l361 -535h-369l-189 324h-4l-188 -324h-369z" />
<glyph unicode="y" horiz-adv-x="1083" d="M-6 1049h328l237 -689h4l199 689h328l-357 -1069q-67 -199 -154 -270.5t-255 -71.5q-86 0 -183 20v246h4q45 -10 81 -10q83 0 118 53q19 29 19 74q0 54 -27 131z" />
<glyph unicode="z" horiz-adv-x="1034" d="M49 0v236l510 563v4h-469v246h879v-236l-510 -563v-4h530v-246h-940z" />
<glyph unicode="{" horiz-adv-x="815" d="M51 512v283q83 -1 111 26.5t28 106.5v252q0 85 14 148.5t44.5 107t70 71.5t100 43.5t123.5 21t150 5.5h56v-281h-56q-153 0 -153 -190v-232q0 -87 -34 -137.5t-110 -81.5v-4q76 -31 110 -82t34 -139v-231q0 -199 153 -199h56v-281h-56q-86 0 -150 6.5t-123.5 22.5 t-99.5 45t-70.5 73.5t-44.5 109t-14 149.5v252q0 79 -27.5 107t-111.5 28z" />
<glyph unicode="|" horiz-adv-x="460" d="M119 -283v1827h225v-1827h-225z" />
<glyph unicode="}" horiz-adv-x="817" d="M70 0h55q154 0 154 199v231q0 88 33.5 139t109.5 82v4q-76 31 -109.5 81.5t-33.5 137.5v232q0 190 -154 190h-55v281h55q87 0 150 -5.5t123.5 -21t100 -43.5t70 -71.5t44.5 -107t14 -148.5v-252q0 -79 28 -106.5t111 -26.5v-283q-84 0 -111.5 -28t-27.5 -107v-252 q0 -85 -14 -149.5t-44.5 -109t-70.5 -73.5t-99.5 -45t-123.5 -22.5t-150 -6.5h-55v281z" />
<glyph unicode="~" horiz-adv-x="907" d="M49 489q0 182 60 270.5t184 88.5q59 0 112 -23t97 -46t82 -23q84 0 84 92h204q0 -182 -59.5 -270.5t-183.5 -88.5q-59 0 -112 23.5t-97 46.5t-82 23q-84 0 -84 -93h-205z" />
<glyph unicode="&#xa1;" horiz-adv-x="540" d="M96 53l92 666h164l92 -666v-319h-348v319zM96 1042q0 78 49.5 126.5t124.5 48.5q74 0 124 -48.5t50 -126.5q0 -77 -50.5 -125.5t-123.5 -48.5q-75 0 -124.5 48.5t-49.5 125.5z" />
<glyph unicode="&#xa2;" horiz-adv-x="1114" d="M33 524q0 230 129 382.5t346 172.5v138h123v-138q194 -18 311.5 -133t136.5 -289h-321q-16 140 -127 170v-608q112 33 127 176h321q-11 -176 -130.5 -292.5t-317.5 -133.5v-188h-123v190q-218 20 -346.5 171.5t-128.5 381.5zM360 524q0 -123 38 -200.5t110 -100.5v602 q-71 -23 -109.5 -101t-38.5 -200z" />
<glyph unicode="&#xa3;" horiz-adv-x="1200" d="M47 252q85 55 135.5 138.5t50.5 174.5q0 19 -2 27h-174v184h99q-24 42 -38.5 72t-27 77t-12.5 95q0 60 14.5 115.5t52.5 111.5t96.5 97t153.5 66.5t217 25.5q146 0 255.5 -38t176 -108.5t98.5 -165.5t32 -215h-349q0 246 -207 246q-101 0 -146.5 -46.5t-45.5 -123.5 q0 -54 19 -101t55 -108h305v-184h-225q2 -12 2 -37q0 -63 -21 -112t-53 -81.5t-88 -72.5l2 -4q84 41 149 41q52 0 92.5 -19.5t79 -39t84.5 -19.5q47 0 91.5 17t65 31.5t58.5 45.5l121 -248q-89 -69 -154.5 -98t-162.5 -29q-57 0 -104 13.5t-78 32.5t-59 37.5t-61.5 32 t-70.5 13.5q-63 0 -120.5 -22t-139.5 -74z" />
<glyph unicode="&#xa4;" horiz-adv-x="1048" d="M29 393l123 123q-43 94 -43 199q0 104 43 198l-123 123l174 174l123 -123q90 46 200 46q114 0 199 -43l121 120l174 -174l-121 -121q43 -87 43 -200q0 -111 -45 -199l123 -123l-174 -174l-121 121q-94 -43 -199 -43q-112 0 -200 45l-123 -123zM354 715q0 -106 41.5 -170 t130.5 -64q87 0 128.5 64t41.5 170t-41.5 169.5t-128.5 63.5q-89 0 -130.5 -63.5t-41.5 -169.5z" />
<glyph unicode="&#xa5;" horiz-adv-x="1323" d="M2 1405h389l264 -555h4l265 555h389l-434 -778h290v-123h-338v-119h338v-123h-338v-262h-348v262h-338v123h338v119h-338v123h291z" />
<glyph unicode="&#xa6;" horiz-adv-x="460" d="M119 -283v785h225v-785h-225zM119 786v758h225v-758h-225z" />
<glyph unicode="&#xa7;" horiz-adv-x="1280" d="M59 586q0 104 53.5 183t153.5 122q-106 99 -106 250q0 162 120.5 259t337.5 97q124 0 218.5 -29t151 -81.5t85.5 -117.5t33 -143h-328q-8 78 -44.5 112t-123.5 34q-58 0 -90.5 -24.5t-32.5 -73.5q0 -29 14.5 -55.5t33.5 -45t57.5 -41t65.5 -35.5t77 -36 q36 -16 96.5 -42.5t94 -41t82 -39t75.5 -43.5t59 -47t48 -55.5t27.5 -64.5t11.5 -79q0 -104 -53.5 -183t-153.5 -122q106 -99 106 -250q0 -162 -120.5 -259t-337.5 -97q-124 0 -218.5 29t-151 81t-85.5 117t-33 143h328q8 -78 44.5 -111.5t123.5 -33.5q58 0 90.5 24.5 t32.5 73.5q0 29 -14.5 55.5t-33.5 45t-57.5 41t-65.5 35.5t-77 36q-36 16 -96.5 42.5t-94 41t-82 39t-75.5 43.5t-59 47t-48 55.5t-27.5 64.5t-11.5 79zM387 666q0 -22 7.5 -41.5t26 -37.5t36.5 -32t53.5 -33t62.5 -31.5t77 -36t83 -40.5l62 -31q106 9 106 86 q0 22 -7.5 41.5t-26 37.5t-36.5 32t-53.5 33t-62.5 31.5t-77 36t-83 40.5l-61 31q-107 -9 -107 -86z" />
<glyph unicode="&#xa8;" horiz-adv-x="1003" d="M125 1331q0 66 43 111t115 45t113.5 -45t41.5 -111t-41.5 -110.5t-113.5 -44.5q-74 0 -116 44.5t-42 110.5zM565 1331q0 66 43 111t115 45t114 -45t42 -111t-42 -110.5t-114 -44.5q-74 0 -116 44.5t-42 110.5z" />
<glyph unicode="&#xa9;" horiz-adv-x="1673" d="M82 731q0 163 53.5 304t150.5 243.5t239.5 160.5t312.5 58t312.5 -58t238.5 -160.5t149 -243t53 -304.5q0 -163 -53 -304t-149.5 -242.5t-238.5 -159.5t-312 -58t-312.5 58t-239.5 159.5t-150.5 242.5t-53.5 304zM205 731q0 -189 73.5 -337.5t218.5 -236t341 -87.5 q146 0 266 50.5t199 139.5t122 209.5t43 261.5q0 142 -43 263t-122 210.5t-199 140t-266 50.5q-196 0 -341 -88t-218.5 -237t-73.5 -339zM403 731q0 204 117 332.5t314 128.5q183 0 291 -96.5t124 -253.5h-264q-17 147 -145 147q-166 0 -166 -258q0 -256 166 -256 q75 0 106.5 40.5t38.5 109.5h264q-9 -158 -113.5 -255.5t-285.5 -97.5q-204 0 -325.5 127t-121.5 332z" />
<glyph unicode="&#xab;" horiz-adv-x="829" d="M61 362v308l316 295v-308l-160 -139v-4l160 -139v-307zM453 362v308l315 295v-308l-160 -139v-4l160 -139v-307z" />
<glyph unicode="&#xac;" horiz-adv-x="1073" d="M82 600v281h879v-607h-328v326h-551z" />
<glyph unicode="&#xad;" horiz-adv-x="686" d="M66 377v280h557v-280h-557z" />
<glyph unicode="&#xae;" horiz-adv-x="1087" d="M53 1001q0 212 142.5 354t349.5 142q135 0 247 -64t177 -178t65 -254q0 -212 -141 -353.5t-348 -141.5t-349.5 141.5t-142.5 353.5zM176 1004q0 -181 99.5 -288.5t269.5 -107.5q169 0 267.5 107.5t98.5 288.5q0 180 -98.5 286.5t-267.5 106.5q-170 0 -269.5 -107 t-99.5 -286zM330 737v533h252q89 0 138.5 -40t49.5 -114q0 -53 -28 -86.5t-78 -44.5v-2q45 -8 69 -46t26.5 -79t6 -76t12.5 -37v-8h-139q-8 7 -10 38.5t-3 63.5t-20.5 58.5t-58.5 26.5h-84v-187h-133zM463 1032h92q82 0 82 64q0 65 -82 65h-92v-129z" />
<glyph unicode="&#xaf;" horiz-adv-x="1024" d="M225 1229v205h586v-205h-586z" />
<glyph unicode="&#xb0;" horiz-adv-x="768" d="M66 1184q0 131 87 222t232 91q143 0 231.5 -91.5t88.5 -221.5q0 -131 -88.5 -223.5t-231.5 -92.5q-144 0 -231.5 92.5t-87.5 223.5zM250 1184q0 -76 40.5 -114t94.5 -38t94.5 38t40.5 114q0 75 -40.5 112t-94.5 37t-94.5 -37t-40.5 -112z" />
<glyph unicode="&#xb1;" horiz-adv-x="1159" d="M82 0v246h995v-246h-995zM82 625v280h356v281h283v-281h356v-280h-356v-281h-283v281h-356z" />
<glyph unicode="&#xb4;" horiz-adv-x="978" d="M350 1188l88 287h369l-211 -287h-246z" />
<glyph unicode="&#xb6;" horiz-adv-x="1234" d="M55 1069q0 186 111.5 290.5t316.5 104.5h641v-1464h-204v1280h-146v-1280h-205v686h-86q-207 0 -317.5 99t-110.5 284z" />
<glyph unicode="&#xb7;" horiz-adv-x="479" d="M66 635q0 77 49.5 125.5t124.5 48.5q73 0 123.5 -48.5t50.5 -125.5q0 -78 -50 -126t-124 -48q-75 0 -124.5 48t-49.5 126z" />
<glyph unicode="&#xb8;" horiz-adv-x="1036" d="M236 -346l61 72q67 -31 141 -31q33 0 61.5 15t28.5 44q0 28 -21.5 42t-55.5 14q-29 0 -72 -15l-35 49l117 136h121l-78 -86v-5q94 10 161.5 -26t67.5 -111q0 -71 -77.5 -114.5t-190.5 -43.5q-131 0 -229 60z" />
<glyph unicode="&#xbb;" horiz-adv-x="829" d="M61 68v307l160 139v4l-160 139v308l316 -295v-308zM453 68v307l159 139v4l-159 139v308l315 -295v-308z" />
<glyph unicode="&#xbf;" horiz-adv-x="1120" d="M35 102q0 119 55.5 200t167.5 144q115 64 156 117t41 160h266q0 -23 0.5 -62t0.5 -57t-1.5 -48.5t-5.5 -47t-12.5 -41t-21 -42t-31.5 -37.5t-44 -40q-29 -22 -79 -53.5t-75.5 -49.5t-47 -51.5t-21.5 -74.5q0 -55 41.5 -94t120.5 -39q99 0 151 53.5t52 153.5h348 q0 -103 -36 -190.5t-105.5 -154.5t-180 -105t-250.5 -38q-215 0 -352 110t-137 287zM412 1047q0 78 50 126t124 48q75 0 124.5 -48t49.5 -126q0 -77 -50 -126t-124 -49q-73 0 -123.5 49t-50.5 126z" />
<glyph unicode="&#xc0;" d="M-2 0l530 1464h369l528 -1464h-368l-94 305h-502l-94 -305h-369zM381 1890h369l88 -286h-246zM545 586h336l-166 540h-4z" />
<glyph unicode="&#xc1;" d="M-2 0l530 1464h369l528 -1464h-368l-94 305h-502l-94 -305h-369zM545 586h336l-166 540h-4zM586 1604l88 286h368l-211 -286h-245z" />
<glyph unicode="&#xc2;" d="M-2 0l530 1464h369l528 -1464h-368l-94 305h-502l-94 -305h-369zM334 1604l219 286h319l220 -286h-285l-92 141h-4l-93 -141h-284zM545 586h336l-166 540h-4z" />
<glyph unicode="&#xc3;" d="M-2 0l530 1464h369l528 -1464h-368l-94 305h-502l-94 -305h-369zM373 1632q0 146 45 211.5t135 65.5q49 0 137 -39t125 -39q68 0 68 78h170q0 -147 -45 -212t-136 -65q-49 0 -137 38t-125 38q-67 0 -67 -76h-170zM545 586h336l-166 540h-4z" />
<glyph unicode="&#xc4;" d="M-2 0l530 1464h369l528 -1464h-368l-94 305h-502l-94 -305h-369zM336 1747q0 66 43 111t115 45t113.5 -45t41.5 -111t-41.5 -111t-113.5 -45q-74 0 -116 45t-42 111zM545 586h336l-166 540h-4zM776 1747q0 66 43 111t115 45t114 -45t42 -111t-42 -111t-114 -45 q-74 0 -116 45t-42 111z" />
<glyph unicode="&#xc5;" d="M-2 0l530 1464h369l528 -1464h-368l-94 305h-502l-94 -305h-369zM504 1786q0 91 61 148t148 57q88 0 148.5 -56.5t60.5 -148.5q0 -93 -60 -149t-149 -56q-88 0 -148.5 56.5t-60.5 148.5zM545 586h336l-166 540h-4zM629 1786q0 -43 23 -67.5t61 -24.5q39 0 62.5 24.5 t23.5 67.5t-23.5 67.5t-62.5 24.5q-38 0 -61 -24.5t-23 -67.5z" />
<glyph unicode="&#xc6;" horiz-adv-x="2064" d="M-2 -4l553 1468h1442v-301h-742v-260h639v-301h-639v-301h754v-301h-1122v301h-422l-94 -305h-369zM545 582h338v581h-150z" />
<glyph unicode="&#xc7;" d="M41 733q0 166 46.5 305t134.5 241.5t224 160t306 57.5q147 0 264 -42t196.5 -119t126.5 -182t60 -234h-369q-22 132 -90 204t-205 72q-89 0 -153 -33t-101 -96.5t-54 -145t-17 -188.5t17 -189.5t54 -146t101 -96.5t153 -33q139 0 207 73.5t88 207.5h369 q-24 -252 -172.5 -407t-409.5 -173l-69 -75v-5q94 10 161.5 -26t67.5 -111q0 -71 -77.5 -114.5t-190.5 -43.5q-132 0 -230 60l62 72q67 -31 141 -31q33 0 61.5 15t28.5 44q0 28 -22 42t-56 14q-28 0 -71 -15l-35 49l108 125q-209 12 -358.5 114.5t-223 269t-73.5 380.5z" />
<glyph unicode="&#xc8;" horiz-adv-x="1286" d="M100 0v1464h1110v-301h-741v-260h639v-301h-639v-301h754v-301h-1123zM330 1890h368l88 -286h-245z" />
<glyph unicode="&#xc9;" horiz-adv-x="1286" d="M100 0v1464h1110v-301h-741v-260h639v-301h-639v-301h754v-301h-1123zM535 1604l88 286h368l-211 -286h-245z" />
<glyph unicode="&#xca;" horiz-adv-x="1286" d="M100 0v1464h1110v-301h-741v-260h639v-301h-639v-301h754v-301h-1123zM283 1604l219 286h319l219 -286h-284l-92 141h-5l-92 -141h-284z" />
<glyph unicode="&#xcb;" horiz-adv-x="1286" d="M100 0v1464h1110v-301h-741v-260h639v-301h-639v-301h754v-301h-1123zM285 1747q0 66 42.5 111t114.5 45t114 -45t42 -111t-42 -111t-114 -45q-74 0 -115.5 45t-41.5 111zM725 1747q0 66 43 111t115 45t113.5 -45t41.5 -111t-41.5 -111t-113.5 -45q-74 0 -116 45t-42 111 z" />
<glyph unicode="&#xcc;" horiz-adv-x="571" d="M-45 1890h369l88 -286h-246zM102 0v1464h369v-1464h-369z" />
<glyph unicode="&#xcd;" horiz-adv-x="571" d="M102 0v1464h369v-1464h-369zM160 1604l88 286h368l-210 -286h-246z" />
<glyph unicode="&#xce;" horiz-adv-x="571" d="M-92 1604l219 286h319l220 -286h-285l-92 141h-4l-92 -141h-285zM102 0v1464h369v-1464h-369z" />
<glyph unicode="&#xcf;" horiz-adv-x="571" d="M-90 1747q0 66 43 111t115 45t113.5 -45t41.5 -111t-41.5 -111t-113.5 -45q-74 0 -116 45t-42 111zM102 0v1464h369v-1464h-369zM350 1747q0 66 43 111t115 45t114 -45t42 -111t-42 -111t-114 -45q-74 0 -116 45t-42 111z" />
<glyph unicode="&#xd0;" horiz-adv-x="1462" d="M6 666v184h113v614h649q308 0 480.5 -191t172.5 -542q0 -353 -163.5 -542t-469.5 -189h-669v666h-113zM487 301h244q322 0 322 430q0 432 -322 432h-244v-313h293v-184h-293v-365z" />
<glyph unicode="&#xd1;" horiz-adv-x="1454" d="M100 0v1464h369l512 -870h4v870h369v-1464h-369l-512 870h-4v-870h-369zM387 1632q0 146 45 211.5t135 65.5q49 0 137 -39t125 -39q68 0 68 78h170q0 -147 -45 -212t-135 -65q-49 0 -137 38t-125 38q-68 0 -68 -76h-170z" />
<glyph unicode="&#xd2;" horiz-adv-x="1531" d="M41 733q0 222 83 393t249 271t393 100q225 0 391 -99.5t250 -271.5t84 -393q0 -166 -49 -306.5t-140 -242.5t-228.5 -159.5t-307.5 -57.5q-171 0 -309 57.5t-228.5 160t-139 242.5t-48.5 306zM410 733q0 -100 18.5 -181t58.5 -146.5t111 -101.5t168 -36t167.5 36 t111 101.5t59 146.5t18.5 181q0 99 -18.5 180t-59 146t-111 101t-167.5 36t-168 -36t-111 -101t-58.5 -145.5t-18.5 -180.5zM434 1890h369l88 -286h-246z" />
<glyph unicode="&#xd3;" horiz-adv-x="1531" d="M41 733q0 222 83 393t249 271t393 100q225 0 391 -99.5t250 -271.5t84 -393q0 -166 -49 -306.5t-140 -242.5t-228.5 -159.5t-307.5 -57.5q-171 0 -309 57.5t-228.5 160t-139 242.5t-48.5 306zM410 733q0 -100 18.5 -181t58.5 -146.5t111 -101.5t168 -36t167.5 36 t111 101.5t59 146.5t18.5 181q0 99 -18.5 180t-59 146t-111 101t-167.5 36t-168 -36t-111 -101t-58.5 -145.5t-18.5 -180.5zM639 1604l88 286h369l-211 -286h-246z" />
<glyph unicode="&#xd4;" horiz-adv-x="1531" d="M41 733q0 222 83 393t249 271t393 100q225 0 391 -99.5t250 -271.5t84 -393q0 -166 -49 -306.5t-140 -242.5t-228.5 -159.5t-307.5 -57.5q-171 0 -309 57.5t-228.5 160t-139 242.5t-48.5 306zM387 1604l219 286h320l219 -286h-285l-92 141h-4l-92 -141h-285zM410 733 q0 -100 18.5 -181t58.5 -146.5t111 -101.5t168 -36t167.5 36t111 101.5t59 146.5t18.5 181q0 99 -18.5 180t-59 146t-111 101t-167.5 36t-168 -36t-111 -101t-58.5 -145.5t-18.5 -180.5z" />
<glyph unicode="&#xd5;" horiz-adv-x="1531" d="M41 733q0 222 83 393t249 271t393 100q225 0 391 -99.5t250 -271.5t84 -393q0 -166 -49 -306.5t-140 -242.5t-228.5 -159.5t-307.5 -57.5q-171 0 -309 57.5t-228.5 160t-139 242.5t-48.5 306zM410 733q0 -100 18.5 -181t58.5 -146.5t111 -101.5t168 -36t167.5 36 t111 101.5t59 146.5t18.5 181q0 99 -18.5 180t-59 146t-111 101t-167.5 36t-168 -36t-111 -101t-58.5 -145.5t-18.5 -180.5zM426 1632q0 146 45 211.5t135 65.5q49 0 137 -39t125 -39q68 0 68 78h170q0 -147 -45 -212t-135 -65q-49 0 -137 38t-125 38q-68 0 -68 -76h-170z " />
<glyph unicode="&#xd6;" horiz-adv-x="1531" d="M41 733q0 222 83 393t249 271t393 100q225 0 391 -99.5t250 -271.5t84 -393q0 -166 -49 -306.5t-140 -242.5t-228.5 -159.5t-307.5 -57.5q-171 0 -309 57.5t-228.5 160t-139 242.5t-48.5 306zM389 1747q0 66 43 111t115 45t113.5 -45t41.5 -111t-41.5 -111t-113.5 -45 q-74 0 -116 45t-42 111zM410 733q0 -100 18.5 -181t58.5 -146.5t111 -101.5t168 -36t167.5 36t111 101.5t59 146.5t18.5 181q0 99 -18.5 180t-59 146t-111 101t-167.5 36t-168 -36t-111 -101t-58.5 -145.5t-18.5 -180.5zM829 1747q0 66 43 111t115 45t114 -45t42 -111 t-42 -111t-114 -45q-74 0 -116 45t-42 111z" />
<glyph unicode="&#xd7;" horiz-adv-x="1159" d="M80 379l295 295l-295 293l199 198l294 -295l295 295l199 -198l-295 -293l295 -295l-199 -199l-295 295l-294 -295z" />
<glyph unicode="&#xd8;" horiz-adv-x="1540" d="M41 90l147 150q-143 196 -143 493q0 222 83 393t249 271t393 100q305 0 496 -178l145 145l88 -86l-151 -151q147 -201 147 -494q0 -166 -49 -306.5t-140 -242.5t-228.5 -159.5t-307.5 -57.5q-309 0 -500 178l-143 -143zM414 733q0 -138 32 -235l584 585q-89 113 -260 113 q-97 0 -168 -36t-111 -101t-58.5 -145.5t-18.5 -180.5zM506 383q91 -115 264 -115q97 0 167.5 36t111 101.5t59 146.5t18.5 181q0 143 -34 238z" />
<glyph unicode="&#xd9;" horiz-adv-x="1404" d="M90 528v936h369v-936q0 -139 59 -199.5t184 -60.5t184.5 60.5t59.5 199.5v936h369v-936q0 -281 -144.5 -421t-468.5 -140q-323 0 -467.5 139.5t-144.5 421.5zM371 1890h368l88 -286h-245z" />
<glyph unicode="&#xda;" horiz-adv-x="1404" d="M90 528v936h369v-936q0 -139 59 -199.5t184 -60.5t184.5 60.5t59.5 199.5v936h369v-936q0 -281 -144.5 -421t-468.5 -140q-323 0 -467.5 139.5t-144.5 421.5zM575 1604l89 286h368l-211 -286h-246z" />
<glyph unicode="&#xdb;" horiz-adv-x="1404" d="M90 528v936h369v-936q0 -139 59 -199.5t184 -60.5t184.5 60.5t59.5 199.5v936h369v-936q0 -281 -144.5 -421t-468.5 -140q-323 0 -467.5 139.5t-144.5 421.5zM324 1604l219 286h319l219 -286h-284l-92 141h-5l-92 -141h-284z" />
<glyph unicode="&#xdc;" horiz-adv-x="1404" d="M90 528v936h369v-936q0 -139 59 -199.5t184 -60.5t184.5 60.5t59.5 199.5v936h369v-936q0 -281 -144.5 -421t-468.5 -140q-323 0 -467.5 139.5t-144.5 421.5zM326 1747q0 66 42.5 111t114.5 45t114 -45t42 -111t-42 -111t-114 -45q-74 0 -115.5 45t-41.5 111zM766 1747 q0 66 43 111t115 45t113.5 -45t41.5 -111t-41.5 -111t-113.5 -45q-74 0 -116 45t-42 111z" />
<glyph unicode="&#xdd;" horiz-adv-x="1357" d="M2 1464h389l285 -602h4l287 602h389l-494 -923v-541h-368v541zM553 1604l88 286h369l-211 -286h-246z" />
<glyph unicode="&#xde;" horiz-adv-x="1331" d="M100 0v1464h369v-204h342q233 0 364.5 -120t131.5 -347t-131.5 -347t-364.5 -120h-342v-326h-369zM469 627h264q100 0 152.5 38.5t52.5 127.5q0 88 -52.5 126.5t-152.5 38.5h-264v-331z" />
<glyph unicode="&#xdf;" horiz-adv-x="1179" d="M80 0v961q0 244 126.5 390t373.5 146q138 0 250.5 -55.5t173.5 -146.5t61 -195q0 -213 -184 -289v-4q125 -40 195.5 -156t70.5 -254q0 -102 -35.5 -184.5t-96 -135.5t-138.5 -81.5t-166 -28.5q-111 0 -201 33v246q309 -57 309 213q0 110 -63.5 176.5t-194.5 66.5h-51v205 h43q83 0 133.5 48t50.5 128q0 78 -42 123t-115 45q-172 0 -172 -209v-1042h-328z" />
<glyph unicode="&#xe0;" horiz-adv-x="1132" d="M31 268q0 150 101.5 233t297.5 122q145 29 187 45q80 34 81 90q0 98 -133 98q-89 0 -133.5 -35t-52.5 -110h-307q4 78 34 143t88 117t153.5 81t219.5 29q230 0 344.5 -102.5t114.5 -341.5v-481q0 -78 61 -78v-78q-87 -33 -190 -33q-102 0 -145 40.5t-43 103.5h-4 q-54 -62 -142.5 -103t-187.5 -41q-162 0 -253 83t-91 218zM236 1475h368l88 -287h-246zM358 315q0 -122 131 -122q100 0 154.5 55t54.5 135v127q-65 -25 -168 -47q-172 -39 -172 -148z" />
<glyph unicode="&#xe1;" horiz-adv-x="1132" d="M31 268q0 150 101.5 233t297.5 122q145 29 187 45q80 34 81 90q0 98 -133 98q-89 0 -133.5 -35t-52.5 -110h-307q4 78 34 143t88 117t153.5 81t219.5 29q230 0 344.5 -102.5t114.5 -341.5v-481q0 -78 61 -78v-78q-87 -33 -190 -33q-102 0 -145 40.5t-43 103.5h-4 q-54 -62 -142.5 -103t-187.5 -41q-162 0 -253 83t-91 218zM358 315q0 -122 131 -122q100 0 154.5 55t54.5 135v127q-65 -25 -168 -47q-172 -39 -172 -148zM440 1188l88 287h369l-211 -287h-246z" />
<glyph unicode="&#xe2;" horiz-adv-x="1132" d="M31 268q0 150 101.5 233t297.5 122q145 29 187 45q80 34 81 90q0 98 -133 98q-89 0 -133.5 -35t-52.5 -110h-307q4 78 34 143t88 117t153.5 81t219.5 29q230 0 344.5 -102.5t114.5 -341.5v-481q0 -78 61 -78v-78q-87 -33 -190 -33q-102 0 -145 40.5t-43 103.5h-4 q-54 -62 -142.5 -103t-187.5 -41q-162 0 -253 83t-91 218zM188 1188l220 287h319l219 -287h-284l-93 141h-4l-92 -141h-285zM358 315q0 -122 131 -122q100 0 154.5 55t54.5 135v127q-65 -25 -168 -47q-172 -39 -172 -148z" />
<glyph unicode="&#xe3;" horiz-adv-x="1132" d="M31 268q0 150 101.5 233t297.5 122q145 29 187 45q80 34 81 90q0 98 -133 98q-89 0 -133.5 -35t-52.5 -110h-307q4 78 34 143t88 117t153.5 81t219.5 29q230 0 344.5 -102.5t114.5 -341.5v-481q0 -78 61 -78v-78q-87 -33 -190 -33q-102 0 -145 40.5t-43 103.5h-4 q-54 -62 -142.5 -103t-187.5 -41q-162 0 -253 83t-91 218zM227 1217q0 145 45.5 210.5t135.5 65.5q49 0 137 -39t125 -39q67 0 67 78h170q0 -147 -44.5 -211.5t-135.5 -64.5q-49 0 -137 37.5t-125 37.5q-68 0 -68 -75h-170zM358 315q0 -122 131 -122q100 0 154.5 55 t54.5 135v127q-65 -25 -168 -47q-172 -39 -172 -148z" />
<glyph unicode="&#xe4;" horiz-adv-x="1132" d="M31 268q0 150 101.5 233t297.5 122q145 29 187 45q80 34 81 90q0 98 -133 98q-89 0 -133.5 -35t-52.5 -110h-307q4 78 34 143t88 117t153.5 81t219.5 29q230 0 344.5 -102.5t114.5 -341.5v-481q0 -78 61 -78v-78q-87 -33 -190 -33q-102 0 -145 40.5t-43 103.5h-4 q-54 -62 -142.5 -103t-187.5 -41q-162 0 -253 83t-91 218zM190 1331q0 66 43 111t115 45t114 -45t42 -111t-42 -110.5t-114 -44.5q-74 0 -116 44.5t-42 110.5zM358 315q0 -122 131 -122q100 0 154.5 55t54.5 135v127q-65 -25 -168 -47q-172 -39 -172 -148zM631 1331 q0 66 42.5 111t114.5 45t114 -45t42 -111t-42 -110.5t-114 -44.5q-74 0 -115.5 44.5t-41.5 110.5z" />
<glyph unicode="&#xe5;" horiz-adv-x="1132" d="M31 268q0 150 101.5 233t297.5 122q145 29 187 45q80 34 81 90q0 98 -133 98q-89 0 -133.5 -35t-52.5 -110h-307q4 78 34 143t88 117t153.5 81t219.5 29q230 0 344.5 -102.5t114.5 -341.5v-481q0 -78 61 -78v-78q-87 -33 -190 -33q-102 0 -145 40.5t-43 103.5h-4 q-54 -62 -142.5 -103t-187.5 -41q-162 0 -253 83t-91 218zM358 315q0 -122 131 -122q100 0 154.5 55t54.5 135v127q-65 -25 -168 -47q-172 -39 -172 -148zM358 1370q0 91 61 148t148 57q88 0 148.5 -56.5t60.5 -148.5q0 -93 -60 -149t-149 -56q-88 0 -148.5 56.5 t-60.5 148.5zM483 1370q0 -43 23 -67.5t61 -24.5q39 0 62.5 24.5t23.5 67.5t-23.5 67.5t-62.5 24.5q-38 0 -61 -24.5t-23 -67.5z" />
<glyph unicode="&#xe6;" horiz-adv-x="1814" d="M31 268q0 150 101.5 233t297.5 122q145 29 187 45q80 34 81 90q0 98 -133 98q-89 0 -133.5 -35t-52.5 -110h-307q4 78 34 143t88 117t153.5 81t219.5 29q234 0 342 -100q140 100 330 100q175 0 298.5 -82t183 -225t59.5 -336h-754q0 -105 66 -165t159 -60q78 0 124 30 t63 74h325q-44 -164 -171.5 -257t-334.5 -93q-245 0 -399 144q-57 -50 -122 -82t-131 -44t-103 -15t-86 -3q-190 0 -287.5 82t-97.5 219zM358 315q0 -122 131 -122q100 0 154.5 55t54.5 135v127q-65 -25 -168 -47q-172 -39 -172 -148zM1026 637h426q0 106 -58 162.5 t-155 56.5t-155 -56.5t-58 -162.5z" />
<glyph unicode="&#xe7;" horiz-adv-x="1105" d="M33 524q0 247 145.5 402t386.5 155q222 0 357.5 -117.5t156.5 -306.5h-321q-4 34 -15.5 64t-32.5 57t-56.5 42.5t-82.5 15.5q-101 0 -156 -82t-55 -230q0 -149 54.5 -230t156.5 -81q91 0 134.5 51t52.5 131h321q-11 -176 -131.5 -293.5t-316.5 -132.5l-70 -75v-5 q94 10 162 -26t68 -111q0 -71 -78 -114.5t-191 -43.5q-131 0 -229 60l61 72q67 -31 142 -31q33 0 61.5 15t28.5 44q0 28 -22 42t-56 14q-29 0 -72 -15l-35 49l109 125q-220 22 -348.5 172.5t-128.5 382.5z" />
<glyph unicode="&#xe8;" horiz-adv-x="1146" d="M33 524q0 244 147 400.5t393 156.5q175 0 298.5 -82t183 -225t59.5 -336h-754q0 -105 66.5 -165t159.5 -60q78 0 123.5 30t62.5 74h326q-44 -164 -171.5 -257t-334.5 -93q-258 0 -408.5 154t-150.5 403zM242 1475h368l88 -287h-245zM360 637h426q0 106 -58 162.5 t-155 56.5t-155 -56.5t-58 -162.5z" />
<glyph unicode="&#xe9;" horiz-adv-x="1146" d="M33 524q0 244 147 400.5t393 156.5q175 0 298.5 -82t183 -225t59.5 -336h-754q0 -105 66.5 -165t159.5 -60q78 0 123.5 30t62.5 74h326q-44 -164 -171.5 -257t-334.5 -93q-258 0 -408.5 154t-150.5 403zM360 637h426q0 106 -58 162.5t-155 56.5t-155 -56.5t-58 -162.5z M446 1188l89 287h368l-211 -287h-246z" />
<glyph unicode="&#xea;" horiz-adv-x="1146" d="M33 524q0 244 147 400.5t393 156.5q175 0 298.5 -82t183 -225t59.5 -336h-754q0 -105 66.5 -165t159.5 -60q78 0 123.5 30t62.5 74h326q-44 -164 -171.5 -257t-334.5 -93q-258 0 -408.5 154t-150.5 403zM195 1188l219 287h319l219 -287h-284l-93 141h-4l-92 -141h-284z M360 637h426q0 106 -58 162.5t-155 56.5t-155 -56.5t-58 -162.5z" />
<glyph unicode="&#xeb;" horiz-adv-x="1146" d="M33 524q0 244 147 400.5t393 156.5q175 0 298.5 -82t183 -225t59.5 -336h-754q0 -105 66.5 -165t159.5 -60q78 0 123.5 30t62.5 74h326q-44 -164 -171.5 -257t-334.5 -93q-258 0 -408.5 154t-150.5 403zM197 1331q0 66 42.5 111t114.5 45t114 -45t42 -111t-42 -110.5 t-114 -44.5q-74 0 -115.5 44.5t-41.5 110.5zM360 637h426q0 106 -58 162.5t-155 56.5t-155 -56.5t-58 -162.5zM637 1331q0 66 43 111t115 45t113.5 -45t41.5 -111t-41.5 -110.5t-113.5 -44.5q-74 0 -116 44.5t-42 110.5z" />
<glyph unicode="&#xec;" horiz-adv-x="450" d="M-106 1475h368l88 -287h-246zM61 0v1049h328v-1049h-328z" />
<glyph unicode="&#xed;" horiz-adv-x="450" d="M61 0v1049h328v-1049h-328zM98 1188l88 287h369l-211 -287h-246z" />
<glyph unicode="&#xee;" horiz-adv-x="450" d="M-154 1188l220 287h319l219 -287h-285l-92 141h-4l-92 -141h-285zM61 0v1049h328v-1049h-328z" />
<glyph unicode="&#xef;" horiz-adv-x="450" d="M-152 1331q0 66 43 111t115 45t114 -45t42 -111t-42 -110.5t-114 -44.5q-74 0 -116 44.5t-42 110.5zM61 0v1049h328v-1049h-328zM289 1331q0 66 42.5 111t114.5 45t114 -45t42 -111t-42 -110.5t-114 -44.5q-74 0 -115.5 44.5t-41.5 110.5z" />
<glyph unicode="&#xf0;" horiz-adv-x="1185" d="M33 526q0 128 38 234t102.5 175.5t149 107.5t179.5 38q161 0 256 -88v4q-38 115 -135 220l-265 -72l-32 119l188 51q-94 74 -231 149h409q53 -26 109 -73l215 57l33 -119l-144 -39q107 -119 174.5 -281.5t67.5 -361.5q0 -108 -18.5 -204.5t-61.5 -185t-107 -152 t-161.5 -101t-218.5 -37.5q-259 0 -403 151.5t-144 407.5zM360 526q0 -145 59 -221t161 -76t160.5 76t58.5 221t-58.5 220t-160.5 75t-161 -75.5t-59 -219.5z" />
<glyph unicode="&#xf1;" horiz-adv-x="1167" d="M80 0v1049h328v-152h4q105 184 344 184q155 0 247.5 -97.5t92.5 -274.5v-709h-328v604q0 115 -39.5 163t-124.5 48q-196 0 -196 -209v-606h-328zM309 1217q0 146 45 211t135 65q49 0 137.5 -39t125.5 -39q67 0 67 78h170q0 -147 -44.5 -211.5t-135.5 -64.5 q-49 0 -137 37.5t-125 37.5q-68 0 -68 -75h-170z" />
<glyph unicode="&#xf2;" horiz-adv-x="1185" d="M33 524q0 -248 151 -402.5t410 -154.5q257 0 408 154.5t151 402.5t-151 402.5t-408 154.5q-259 0 -410 -154.5t-151 -402.5zM262 1475h369l88 -287h-246zM360 524q0 150 62 231t172 81q109 0 170 -81t61 -231t-61 -230.5t-170 -80.5q-110 0 -172 80.5t-62 230.5z" />
<glyph unicode="&#xf3;" horiz-adv-x="1185" d="M33 524q0 -248 151 -402.5t410 -154.5q257 0 408 154.5t151 402.5t-151 402.5t-408 154.5q-259 0 -410 -154.5t-151 -402.5zM360 524q0 150 62 231t172 81q109 0 170 -81t61 -231t-61 -230.5t-170 -80.5q-110 0 -172 80.5t-62 230.5zM467 1188l88 287h369l-211 -287h-246 z" />
<glyph unicode="&#xf4;" horiz-adv-x="1185" d="M33 524q0 -248 151 -402.5t410 -154.5q257 0 408 154.5t151 402.5t-151 402.5t-408 154.5q-259 0 -410 -154.5t-151 -402.5zM215 1188l219 287h320l219 -287h-285l-92 141h-4l-92 -141h-285zM360 524q0 150 62 231t172 81q109 0 170 -81t61 -231t-61 -230.5t-170 -80.5 q-110 0 -172 80.5t-62 230.5z" />
<glyph unicode="&#xf5;" horiz-adv-x="1185" d="M33 524q0 -248 151 -402.5t410 -154.5q257 0 408 154.5t151 402.5t-151 402.5t-408 154.5q-259 0 -410 -154.5t-151 -402.5zM254 1217q0 146 45 211t135 65q49 0 137 -39t125 -39q68 0 68 78h170q0 -147 -44.5 -211.5t-135.5 -64.5q-49 0 -137 37.5t-125 37.5 q-68 0 -68 -75h-170zM360 524q0 150 62 231t172 81q109 0 170 -81t61 -231t-61 -230.5t-170 -80.5q-110 0 -172 80.5t-62 230.5z" />
<glyph unicode="&#xf6;" horiz-adv-x="1185" d="M33 524q0 -248 151 -402.5t410 -154.5q257 0 408 154.5t151 402.5t-151 402.5t-408 154.5q-259 0 -410 -154.5t-151 -402.5zM217 1331q0 66 43 111t115 45t113.5 -45t41.5 -111t-41.5 -110.5t-113.5 -44.5q-74 0 -116 44.5t-42 110.5zM360 524q0 150 62 231t172 81 q109 0 170 -81t61 -231t-61 -230.5t-170 -80.5q-110 0 -172 80.5t-62 230.5zM657 1331q0 66 43 111t115 45t114 -45t42 -111t-42 -110.5t-114 -44.5q-74 0 -116 44.5t-42 110.5z" />
<glyph unicode="&#xf7;" horiz-adv-x="1159" d="M82 528v281h995v-281h-995zM416 264q0 70 45 110t119 40q73 0 118 -40.5t45 -109.5q0 -71 -44.5 -110t-118.5 -39q-75 0 -119.5 39t-44.5 110zM416 1073q0 70 45 110t119 40q73 0 118 -40.5t45 -109.5q0 -71 -44.5 -110t-118.5 -39q-75 0 -119.5 39t-44.5 110z" />
<glyph unicode="&#xf8;" horiz-adv-x="1185" d="M33 524q0 248 151 402.5t410 154.5q215 0 360 -110l103 102l88 -86l-105 -104q113 -144 113 -359q0 -248 -151 -402.5t-408 -154.5q-215 0 -358 109l-103 -103l-86 88l100 101q-114 148 -114 362zM360 524q0 -67 17 -133l377 377q-62 68 -160 68q-110 0 -172 -81 t-62 -231zM436 276q60 -63 158 -63q109 0 170 80.5t61 230.5q0 74 -14 129z" />
<glyph unicode="&#xf9;" horiz-adv-x="1167" d="M72 340v709h327v-605q0 -115 39.5 -163t124.5 -48q197 0 197 209v607h327v-1049h-327v152h-4q-106 -185 -344 -185q-155 0 -247.5 98t-92.5 275zM248 1475h368l89 -287h-246z" />
<glyph unicode="&#xfa;" horiz-adv-x="1167" d="M72 340v709h327v-605q0 -115 39.5 -163t124.5 -48q197 0 197 209v607h327v-1049h-327v152h-4q-106 -185 -344 -185q-155 0 -247.5 98t-92.5 275zM453 1188l88 287h368l-211 -287h-245z" />
<glyph unicode="&#xfb;" horiz-adv-x="1167" d="M72 340v709h327v-605q0 -115 39.5 -163t124.5 -48q197 0 197 209v607h327v-1049h-327v152h-4q-106 -185 -344 -185q-155 0 -247.5 98t-92.5 275zM201 1188l219 287h319l219 -287h-284l-92 141h-4l-93 -141h-284z" />
<glyph unicode="&#xfc;" horiz-adv-x="1167" d="M72 340v709h327v-605q0 -115 39.5 -163t124.5 -48q197 0 197 209v607h327v-1049h-327v152h-4q-106 -185 -344 -185q-155 0 -247.5 98t-92.5 275zM203 1331q0 66 42.5 111t114.5 45t114 -45t42 -111t-42 -110.5t-114 -44.5q-74 0 -115.5 44.5t-41.5 110.5zM643 1331 q0 66 43 111t115 45t113.5 -45t41.5 -111t-41.5 -110.5t-113.5 -44.5q-74 0 -116 44.5t-42 110.5z" />
<glyph unicode="&#xfd;" horiz-adv-x="1083" d="M-6 1049h328l237 -689h4l199 689h328l-357 -1069q-67 -199 -154 -270.5t-255 -71.5q-86 0 -183 20v246h4q149 -33 199 43q42 64 -8 205zM416 1188l88 287h368l-210 -287h-246z" />
<glyph unicode="&#xfe;" horiz-adv-x="1204" d="M80 -362v1826h328v-561h4q76 178 303 178q210 0 333 -150t123 -407t-123 -407t-333 -150q-227 0 -303 178h-4v-507h-328zM414 524q0 -151 57 -231t158 -80t158 80t57 231q0 152 -57 232t-158 80t-158 -80t-57 -232z" />
<glyph unicode="&#xff;" horiz-adv-x="1083" d="M-6 1049h328l237 -689h4l199 689h328l-357 -1069q-67 -199 -154 -270.5t-255 -71.5q-86 0 -183 20v246h4q149 -33 199 43q42 64 -8 205zM166 1331q0 66 43 111t115 45t113.5 -45t41.5 -111t-41.5 -110.5t-113.5 -44.5q-74 0 -116 44.5t-42 110.5zM606 1331q0 66 43 111 t115 45t114 -45t42 -111t-42 -110.5t-114 -44.5q-74 0 -116 44.5t-42 110.5z" />
<glyph unicode="&#x152;" horiz-adv-x="2246" d="M41 733q0 219 81 392.5t233 272.5t350 99q210 0 356 -129v96h1110v-301h-741v-260h638v-301h-638v-301h753v-301h-1122v96q-146 -129 -356 -129q-149 0 -273.5 57.5t-210 159.5t-133 243t-47.5 306zM410 733q0 -100 18.5 -181t58.5 -146.5t111 -101.5t168 -36 q99 0 174 50t121 139v551q-45 89 -120.5 138.5t-174.5 49.5q-97 0 -168 -36t-111 -101t-58.5 -145.5t-18.5 -180.5z" />
<glyph unicode="&#x153;" horiz-adv-x="1996" d="M33 524q0 248 151 402.5t410 154.5q268 0 422 -170q151 170 405 170q175 0 298.5 -82t183 -225t59.5 -336h-754q0 -105 66.5 -165t159.5 -60q78 0 123.5 30t62.5 74h326q-44 -164 -171.5 -257t-334.5 -93q-270 0 -424 170q-154 -170 -422 -170q-259 0 -410 154.5 t-151 402.5zM360 524q0 -150 62 -230.5t172 -80.5q109 0 170 80.5t61 230.5t-61 231t-170 81q-110 0 -172 -81t-62 -231zM1208 637h426q0 106 -58 162.5t-155 56.5t-155 -56.5t-58 -162.5z" />
<glyph unicode="&#x178;" horiz-adv-x="1357" d="M2 1464h389l285 -602h4l287 602h389l-494 -923v-541h-368v541zM303 1747q0 66 43 111t115 45t113.5 -45t41.5 -111t-41.5 -111t-113.5 -45q-74 0 -116 45t-42 111zM743 1747q0 66 43 111t115 45t114 -45t42 -111t-42 -111t-114 -45q-74 0 -116 45t-42 111z" />
<glyph unicode="&#x2c6;" horiz-adv-x="950" d="M133 1188l219 287h320l219 -287h-285l-92 141h-4l-92 -141h-285z" />
<glyph unicode="&#x2dc;" horiz-adv-x="1024" d="M172 1217q0 146 45 211t135 65q49 0 137 -39t125 -39q68 0 68 78h170q0 -147 -44.5 -211.5t-135.5 -64.5q-49 0 -137 37.5t-125 37.5q-68 0 -68 -75h-170z" />
<glyph unicode="&#x2000;" horiz-adv-x="995" />
<glyph unicode="&#x2001;" horiz-adv-x="1991" />
<glyph unicode="&#x2002;" horiz-adv-x="995" />
<glyph unicode="&#x2003;" horiz-adv-x="1991" />
<glyph unicode="&#x2004;" horiz-adv-x="663" />
<glyph unicode="&#x2005;" horiz-adv-x="497" />
<glyph unicode="&#x2006;" horiz-adv-x="331" />
<glyph unicode="&#x2007;" horiz-adv-x="331" />
<glyph unicode="&#x2008;" horiz-adv-x="248" />
<glyph unicode="&#x2009;" horiz-adv-x="398" />
<glyph unicode="&#x200a;" horiz-adv-x="110" />
<glyph unicode="&#x2010;" horiz-adv-x="686" d="M66 377v280h557v-280h-557z" />
<glyph unicode="&#x2011;" horiz-adv-x="686" d="M66 377v280h557v-280h-557z" />
<glyph unicode="&#x2012;" horiz-adv-x="686" d="M66 377v280h557v-280h-557z" />
<glyph unicode="&#x2013;" horiz-adv-x="1024" d="M0 377v280h1024v-280h-1024z" />
<glyph unicode="&#x2014;" horiz-adv-x="2048" d="M205 377v280h1638v-280h-1638z" />
<glyph unicode="&#x2018;" horiz-adv-x="514" d="M70 1044q0 178 92.5 291t255.5 135v-143q-75 -10 -124.5 -63t-49.5 -117q88 0 141 -46t53 -128q0 -81 -51.5 -127.5t-130.5 -46.5q-92 0 -139 64.5t-47 180.5z" />
<glyph unicode="&#x2019;" horiz-adv-x="503" d="M66 1309q0 81 51.5 127.5t130.5 46.5q92 0 139 -64.5t47 -181.5q0 -178 -92.5 -291t-255.5 -135v143q75 10 124.5 63.5t49.5 117.5q-88 0 -141 46t-53 128z" />
<glyph unicode="&#x201a;" horiz-adv-x="503" d="M66 156q0 81 51.5 127.5t130.5 46.5q92 0 139 -64.5t47 -181.5q0 -178 -92.5 -291t-255.5 -135v143q75 10 124.5 63.5t49.5 117.5q-88 0 -141 46t-53 128z" />
<glyph unicode="&#x201c;" horiz-adv-x="954" d="M70 1044q0 178 92.5 291t255.5 135v-143q-75 -10 -124.5 -63t-49.5 -117q88 0 141 -46t53 -128q0 -81 -51.5 -127.5t-130.5 -46.5q-92 0 -139 64.5t-47 180.5zM520 1044q0 178 92.5 291t255.5 135v-143q-75 -10 -124.5 -63t-49.5 -117q88 0 141.5 -46t53.5 -128 q0 -81 -51.5 -127.5t-130.5 -46.5q-92 0 -139.5 64.5t-47.5 180.5z" />
<glyph unicode="&#x201d;" horiz-adv-x="954" d="M66 1309q0 81 51.5 127.5t130.5 46.5q92 0 139 -64.5t47 -181.5q0 -178 -92.5 -291t-255.5 -135v143q75 10 124.5 63.5t49.5 117.5q-88 0 -141 46t-53 128zM516 1309q0 81 51.5 127.5t130.5 46.5q92 0 139.5 -65t47.5 -181q0 -178 -92.5 -291t-255.5 -135v143 q75 10 124.5 63.5t49.5 117.5q-88 0 -141.5 46t-53.5 128z" />
<glyph unicode="&#x201e;" horiz-adv-x="954" d="M66 156q0 81 51.5 127.5t130.5 46.5q92 0 139 -64.5t47 -181.5q0 -178 -92.5 -291t-255.5 -135v143q75 10 124.5 63.5t49.5 117.5q-88 0 -141 46t-53 128zM516 156q0 81 51.5 127.5t130.5 46.5q92 0 139.5 -65t47.5 -181q0 -178 -92.5 -291t-255.5 -135v143 q75 10 124.5 63.5t49.5 117.5q-88 0 -141.5 46t-53.5 128z" />
<glyph unicode="&#x2022;" horiz-adv-x="765" d="M76 655q0 130 85 219t222 89t222 -89t85 -219t-84.5 -218.5t-222.5 -88.5t-222.5 88.5t-84.5 218.5z" />
<glyph unicode="&#x2026;" horiz-adv-x="1339" d="M66 156q0 77 49.5 125.5t124.5 48.5q73 0 123.5 -48.5t50.5 -125.5q0 -78 -50 -126t-124 -48q-75 0 -124.5 48t-49.5 126zM496 156q0 77 49.5 125.5t124.5 48.5q73 0 123.5 -48.5t50.5 -125.5q0 -78 -50 -126t-124 -48q-75 0 -124.5 48t-49.5 126zM926 156 q0 77 49.5 125.5t124.5 48.5q73 0 123.5 -48.5t50.5 -125.5q0 -78 -50 -126t-124 -48q-75 0 -124.5 48t-49.5 126z" />
<glyph unicode="&#x202f;" horiz-adv-x="398" />
<glyph unicode="&#x2039;" horiz-adv-x="542" d="M90 362v308l348 295v-308l-174 -139v-4l174 -139v-307z" />
<glyph unicode="&#x203a;" horiz-adv-x="542" d="M104 68v307l175 139v4l-175 139v308l349 -295v-308z" />
<glyph unicode="&#x205f;" horiz-adv-x="497" />
<glyph unicode="&#x20ac;" horiz-adv-x="1404" d="M61 510v123h97q-2 23 -2 69v50h-95v122h105q17 124 59 223t113.5 177t182.5 120t255 42q273 0 414.5 -148t152.5 -399h-348q-8 129 -60 197.5t-167 68.5q-114 0 -174 -72t-80 -209h326v-122h-336v-50q0 -46 2 -69h334v-123h-324q23 -129 82.5 -195.5t169.5 -66.5 q117 0 169 69t58 201h348q-5 -124 -41 -223.5t-104 -173.5t-175 -114t-247 -40q-114 0 -207 27t-160 75t-116.5 117t-78.5 149t-44 175h-109z" />
<glyph unicode="&#x2122;" horiz-adv-x="1472" d="M63 1280v184h504v-184h-159v-379h-185v379h-160zM643 901v563h248l108 -297h5l110 297h250v-563h-188v359h-5l-104 -359h-133l-103 359h-4v-359h-184z" />
<glyph unicode="&#x25fc;" horiz-adv-x="1044" d="M0 0v1044h1044v-1044h-1044z" />
<hkern u1="&#x23;" u2="&#x39;" k="-20" />
<hkern u1="&#x23;" u2="&#x38;" k="20" />
<hkern u1="&#x23;" u2="&#x37;" k="-31" />
<hkern u1="&#x23;" u2="&#x35;" k="31" />
<hkern u1="&#x23;" u2="&#x34;" k="41" />
<hkern u1="&#x23;" u2="&#x31;" k="-20" />
<hkern u1="&#x24;" u2="&#x37;" k="10" />
<hkern u1="&#x26;" u2="x" k="-10" />
<hkern u1="&#x26;" u2="v" k="82" />
<hkern u1="&#x26;" u2="V" k="133" />
<hkern u1="&#x26;" u2="&#x39;" k="51" />
<hkern u1="&#x26;" u2="&#x38;" k="31" />
<hkern u1="&#x26;" u2="&#x37;" k="72" />
<hkern u1="&#x26;" u2="&#x36;" k="20" />
<hkern u1="&#x26;" u2="&#x35;" k="31" />
<hkern u1="&#x26;" u2="&#x34;" k="31" />
<hkern u1="&#x26;" u2="&#x33;" k="41" />
<hkern u1="&#x26;" u2="&#x32;" k="-10" />
<hkern u1="&#x26;" u2="&#x31;" k="82" />
<hkern u1="&#x26;" u2="&#x30;" k="20" />
<hkern u1="&#x28;" u2="x" k="10" />
<hkern u1="&#x28;" u2="v" k="10" />
<hkern u1="&#x28;" u2="V" k="-10" />
<hkern u1="&#x28;" u2="&#x34;" k="31" />
<hkern u1="&#x28;" u2="&#x31;" k="51" />
<hkern u1="&#x28;" u2="&#x30;" k="10" />
<hkern u1="&#x2a;" u2="x" k="10" />
<hkern u1="&#x2a;" u2="v" k="-31" />
<hkern u1="&#x2a;" u2="X" k="72" />
<hkern u1="&#x2a;" u2="&#x39;" k="10" />
<hkern u1="&#x2a;" u2="&#x38;" k="31" />
<hkern u1="&#x2a;" u2="&#x37;" k="-20" />
<hkern u1="&#x2a;" u2="&#x36;" k="20" />
<hkern u1="&#x2a;" u2="&#x35;" k="31" />
<hkern u1="&#x2a;" u2="&#x34;" k="123" />
<hkern u1="&#x2a;" u2="&#x33;" k="10" />
<hkern u1="&#x2a;" u2="&#x32;" k="10" />
<hkern u1="&#x2a;" u2="&#x30;" k="10" />
<hkern u1="&#x2c;" u2="j" k="-113" />
<hkern u1="&#x2f;" u2="&#xdf;" k="61" />
<hkern u1="&#x2f;" u2="x" k="123" />
<hkern u1="&#x2f;" u2="v" k="82" />
<hkern u1="&#x2f;" u2="p" k="102" />
<hkern u1="&#x2f;" u2="V" k="-31" />
<hkern u1="&#x2f;" u2="&#x39;" k="72" />
<hkern u1="&#x2f;" u2="&#x38;" k="72" />
<hkern u1="&#x2f;" u2="&#x37;" k="-20" />
<hkern u1="&#x2f;" u2="&#x36;" k="102" />
<hkern u1="&#x2f;" u2="&#x35;" k="92" />
<hkern u1="&#x2f;" u2="&#x34;" k="164" />
<hkern u1="&#x2f;" u2="&#x33;" k="82" />
<hkern u1="&#x2f;" u2="&#x32;" k="82" />
<hkern u1="&#x2f;" u2="&#x31;" k="41" />
<hkern u1="&#x2f;" u2="&#x30;" k="82" />
<hkern u1="&#x2f;" u2="&#x2f;" k="266" />
<hkern u1="&#x30;" u2="&#x2122;" k="82" />
<hkern u1="&#x30;" u2="&#xb7;" k="-20" />
<hkern u1="&#x30;" u2="&#xb0;" k="41" />
<hkern u1="&#x30;" u2="^" k="-10" />
<hkern u1="&#x30;" u2="\" k="82" />
<hkern u1="&#x30;" u2="&#x39;" k="10" />
<hkern u1="&#x30;" u2="&#x38;" k="10" />
<hkern u1="&#x30;" u2="&#x37;" k="51" />
<hkern u1="&#x30;" u2="&#x35;" k="20" />
<hkern u1="&#x30;" u2="&#x32;" k="20" />
<hkern u1="&#x30;" u2="&#x31;" k="10" />
<hkern u1="&#x30;" u2="&#x2f;" k="61" />
<hkern u1="&#x30;" u2="&#x2a;" k="10" />
<hkern u1="&#x30;" u2="&#x29;" k="10" />
<hkern u1="&#x30;" u2="&#x26;" k="10" />
<hkern u1="&#x31;" u2="&#x40;" k="-10" />
<hkern u1="&#x32;" u2="&#x2122;" k="10" />
<hkern u1="&#x32;" u2="&#xa2;" k="10" />
<hkern u1="&#x32;" u2="^" k="10" />
<hkern u1="&#x32;" u2="\" k="61" />
<hkern u1="&#x32;" u2="&#x40;" k="-20" />
<hkern u1="&#x32;" u2="&#x3c;" k="10" />
<hkern u1="&#x32;" u2="&#x39;" k="-20" />
<hkern u1="&#x32;" u2="&#x34;" k="20" />
<hkern u1="&#x32;" u2="&#x32;" k="-20" />
<hkern u1="&#x32;" u2="&#x31;" k="-20" />
<hkern u1="&#x32;" u2="&#x30;" k="-10" />
<hkern u1="&#x32;" u2="&#x2f;" k="10" />
<hkern u1="&#x32;" u2="&#x26;" k="10" />
<hkern u1="&#x32;" u2="&#x23;" k="10" />
<hkern u1="&#x33;" u2="&#x2122;" k="41" />
<hkern u1="&#x33;" u2="&#xb7;" k="-20" />
<hkern u1="&#x33;" u2="&#xb0;" k="20" />
<hkern u1="&#x33;" u2="\" k="72" />
<hkern u1="&#x33;" u2="&#x40;" k="-20" />
<hkern u1="&#x33;" u2="&#x37;" k="31" />
<hkern u1="&#x33;" u2="&#x2f;" k="20" />
<hkern u1="&#x33;" u2="&#x2a;" k="10" />
<hkern u1="&#x34;" u2="&#x2122;" k="72" />
<hkern u1="&#x34;" u2="&#xb7;" k="-20" />
<hkern u1="&#x34;" u2="&#xb0;" k="113" />
<hkern u1="&#x34;" u2="&#xa2;" k="-20" />
<hkern u1="&#x34;" u2="^" k="-10" />
<hkern u1="&#x34;" u2="\" k="82" />
<hkern u1="&#x34;" u2="&#x40;" k="-20" />
<hkern u1="&#x34;" u2="&#x3f;" k="41" />
<hkern u1="&#x34;" u2="&#x3c;" k="10" />
<hkern u1="&#x34;" u2="&#x38;" k="-20" />
<hkern u1="&#x34;" u2="&#x37;" k="41" />
<hkern u1="&#x34;" u2="&#x34;" k="-20" />
<hkern u1="&#x34;" u2="&#x32;" k="20" />
<hkern u1="&#x34;" u2="&#x31;" k="51" />
<hkern u1="&#x34;" u2="&#x2a;" k="61" />
<hkern u1="&#x34;" u2="&#x26;" k="-20" />
<hkern u1="&#x34;" u2="&#x23;" k="-20" />
<hkern u1="&#x35;" u2="&#x2122;" k="10" />
<hkern u1="&#x35;" u2="&#xb7;" k="-20" />
<hkern u1="&#x35;" u2="&#xb0;" k="51" />
<hkern u1="&#x35;" u2="\" k="51" />
<hkern u1="&#x35;" u2="&#x3f;" k="31" />
<hkern u1="&#x35;" u2="&#x39;" k="10" />
<hkern u1="&#x35;" u2="&#x37;" k="20" />
<hkern u1="&#x35;" u2="&#x32;" k="20" />
<hkern u1="&#x35;" u2="&#x31;" k="31" />
<hkern u1="&#x35;" u2="&#x2f;" k="31" />
<hkern u1="&#x35;" u2="&#x2a;" k="10" />
<hkern u1="&#x36;" u2="&#x2122;" k="41" />
<hkern u1="&#x36;" u2="&#xb7;" k="-20" />
<hkern u1="&#x36;" u2="&#xb0;" k="31" />
<hkern u1="&#x36;" u2="\" k="72" />
<hkern u1="&#x36;" u2="&#x40;" k="-20" />
<hkern u1="&#x36;" u2="&#x3e;" k="10" />
<hkern u1="&#x36;" u2="&#x39;" k="10" />
<hkern u1="&#x36;" u2="&#x37;" k="10" />
<hkern u1="&#x36;" u2="&#x32;" k="20" />
<hkern u1="&#x36;" u2="&#x2f;" k="41" />
<hkern u1="&#x36;" u2="&#x2a;" k="10" />
<hkern u1="&#x37;" u2="&#x2122;" k="-10" />
<hkern u1="&#x37;" u2="&#xb7;" k="51" />
<hkern u1="&#x37;" u2="&#xb0;" k="-31" />
<hkern u1="&#x37;" u2="&#xae;" k="31" />
<hkern u1="&#x37;" u2="&#xa7;" k="61" />
<hkern u1="&#x37;" u2="&#xa2;" k="123" />
<hkern u1="&#x37;" u2="^" k="61" />
<hkern u1="&#x37;" u2="\" k="-20" />
<hkern u1="&#x37;" u2="&#x40;" k="61" />
<hkern u1="&#x37;" u2="&#x3f;" k="-20" />
<hkern u1="&#x37;" u2="&#x3d;" k="10" />
<hkern u1="&#x37;" u2="&#x3c;" k="82" />
<hkern u1="&#x37;" u2="&#x39;" k="10" />
<hkern u1="&#x37;" u2="&#x38;" k="10" />
<hkern u1="&#x37;" u2="&#x37;" k="-41" />
<hkern u1="&#x37;" u2="&#x36;" k="41" />
<hkern u1="&#x37;" u2="&#x35;" k="51" />
<hkern u1="&#x37;" u2="&#x34;" k="113" />
<hkern u1="&#x37;" u2="&#x33;" k="31" />
<hkern u1="&#x37;" u2="&#x32;" k="20" />
<hkern u1="&#x37;" u2="&#x30;" k="41" />
<hkern u1="&#x37;" u2="&#x2f;" k="164" />
<hkern u1="&#x37;" u2="&#x2a;" k="-20" />
<hkern u1="&#x37;" u2="&#x26;" k="92" />
<hkern u1="&#x37;" u2="&#x23;" k="72" />
<hkern u1="&#x37;" u2="&#x21;" k="-20" />
<hkern u1="&#x38;" u2="&#x2122;" k="61" />
<hkern u1="&#x38;" u2="&#xb7;" k="-20" />
<hkern u1="&#x38;" u2="&#xb0;" k="41" />
<hkern u1="&#x38;" u2="&#xae;" k="10" />
<hkern u1="&#x38;" u2="^" k="-10" />
<hkern u1="&#x38;" u2="\" k="72" />
<hkern u1="&#x38;" u2="&#x40;" k="-20" />
<hkern u1="&#x38;" u2="&#x3f;" k="20" />
<hkern u1="&#x38;" u2="&#x39;" k="10" />
<hkern u1="&#x38;" u2="&#x38;" k="-20" />
<hkern u1="&#x38;" u2="&#x37;" k="10" />
<hkern u1="&#x38;" u2="&#x36;" k="10" />
<hkern u1="&#x38;" u2="&#x34;" k="-20" />
<hkern u1="&#x38;" u2="&#x32;" k="20" />
<hkern u1="&#x38;" u2="&#x31;" k="10" />
<hkern u1="&#x38;" u2="&#x30;" k="10" />
<hkern u1="&#x38;" u2="&#x2f;" k="20" />
<hkern u1="&#x38;" u2="&#x2a;" k="31" />
<hkern u1="&#x38;" u2="&#x23;" k="-20" />
<hkern u1="&#x39;" u2="&#x2122;" k="41" />
<hkern u1="&#x39;" u2="&#xb7;" k="-20" />
<hkern u1="&#x39;" u2="&#xb0;" k="31" />
<hkern u1="&#x39;" u2="^" k="-10" />
<hkern u1="&#x39;" u2="\" k="92" />
<hkern u1="&#x39;" u2="&#x37;" k="31" />
<hkern u1="&#x39;" u2="&#x32;" k="20" />
<hkern u1="&#x39;" u2="&#x2f;" k="72" />
<hkern u1="&#x39;" u2="&#x2a;" k="10" />
<hkern u1="&#x3b;" u2="j" k="-113" />
<hkern u1="&#x3d;" u2="&#x37;" k="20" />
<hkern u1="&#x3d;" u2="&#x31;" k="10" />
<hkern u1="&#x3e;" u2="&#x39;" k="10" />
<hkern u1="&#x3e;" u2="&#x37;" k="82" />
<hkern u1="&#x3e;" u2="&#x32;" k="31" />
<hkern u1="&#x3e;" u2="&#x31;" k="31" />
<hkern u1="&#x40;" u2="x" k="41" />
<hkern u1="&#x40;" u2="v" k="-10" />
<hkern u1="&#x40;" u2="X" k="113" />
<hkern u1="&#x40;" u2="V" k="82" />
<hkern u1="&#x40;" u2="&#x38;" k="-10" />
<hkern u1="&#x40;" u2="&#x37;" k="41" />
<hkern u1="&#x40;" u2="&#x36;" k="-20" />
<hkern u1="&#x40;" u2="&#x34;" k="-20" />
<hkern u1="&#x40;" u2="&#x32;" k="10" />
<hkern u1="&#x40;" u2="&#x31;" k="10" />
<hkern u1="B" u2="&#x2122;" k="10" />
<hkern u1="B" u2="x" k="41" />
<hkern u1="B" u2="v" k="20" />
<hkern u1="B" u2="^" k="-20" />
<hkern u1="B" u2="\" k="51" />
<hkern u1="B" u2="X" k="61" />
<hkern u1="B" u2="V" k="41" />
<hkern u1="B" u2="&#x40;" k="-20" />
<hkern u1="B" u2="&#x2f;" k="20" />
<hkern u1="B" u2="&#x29;" k="10" />
<hkern u1="F" u2="&#xdf;" k="31" />
<hkern u1="F" u2="&#xb7;" k="10" />
<hkern u1="F" u2="&#xae;" k="20" />
<hkern u1="F" u2="x" k="92" />
<hkern u1="F" u2="v" k="51" />
<hkern u1="F" u2="p" k="51" />
<hkern u1="F" u2="^" k="31" />
<hkern u1="F" u2="\" k="-20" />
<hkern u1="F" u2="X" k="20" />
<hkern u1="F" u2="V" k="-20" />
<hkern u1="F" u2="&#x40;" k="41" />
<hkern u1="F" u2="&#x3f;" k="-20" />
<hkern u1="F" u2="&#x2f;" k="123" />
<hkern u1="F" u2="&#x2a;" k="-10" />
<hkern u1="F" u2="&#x29;" k="-41" />
<hkern u1="F" u2="&#x26;" k="82" />
<hkern u1="P" u2="&#x2122;" k="-10" />
<hkern u1="P" u2="&#xae;" k="-10" />
<hkern u1="P" u2="x" k="20" />
<hkern u1="P" u2="v" k="-31" />
<hkern u1="P" u2="^" k="20" />
<hkern u1="P" u2="\" k="20" />
<hkern u1="P" u2="X" k="102" />
<hkern u1="P" u2="V" k="20" />
<hkern u1="P" u2="&#x3f;" k="-41" />
<hkern u1="P" u2="&#x2f;" k="143" />
<hkern u1="P" u2="&#x2a;" k="-10" />
<hkern u1="P" u2="&#x29;" k="20" />
<hkern u1="P" u2="&#x26;" k="72" />
<hkern u1="Q" u2="&#x2122;" k="72" />
<hkern u1="Q" u2="x" k="31" />
<hkern u1="Q" u2="\" k="102" />
<hkern u1="Q" u2="X" k="72" />
<hkern u1="Q" u2="V" k="72" />
<hkern u1="Q" u2="&#x2f;" k="10" />
<hkern u1="Q" u2="&#x2c;" k="72" />
<hkern u1="Q" u2="&#x2a;" k="10" />
<hkern u1="V" u2="&#x2122;" k="-10" />
<hkern u1="V" u2="&#xdf;" k="41" />
<hkern u1="V" u2="&#xb7;" k="82" />
<hkern u1="V" u2="&#xae;" k="72" />
<hkern u1="V" u2="x" k="92" />
<hkern u1="V" u2="v" k="51" />
<hkern u1="V" u2="p" k="72" />
<hkern u1="V" u2="^" k="72" />
<hkern u1="V" u2="\" k="-31" />
<hkern u1="V" u2="V" k="-41" />
<hkern u1="V" u2="&#x40;" k="102" />
<hkern u1="V" u2="&#x2f;" k="164" />
<hkern u1="V" u2="&#x29;" k="-10" />
<hkern u1="V" u2="&#x26;" k="102" />
<hkern u1="X" u2="&#xdf;" k="10" />
<hkern u1="X" u2="&#xb7;" k="143" />
<hkern u1="X" u2="&#xae;" k="113" />
<hkern u1="X" u2="v" k="113" />
<hkern u1="X" u2="^" k="51" />
<hkern u1="X" u2="X" k="-20" />
<hkern u1="X" u2="&#x40;" k="72" />
<hkern u1="X" u2="&#x3f;" k="72" />
<hkern u1="X" u2="&#x2f;" k="-20" />
<hkern u1="X" u2="&#x2a;" k="72" />
<hkern u1="X" u2="&#x26;" k="61" />
<hkern u1="\" u2="v" k="82" />
<hkern u1="\" u2="X" k="-20" />
<hkern u1="\" u2="V" k="164" />
<hkern u1="\" u2="\" k="266" />
<hkern u1="\" u2="&#x39;" k="61" />
<hkern u1="\" u2="&#x38;" k="20" />
<hkern u1="\" u2="&#x37;" k="92" />
<hkern u1="\" u2="&#x36;" k="72" />
<hkern u1="\" u2="&#x35;" k="20" />
<hkern u1="\" u2="&#x34;" k="31" />
<hkern u1="\" u2="&#x33;" k="20" />
<hkern u1="\" u2="&#x31;" k="113" />
<hkern u1="\" u2="&#x30;" k="61" />
<hkern u1="^" u2="x" k="41" />
<hkern u1="^" u2="v" k="20" />
<hkern u1="^" u2="X" k="51" />
<hkern u1="^" u2="V" k="72" />
<hkern u1="^" u2="&#x39;" k="10" />
<hkern u1="^" u2="&#x38;" k="-10" />
<hkern u1="^" u2="&#x37;" k="41" />
<hkern u1="^" u2="&#x34;" k="-10" />
<hkern u1="^" u2="&#x32;" k="10" />
<hkern u1="^" u2="&#x31;" k="72" />
<hkern u1="^" u2="&#x30;" k="-10" />
<hkern u1="q" u2="&#x2122;" k="51" />
<hkern u1="q" u2="\" k="113" />
<hkern u1="v" u2="&#xb7;" k="10" />
<hkern u1="v" u2="v" k="-41" />
<hkern u1="v" u2="^" k="20" />
<hkern u1="v" u2="\" k="82" />
<hkern u1="v" u2="&#x3f;" k="-51" />
<hkern u1="v" u2="&#x2f;" k="82" />
<hkern u1="v" u2="&#x2a;" k="-31" />
<hkern u1="v" u2="&#x29;" k="10" />
<hkern u1="v" u2="&#x26;" k="61" />
<hkern u1="x" u2="&#x2122;" k="41" />
<hkern u1="x" u2="&#xb7;" k="51" />
<hkern u1="x" u2="&#xae;" k="51" />
<hkern u1="x" u2="^" k="41" />
<hkern u1="x" u2="\" k="123" />
<hkern u1="x" u2="&#x40;" k="61" />
<hkern u1="x" u2="&#x2a;" k="10" />
<hkern u1="x" u2="&#x29;" k="10" />
<hkern u1="x" u2="&#x26;" k="61" />
<hkern u1="&#xa1;" u2="V" k="31" />
<hkern u1="&#xa1;" u2="&#x37;" k="10" />
<hkern u1="&#xa3;" u2="&#x37;" k="10" />
<hkern u1="&#xa3;" u2="&#x35;" k="20" />
<hkern u1="&#xa3;" u2="&#x34;" k="61" />
<hkern u1="&#xa5;" u2="&#x39;" k="51" />
<hkern u1="&#xa5;" u2="&#x38;" k="51" />
<hkern u1="&#xa5;" u2="&#x36;" k="72" />
<hkern u1="&#xa5;" u2="&#x35;" k="61" />
<hkern u1="&#xa5;" u2="&#x34;" k="61" />
<hkern u1="&#xa5;" u2="&#x33;" k="51" />
<hkern u1="&#xa5;" u2="&#x32;" k="72" />
<hkern u1="&#xa5;" u2="&#x31;" k="61" />
<hkern u1="&#xa5;" u2="&#x30;" k="72" />
<hkern u1="&#xa7;" u2="&#x37;" k="31" />
<hkern u1="&#xa7;" u2="&#x33;" k="-10" />
<hkern u1="&#xa7;" u2="&#x31;" k="31" />
<hkern u1="&#xae;" u2="x" k="20" />
<hkern u1="&#xae;" u2="X" k="51" />
<hkern u1="&#xae;" u2="V" k="31" />
<hkern u1="&#xae;" u2="&#x39;" k="10" />
<hkern u1="&#xae;" u2="&#x38;" k="10" />
<hkern u1="&#xae;" u2="&#x37;" k="20" />
<hkern u1="&#xae;" u2="&#x32;" k="10" />
<hkern u1="&#xae;" u2="&#x31;" k="10" />
<hkern u1="&#xb0;" u2="&#x39;" k="10" />
<hkern u1="&#xb0;" u2="&#x38;" k="41" />
<hkern u1="&#xb0;" u2="&#x37;" k="-20" />
<hkern u1="&#xb0;" u2="&#x36;" k="41" />
<hkern u1="&#xb0;" u2="&#x35;" k="72" />
<hkern u1="&#xb0;" u2="&#x34;" k="195" />
<hkern u1="&#xb0;" u2="&#x33;" k="20" />
<hkern u1="&#xb0;" u2="&#x32;" k="31" />
<hkern u1="&#xb0;" u2="&#x31;" k="-20" />
<hkern u1="&#xb0;" u2="&#x30;" k="41" />
<hkern u1="&#xb1;" u2="&#x3f;" k="-10" />
<hkern u1="&#xb7;" u2="x" k="51" />
<hkern u1="&#xb7;" u2="v" k="10" />
<hkern u1="&#xb7;" u2="X" k="143" />
<hkern u1="&#xb7;" u2="V" k="82" />
<hkern u1="&#xb7;" u2="&#x39;" k="-20" />
<hkern u1="&#xb7;" u2="&#x38;" k="-20" />
<hkern u1="&#xb7;" u2="&#x37;" k="61" />
<hkern u1="&#xb7;" u2="&#x36;" k="-20" />
<hkern u1="&#xb7;" u2="&#x34;" k="-20" />
<hkern u1="&#xb7;" u2="&#x33;" k="-20" />
<hkern u1="&#xb7;" u2="&#x31;" k="20" />
<hkern u1="&#xb7;" u2="&#x30;" k="-20" />
<hkern u1="&#xbf;" u2="v" k="123" />
<hkern u1="&#xbf;" u2="V" k="205" />
<hkern u1="&#xbf;" u2="&#x39;" k="41" />
<hkern u1="&#xbf;" u2="&#x37;" k="82" />
<hkern u1="&#xbf;" u2="&#x36;" k="51" />
<hkern u1="&#xbf;" u2="&#x35;" k="20" />
<hkern u1="&#xbf;" u2="&#x34;" k="51" />
<hkern u1="&#xbf;" u2="&#x33;" k="20" />
<hkern u1="&#xbf;" u2="&#x32;" k="-10" />
<hkern u1="&#xbf;" u2="&#x31;" k="113" />
<hkern u1="&#xbf;" u2="&#x30;" k="51" />
<hkern u1="&#xdf;" u2="&#x2122;" k="41" />
<hkern u1="&#xdf;" u2="x" k="41" />
<hkern u1="&#xdf;" u2="v" k="31" />
<hkern u1="&#xdf;" u2="^" k="-10" />
<hkern u1="&#xdf;" u2="\" k="82" />
<hkern u1="&#xdf;" u2="&#x2f;" k="20" />
<hkern u1="&#xdf;" u2="&#x2a;" k="20" />
<hkern g1="bracketleft,braceleft" 	g2="five" 	k="41" />
<hkern g1="bracketleft,braceleft" 	g2="four" 	k="72" />
<hkern g1="bracketleft,braceleft" 	g2="nine" 	k="20" />
<hkern g1="bracketleft,braceleft" 	g2="one" 	k="10" />
<hkern g1="bracketleft,braceleft" 	g2="three" 	k="20" />
<hkern g1="bracketleft,braceleft" 	g2="zero" 	k="31" />
<hkern g1="colon,semicolon" 	g2="four" 	k="-20" />
<hkern g1="colon,semicolon" 	g2="one" 	k="20" />
<hkern g1="colon,semicolon" 	g2="zero" 	k="-20" />
<hkern g1="colon,semicolon" 	g2="seven" 	k="10" />
<hkern g1="colon,semicolon" 	g2="two" 	k="-20" />
<hkern g1="comma,period,ellipsis" 	g2="four" 	k="20" />
<hkern g1="comma,period,ellipsis" 	g2="nine" 	k="31" />
<hkern g1="comma,period,ellipsis" 	g2="one" 	k="174" />
<hkern g1="comma,period,ellipsis" 	g2="zero" 	k="31" />
<hkern g1="comma,period,ellipsis" 	g2="seven" 	k="51" />
<hkern g1="comma,period,ellipsis" 	g2="six" 	k="51" />
<hkern g1="guilsinglleft" 	g2="one" 	k="20" />
<hkern g1="guilsinglleft" 	g2="seven" 	k="31" />
<hkern g1="guilsinglleft" 	g2="two" 	k="10" />
<hkern g1="guilsinglright" 	g2="four" 	k="-20" />
<hkern g1="guilsinglright" 	g2="nine" 	k="10" />
<hkern g1="guilsinglright" 	g2="one" 	k="72" />
<hkern g1="guilsinglright" 	g2="seven" 	k="92" />
<hkern g1="guilsinglright" 	g2="two" 	k="31" />
<hkern g1="guilsinglright" 	g2="eight" 	k="10" />
<hkern g1="hyphen,endash,emdash" 	g2="four" 	k="-20" />
<hkern g1="hyphen,endash,emdash" 	g2="nine" 	k="10" />
<hkern g1="hyphen,endash,emdash" 	g2="one" 	k="113" />
<hkern g1="hyphen,endash,emdash" 	g2="zero" 	k="-20" />
<hkern g1="hyphen,endash,emdash" 	g2="seven" 	k="92" />
<hkern g1="hyphen,endash,emdash" 	g2="two" 	k="20" />
<hkern g1="hyphen,endash,emdash" 	g2="eight" 	k="-10" />
<hkern g1="quotedbl,quotesingle" 	g2="five" 	k="51" />
<hkern g1="quotedbl,quotesingle" 	g2="four" 	k="164" />
<hkern g1="quotedbl,quotesingle" 	g2="nine" 	k="10" />
<hkern g1="quotedbl,quotesingle" 	g2="three" 	k="20" />
<hkern g1="quotedbl,quotesingle" 	g2="zero" 	k="51" />
<hkern g1="quotedbl,quotesingle" 	g2="seven" 	k="-10" />
<hkern g1="quotedbl,quotesingle" 	g2="two" 	k="20" />
<hkern g1="quotedbl,quotesingle" 	g2="six" 	k="51" />
<hkern g1="quotedbl,quotesingle" 	g2="eight" 	k="31" />
<hkern g1="quoteleft,quotedblleft" 	g2="five" 	k="31" />
<hkern g1="quoteleft,quotedblleft" 	g2="four" 	k="82" />
<hkern g1="quoteleft,quotedblleft" 	g2="nine" 	k="-20" />
<hkern g1="quoteleft,quotedblleft" 	g2="one" 	k="-41" />
<hkern g1="quoteleft,quotedblleft" 	g2="zero" 	k="10" />
<hkern g1="quoteleft,quotedblleft" 	g2="seven" 	k="-10" />
<hkern g1="quoteleft,quotedblleft" 	g2="six" 	k="10" />
<hkern g1="quoteleft,quotedblleft" 	g2="eight" 	k="10" />
<hkern g1="quoteright,quotedblright" 	g2="five" 	k="51" />
<hkern g1="quoteright,quotedblright" 	g2="four" 	k="164" />
<hkern g1="quoteright,quotedblright" 	g2="nine" 	k="10" />
<hkern g1="quoteright,quotedblright" 	g2="three" 	k="20" />
<hkern g1="quoteright,quotedblright" 	g2="zero" 	k="51" />
<hkern g1="quoteright,quotedblright" 	g2="seven" 	k="-10" />
<hkern g1="quoteright,quotedblright" 	g2="two" 	k="20" />
<hkern g1="quoteright,quotedblright" 	g2="six" 	k="51" />
<hkern g1="quoteright,quotedblright" 	g2="eight" 	k="31" />
<hkern g1="quotesinglbase,quotedblbase" 	g2="four" 	k="20" />
<hkern g1="quotesinglbase,quotedblbase" 	g2="nine" 	k="31" />
<hkern g1="quotesinglbase,quotedblbase" 	g2="one" 	k="174" />
<hkern g1="quotesinglbase,quotedblbase" 	g2="zero" 	k="31" />
<hkern g1="quotesinglbase,quotedblbase" 	g2="seven" 	k="51" />
<hkern g1="quotesinglbase,quotedblbase" 	g2="six" 	k="51" />
<hkern g1="copyright" 	g2="eight" 	k="10" />
<hkern g1="copyright" 	g2="nine" 	k="10" />
<hkern g1="copyright" 	g2="one" 	k="10" />
<hkern g1="copyright" 	g2="seven" 	k="31" />
<hkern g1="copyright" 	g2="two" 	k="20" />
<hkern g1="plus,divide" 	g2="eight" 	k="20" />
<hkern g1="plus,divide" 	g2="nine" 	k="10" />
<hkern g1="plus,divide" 	g2="one" 	k="20" />
<hkern g1="plus,divide" 	g2="seven" 	k="51" />
<hkern g1="plus,divide" 	g2="two" 	k="20" />
<hkern g1="eight" 	g2="copyright" 	k="10" />
<hkern g1="eight" 	g2="guilsinglleft" 	k="10" />
<hkern g1="eight" 	g2="hyphen,endash,emdash" 	k="-10" />
<hkern g1="eight" 	g2="plus,divide" 	k="20" />
<hkern g1="eight" 	g2="quotedbl,quotesingle" 	k="31" />
<hkern g1="eight" 	g2="quoteleft,quotedblleft" 	k="10" />
<hkern g1="eight" 	g2="quoteright,quotedblright" 	k="31" />
<hkern g1="five" 	g2="quotedbl,quotesingle" 	k="20" />
<hkern g1="five" 	g2="quoteleft,quotedblleft" 	k="41" />
<hkern g1="five" 	g2="quoteright,quotedblright" 	k="20" />
<hkern g1="five" 	g2="percent" 	k="31" />
<hkern g1="four" 	g2="guilsinglleft" 	k="-20" />
<hkern g1="four" 	g2="hyphen,endash,emdash" 	k="-20" />
<hkern g1="four" 	g2="plus,divide" 	k="10" />
<hkern g1="four" 	g2="quotedbl,quotesingle" 	k="41" />
<hkern g1="four" 	g2="quoteleft,quotedblleft" 	k="61" />
<hkern g1="four" 	g2="quoteright,quotedblright" 	k="41" />
<hkern g1="four" 	g2="percent" 	k="51" />
<hkern g1="four" 	g2="bracketright,braceright" 	k="-10" />
<hkern g1="four" 	g2="colon,semicolon" 	k="-20" />
<hkern g1="nine" 	g2="hyphen,endash,emdash" 	k="-20" />
<hkern g1="nine" 	g2="quotedbl,quotesingle" 	k="10" />
<hkern g1="nine" 	g2="quoteleft,quotedblleft" 	k="10" />
<hkern g1="nine" 	g2="quoteright,quotedblright" 	k="10" />
<hkern g1="nine" 	g2="bracketright,braceright" 	k="31" />
<hkern g1="nine" 	g2="comma,period,ellipsis" 	k="10" />
<hkern g1="nine" 	g2="quotesinglbase,quotedblbase" 	k="10" />
<hkern g1="seven" 	g2="copyright" 	k="31" />
<hkern g1="seven" 	g2="guilsinglleft" 	k="123" />
<hkern g1="seven" 	g2="hyphen,endash,emdash" 	k="102" />
<hkern g1="seven" 	g2="plus,divide" 	k="51" />
<hkern g1="seven" 	g2="quotedbl,quotesingle" 	k="-20" />
<hkern g1="seven" 	g2="quoteleft,quotedblleft" 	k="-10" />
<hkern g1="seven" 	g2="quoteright,quotedblright" 	k="-20" />
<hkern g1="seven" 	g2="percent" 	k="-41" />
<hkern g1="seven" 	g2="colon,semicolon" 	k="20" />
<hkern g1="seven" 	g2="comma,period,ellipsis" 	k="143" />
<hkern g1="seven" 	g2="quotesinglbase,quotedblbase" 	k="41" />
<hkern g1="seven" 	g2="guilsinglright" 	k="61" />
<hkern g1="six" 	g2="plus,divide" 	k="20" />
<hkern g1="six" 	g2="comma,period,ellipsis" 	k="10" />
<hkern g1="six" 	g2="quotesinglbase,quotedblbase" 	k="10" />
<hkern g1="three" 	g2="quoteleft,quotedblleft" 	k="10" />
<hkern g1="three" 	g2="percent" 	k="-20" />
<hkern g1="two" 	g2="guilsinglleft" 	k="20" />
<hkern g1="two" 	g2="hyphen,endash,emdash" 	k="10" />
<hkern g1="two" 	g2="percent" 	k="-51" />
<hkern g1="two" 	g2="colon,semicolon" 	k="-20" />
<hkern g1="zero" 	g2="hyphen,endash,emdash" 	k="-20" />
<hkern g1="zero" 	g2="quotedbl,quotesingle" 	k="10" />
<hkern g1="zero" 	g2="quoteleft,quotedblleft" 	k="10" />
<hkern g1="zero" 	g2="quoteright,quotedblright" 	k="10" />
<hkern g1="zero" 	g2="bracketright,braceright" 	k="31" />
<hkern g1="zero" 	g2="colon,semicolon" 	k="-20" />
<hkern g1="zero" 	g2="comma,period,ellipsis" 	k="31" />
<hkern g1="zero" 	g2="quotesinglbase,quotedblbase" 	k="20" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="-31" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="AE" 	k="-31" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="92" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="J" 	k="20" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="S" 	k="31" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="T" 	k="184" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	k="61" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="V" 	k="164" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="W" 	k="123" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="X" 	k="-20" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="Y,Yacute,Ydieresis" 	k="205" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="10" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="ampersand" 	k="41" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="asciicircum" 	k="20" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="asterisk" 	k="205" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="at" 	k="41" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="backslash" 	k="195" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="51" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="colon,semicolon" 	k="-20" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="comma,period,ellipsis" 	k="-41" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="copyright" 	k="92" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="d,q" 	k="51" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="f" 	k="31" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="g" 	k="51" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="guilsinglleft" 	k="51" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="guilsinglright" 	k="10" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="hyphen,endash,emdash" 	k="41" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="periodcentered" 	k="82" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="question" 	k="154" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="quotedbl,quotesingle" 	k="133" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="quoteleft,quotedblleft" 	k="154" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="quoteright,quotedblright" 	k="133" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="quotesinglbase,quotedblbase" 	k="-10" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="registered" 	k="92" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="s" 	k="20" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="slash" 	k="-41" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="t" 	k="72" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="trademark" 	k="225" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="u,ugrave,uacute,ucircumflex,udieresis" 	k="41" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="v" 	k="123" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="w" 	k="123" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="y,yacute,ydieresis" 	k="102" />
<hkern g1="C,Ccedilla" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="51" />
<hkern g1="C,Ccedilla" 	g2="AE" 	k="31" />
<hkern g1="C,Ccedilla" 	g2="J" 	k="-20" />
<hkern g1="C,Ccedilla" 	g2="T" 	k="31" />
<hkern g1="C,Ccedilla" 	g2="V" 	k="51" />
<hkern g1="C,Ccedilla" 	g2="W" 	k="31" />
<hkern g1="C,Ccedilla" 	g2="X" 	k="82" />
<hkern g1="C,Ccedilla" 	g2="Y,Yacute,Ydieresis" 	k="92" />
<hkern g1="C,Ccedilla" 	g2="asciicircum" 	k="-10" />
<hkern g1="C,Ccedilla" 	g2="backslash" 	k="51" />
<hkern g1="C,Ccedilla" 	g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="-20" />
<hkern g1="C,Ccedilla" 	g2="comma,period,ellipsis" 	k="20" />
<hkern g1="C,Ccedilla" 	g2="d,q" 	k="-20" />
<hkern g1="C,Ccedilla" 	g2="f" 	k="-20" />
<hkern g1="C,Ccedilla" 	g2="g" 	k="-20" />
<hkern g1="C,Ccedilla" 	g2="hyphen,endash,emdash" 	k="-20" />
<hkern g1="C,Ccedilla" 	g2="question" 	k="-31" />
<hkern g1="C,Ccedilla" 	g2="quotedbl,quotesingle" 	k="-10" />
<hkern g1="C,Ccedilla" 	g2="quoteleft,quotedblleft" 	k="-10" />
<hkern g1="C,Ccedilla" 	g2="quoteright,quotedblright" 	k="-10" />
<hkern g1="C,Ccedilla" 	g2="quotesinglbase,quotedblbase" 	k="20" />
<hkern g1="C,Ccedilla" 	g2="s" 	k="-10" />
<hkern g1="C,Ccedilla" 	g2="slash" 	k="51" />
<hkern g1="C,Ccedilla" 	g2="t" 	k="-20" />
<hkern g1="C,Ccedilla" 	g2="v" 	k="-20" />
<hkern g1="C,Ccedilla" 	g2="w" 	k="-20" />
<hkern g1="C,Ccedilla" 	g2="Z" 	k="10" />
<hkern g1="C,Ccedilla" 	g2="parenright" 	k="-31" />
<hkern g1="C,Ccedilla" 	g2="x" 	k="41" />
<hkern g1="C,Ccedilla" 	g2="z" 	k="31" />
<hkern g1="D,Eth" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="92" />
<hkern g1="D,Eth" 	g2="AE" 	k="92" />
<hkern g1="D,Eth" 	g2="J" 	k="10" />
<hkern g1="D,Eth" 	g2="T" 	k="61" />
<hkern g1="D,Eth" 	g2="V" 	k="72" />
<hkern g1="D,Eth" 	g2="W" 	k="61" />
<hkern g1="D,Eth" 	g2="X" 	k="123" />
<hkern g1="D,Eth" 	g2="Y,Yacute,Ydieresis" 	k="133" />
<hkern g1="D,Eth" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="10" />
<hkern g1="D,Eth" 	g2="asterisk" 	k="10" />
<hkern g1="D,Eth" 	g2="backslash" 	k="102" />
<hkern g1="D,Eth" 	g2="comma,period,ellipsis" 	k="72" />
<hkern g1="D,Eth" 	g2="quotedbl,quotesingle" 	k="-10" />
<hkern g1="D,Eth" 	g2="quoteleft,quotedblleft" 	k="10" />
<hkern g1="D,Eth" 	g2="quoteright,quotedblright" 	k="-10" />
<hkern g1="D,Eth" 	g2="quotesinglbase,quotedblbase" 	k="31" />
<hkern g1="D,Eth" 	g2="slash" 	k="102" />
<hkern g1="D,Eth" 	g2="t" 	k="-20" />
<hkern g1="D,Eth" 	g2="trademark" 	k="72" />
<hkern g1="D,Eth" 	g2="Z" 	k="51" />
<hkern g1="D,Eth" 	g2="x" 	k="41" />
<hkern g1="D,Eth" 	g2="z" 	k="20" />
<hkern g1="D,Eth" 	g2="j" 	k="-20" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="-10" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE" 	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="31" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE" 	g2="S" 	k="10" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE" 	g2="ampersand" 	k="10" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE" 	g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="31" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE" 	g2="colon,semicolon" 	k="-20" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE" 	g2="comma,period,ellipsis" 	k="-41" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE" 	g2="d,q" 	k="20" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE" 	g2="f" 	k="31" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE" 	g2="g" 	k="20" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE" 	g2="guilsinglleft" 	k="10" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE" 	g2="quoteleft,quotedblleft" 	k="20" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE" 	g2="quotesinglbase,quotedblbase" 	k="-10" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE" 	g2="s" 	k="10" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE" 	g2="t" 	k="31" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE" 	g2="u,ugrave,uacute,ucircumflex,udieresis" 	k="20" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE" 	g2="v" 	k="61" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE" 	g2="w" 	k="61" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE" 	g2="y,yacute,ydieresis" 	k="51" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE" 	g2="z" 	k="10" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE" 	g2="j" 	k="-20" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE" 	g2="germandbls" 	k="10" />
<hkern g1="F" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="133" />
<hkern g1="F" 	g2="AE" 	k="143" />
<hkern g1="F" 	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="41" />
<hkern g1="F" 	g2="J" 	k="102" />
<hkern g1="F" 	g2="S" 	k="20" />
<hkern g1="F" 	g2="T" 	k="-20" />
<hkern g1="F" 	g2="W" 	k="-31" />
<hkern g1="F" 	g2="Y,Yacute,Ydieresis" 	k="-20" />
<hkern g1="F" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="102" />
<hkern g1="F" 	g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="92" />
<hkern g1="F" 	g2="colon,semicolon" 	k="31" />
<hkern g1="F" 	g2="comma,period,ellipsis" 	k="164" />
<hkern g1="F" 	g2="copyright" 	k="20" />
<hkern g1="F" 	g2="d,q" 	k="82" />
<hkern g1="F" 	g2="f" 	k="20" />
<hkern g1="F" 	g2="g" 	k="82" />
<hkern g1="F" 	g2="guilsinglleft" 	k="61" />
<hkern g1="F" 	g2="guilsinglright" 	k="10" />
<hkern g1="F" 	g2="hyphen,endash,emdash" 	k="51" />
<hkern g1="F" 	g2="quotedbl,quotesingle" 	k="-41" />
<hkern g1="F" 	g2="quoteleft,quotedblleft" 	k="-20" />
<hkern g1="F" 	g2="quoteright,quotedblright" 	k="-41" />
<hkern g1="F" 	g2="quotesinglbase,quotedblbase" 	k="51" />
<hkern g1="F" 	g2="s" 	k="72" />
<hkern g1="F" 	g2="t" 	k="20" />
<hkern g1="F" 	g2="u,ugrave,uacute,ucircumflex,udieresis" 	k="51" />
<hkern g1="F" 	g2="w" 	k="51" />
<hkern g1="F" 	g2="y,yacute,ydieresis" 	k="51" />
<hkern g1="F" 	g2="z" 	k="72" />
<hkern g1="F" 	g2="bracketright,braceright" 	k="-51" />
<hkern g1="F" 	g2="m,n,r,ntilde" 	k="51" />
<hkern g1="G" 	g2="T" 	k="61" />
<hkern g1="G" 	g2="V" 	k="72" />
<hkern g1="G" 	g2="W" 	k="51" />
<hkern g1="G" 	g2="X" 	k="10" />
<hkern g1="G" 	g2="Y,Yacute,Ydieresis" 	k="123" />
<hkern g1="G" 	g2="asterisk" 	k="10" />
<hkern g1="G" 	g2="backslash" 	k="102" />
<hkern g1="G" 	g2="comma,period,ellipsis" 	k="-20" />
<hkern g1="G" 	g2="quotedbl,quotesingle" 	k="10" />
<hkern g1="G" 	g2="quoteleft,quotedblleft" 	k="10" />
<hkern g1="G" 	g2="quoteright,quotedblright" 	k="10" />
<hkern g1="G" 	g2="quotesinglbase,quotedblbase" 	k="-10" />
<hkern g1="G" 	g2="s" 	k="-10" />
<hkern g1="G" 	g2="trademark" 	k="51" />
<hkern g1="G" 	g2="m,n,r,ntilde" 	k="-10" />
<hkern g1="G" 	g2="b,h,i,k,l,igrave,iacute,icircumflex,idieresis" 	k="-10" />
<hkern g1="G" 	g2="p" 	k="-10" />
<hkern g1="J" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="51" />
<hkern g1="J" 	g2="AE" 	k="41" />
<hkern g1="J" 	g2="X" 	k="20" />
<hkern g1="J" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="10" />
<hkern g1="J" 	g2="comma,period,ellipsis" 	k="31" />
<hkern g1="J" 	g2="question" 	k="-10" />
<hkern g1="J" 	g2="quotesinglbase,quotedblbase" 	k="10" />
<hkern g1="J" 	g2="slash" 	k="51" />
<hkern g1="J" 	g2="x" 	k="20" />
<hkern g1="J" 	g2="z" 	k="20" />
<hkern g1="K" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="-20" />
<hkern g1="K" 	g2="AE" 	k="-20" />
<hkern g1="K" 	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="133" />
<hkern g1="K" 	g2="J" 	k="72" />
<hkern g1="K" 	g2="S" 	k="82" />
<hkern g1="K" 	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	k="31" />
<hkern g1="K" 	g2="V" 	k="-20" />
<hkern g1="K" 	g2="W" 	k="-20" />
<hkern g1="K" 	g2="X" 	k="-20" />
<hkern g1="K" 	g2="Y,Yacute,Ydieresis" 	k="-10" />
<hkern g1="K" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="41" />
<hkern g1="K" 	g2="ampersand" 	k="61" />
<hkern g1="K" 	g2="asciicircum" 	k="72" />
<hkern g1="K" 	g2="asterisk" 	k="92" />
<hkern g1="K" 	g2="at" 	k="61" />
<hkern g1="K" 	g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="102" />
<hkern g1="K" 	g2="comma,period,ellipsis" 	k="-10" />
<hkern g1="K" 	g2="copyright" 	k="92" />
<hkern g1="K" 	g2="d,q" 	k="92" />
<hkern g1="K" 	g2="f" 	k="61" />
<hkern g1="K" 	g2="g" 	k="92" />
<hkern g1="K" 	g2="guilsinglleft" 	k="143" />
<hkern g1="K" 	g2="guilsinglright" 	k="41" />
<hkern g1="K" 	g2="hyphen,endash,emdash" 	k="143" />
<hkern g1="K" 	g2="periodcentered" 	k="41" />
<hkern g1="K" 	g2="question" 	k="92" />
<hkern g1="K" 	g2="quotedbl,quotesingle" 	k="20" />
<hkern g1="K" 	g2="quoteleft,quotedblleft" 	k="72" />
<hkern g1="K" 	g2="quoteright,quotedblright" 	k="20" />
<hkern g1="K" 	g2="quotesinglbase,quotedblbase" 	k="-10" />
<hkern g1="K" 	g2="registered" 	k="92" />
<hkern g1="K" 	g2="s" 	k="41" />
<hkern g1="K" 	g2="slash" 	k="-20" />
<hkern g1="K" 	g2="t" 	k="92" />
<hkern g1="K" 	g2="u,ugrave,uacute,ucircumflex,udieresis" 	k="61" />
<hkern g1="K" 	g2="v" 	k="123" />
<hkern g1="K" 	g2="w" 	k="123" />
<hkern g1="K" 	g2="y,yacute,ydieresis" 	k="102" />
<hkern g1="K" 	g2="bracketright,braceright" 	k="-10" />
<hkern g1="L" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="-20" />
<hkern g1="L" 	g2="AE" 	k="-41" />
<hkern g1="L" 	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="51" />
<hkern g1="L" 	g2="J" 	k="-20" />
<hkern g1="L" 	g2="S" 	k="10" />
<hkern g1="L" 	g2="T" 	k="205" />
<hkern g1="L" 	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	k="31" />
<hkern g1="L" 	g2="V" 	k="174" />
<hkern g1="L" 	g2="W" 	k="133" />
<hkern g1="L" 	g2="Y,Yacute,Ydieresis" 	k="215" />
<hkern g1="L" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="-20" />
<hkern g1="L" 	g2="ampersand" 	k="-20" />
<hkern g1="L" 	g2="asciicircum" 	k="20" />
<hkern g1="L" 	g2="asterisk" 	k="164" />
<hkern g1="L" 	g2="at" 	k="10" />
<hkern g1="L" 	g2="backslash" 	k="195" />
<hkern g1="L" 	g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="10" />
<hkern g1="L" 	g2="colon,semicolon" 	k="-41" />
<hkern g1="L" 	g2="comma,period,ellipsis" 	k="-61" />
<hkern g1="L" 	g2="copyright" 	k="41" />
<hkern g1="L" 	g2="d,q" 	k="10" />
<hkern g1="L" 	g2="f" 	k="51" />
<hkern g1="L" 	g2="g" 	k="10" />
<hkern g1="L" 	g2="guilsinglleft" 	k="20" />
<hkern g1="L" 	g2="guilsinglright" 	k="-20" />
<hkern g1="L" 	g2="hyphen,endash,emdash" 	k="20" />
<hkern g1="L" 	g2="periodcentered" 	k="20" />
<hkern g1="L" 	g2="question" 	k="133" />
<hkern g1="L" 	g2="quotedbl,quotesingle" 	k="164" />
<hkern g1="L" 	g2="quoteleft,quotedblleft" 	k="164" />
<hkern g1="L" 	g2="quoteright,quotedblright" 	k="164" />
<hkern g1="L" 	g2="quotesinglbase,quotedblbase" 	k="-10" />
<hkern g1="L" 	g2="registered" 	k="41" />
<hkern g1="L" 	g2="s" 	k="-10" />
<hkern g1="L" 	g2="slash" 	k="-20" />
<hkern g1="L" 	g2="t" 	k="51" />
<hkern g1="L" 	g2="trademark" 	k="215" />
<hkern g1="L" 	g2="v" 	k="102" />
<hkern g1="L" 	g2="w" 	k="102" />
<hkern g1="L" 	g2="y,yacute,ydieresis" 	k="102" />
<hkern g1="L" 	g2="Z" 	k="-31" />
<hkern g1="L" 	g2="z" 	k="-20" />
<hkern g1="L" 	g2="j" 	k="-20" />
<hkern g1="L" 	g2="m,n,r,ntilde" 	k="-20" />
<hkern g1="L" 	g2="b,h,i,k,l,igrave,iacute,icircumflex,idieresis" 	k="-20" />
<hkern g1="L" 	g2="p" 	k="-20" />
<hkern g1="O,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="92" />
<hkern g1="O,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="AE" 	k="92" />
<hkern g1="O,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="J" 	k="10" />
<hkern g1="O,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="T" 	k="61" />
<hkern g1="O,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="V" 	k="72" />
<hkern g1="O,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="W" 	k="61" />
<hkern g1="O,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="X" 	k="123" />
<hkern g1="O,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="Y,Yacute,Ydieresis" 	k="133" />
<hkern g1="O,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="10" />
<hkern g1="O,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="asterisk" 	k="10" />
<hkern g1="O,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="backslash" 	k="102" />
<hkern g1="O,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="comma,period,ellipsis" 	k="72" />
<hkern g1="O,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="quotedbl,quotesingle" 	k="-10" />
<hkern g1="O,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="quoteleft,quotedblleft" 	k="10" />
<hkern g1="O,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="quoteright,quotedblright" 	k="-10" />
<hkern g1="O,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="quotesinglbase,quotedblbase" 	k="31" />
<hkern g1="O,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="slash" 	k="102" />
<hkern g1="O,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="t" 	k="-20" />
<hkern g1="O,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="trademark" 	k="72" />
<hkern g1="O,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="Z" 	k="51" />
<hkern g1="O,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="x" 	k="41" />
<hkern g1="O,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="z" 	k="20" />
<hkern g1="O,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="j" 	k="-20" />
<hkern g1="P" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="143" />
<hkern g1="P" 	g2="AE" 	k="133" />
<hkern g1="P" 	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="-20" />
<hkern g1="P" 	g2="J" 	k="92" />
<hkern g1="P" 	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	k="-10" />
<hkern g1="P" 	g2="W" 	k="10" />
<hkern g1="P" 	g2="Y,Yacute,Ydieresis" 	k="51" />
<hkern g1="P" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="51" />
<hkern g1="P" 	g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="41" />
<hkern g1="P" 	g2="comma,period,ellipsis" 	k="174" />
<hkern g1="P" 	g2="copyright" 	k="-10" />
<hkern g1="P" 	g2="d,q" 	k="31" />
<hkern g1="P" 	g2="f" 	k="-31" />
<hkern g1="P" 	g2="g" 	k="31" />
<hkern g1="P" 	g2="guilsinglleft" 	k="51" />
<hkern g1="P" 	g2="hyphen,endash,emdash" 	k="31" />
<hkern g1="P" 	g2="quotedbl,quotesingle" 	k="-41" />
<hkern g1="P" 	g2="quoteleft,quotedblleft" 	k="-31" />
<hkern g1="P" 	g2="quoteright,quotedblright" 	k="-41" />
<hkern g1="P" 	g2="quotesinglbase,quotedblbase" 	k="61" />
<hkern g1="P" 	g2="s" 	k="10" />
<hkern g1="P" 	g2="t" 	k="-20" />
<hkern g1="P" 	g2="w" 	k="-31" />
<hkern g1="P" 	g2="y,yacute,ydieresis" 	k="-31" />
<hkern g1="P" 	g2="Z" 	k="31" />
<hkern g1="P" 	g2="z" 	k="10" />
<hkern g1="P" 	g2="bracketright,braceright" 	k="20" />
<hkern g1="Q" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="10" />
<hkern g1="Q" 	g2="AE" 	k="10" />
<hkern g1="Q" 	g2="J" 	k="10" />
<hkern g1="Q" 	g2="T" 	k="61" />
<hkern g1="Q" 	g2="W" 	k="61" />
<hkern g1="Q" 	g2="Y,Yacute,Ydieresis" 	k="133" />
<hkern g1="Q" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="10" />
<hkern g1="Q" 	g2="comma,period,ellipsis" 	k="41" />
<hkern g1="Q" 	g2="quotedbl,quotesingle" 	k="-10" />
<hkern g1="Q" 	g2="quoteleft,quotedblleft" 	k="10" />
<hkern g1="Q" 	g2="quoteright,quotedblright" 	k="-10" />
<hkern g1="Q" 	g2="quotesinglbase,quotedblbase" 	k="41" />
<hkern g1="Q" 	g2="t" 	k="-20" />
<hkern g1="Q" 	g2="Z" 	k="31" />
<hkern g1="Q" 	g2="z" 	k="10" />
<hkern g1="Q" 	g2="j" 	k="-20" />
<hkern g1="R" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="-20" />
<hkern g1="R" 	g2="AE" 	k="-20" />
<hkern g1="R" 	g2="T" 	k="10" />
<hkern g1="R" 	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	k="-10" />
<hkern g1="R" 	g2="V" 	k="20" />
<hkern g1="R" 	g2="W" 	k="20" />
<hkern g1="R" 	g2="Y,Yacute,Ydieresis" 	k="51" />
<hkern g1="R" 	g2="asciicircum" 	k="10" />
<hkern g1="R" 	g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="10" />
<hkern g1="R" 	g2="comma,period,ellipsis" 	k="-31" />
<hkern g1="R" 	g2="d,q" 	k="10" />
<hkern g1="R" 	g2="f" 	k="-20" />
<hkern g1="R" 	g2="g" 	k="10" />
<hkern g1="R" 	g2="guilsinglleft" 	k="10" />
<hkern g1="R" 	g2="hyphen,endash,emdash" 	k="10" />
<hkern g1="R" 	g2="question" 	k="-10" />
<hkern g1="R" 	g2="quotedbl,quotesingle" 	k="-20" />
<hkern g1="R" 	g2="quoteleft,quotedblleft" 	k="-20" />
<hkern g1="R" 	g2="quoteright,quotedblright" 	k="-20" />
<hkern g1="R" 	g2="quotesinglbase,quotedblbase" 	k="-10" />
<hkern g1="R" 	g2="slash" 	k="-20" />
<hkern g1="R" 	g2="t" 	k="-10" />
<hkern g1="R" 	g2="Z" 	k="-10" />
<hkern g1="R" 	g2="z" 	k="-20" />
<hkern g1="R" 	g2="j" 	k="-10" />
<hkern g1="R" 	g2="m,n,r,ntilde" 	k="-10" />
<hkern g1="R" 	g2="b,h,i,k,l,igrave,iacute,icircumflex,idieresis" 	k="-10" />
<hkern g1="R" 	g2="p" 	k="-10" />
<hkern g1="S" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="31" />
<hkern g1="S" 	g2="AE" 	k="20" />
<hkern g1="S" 	g2="J" 	k="-20" />
<hkern g1="S" 	g2="S" 	k="-20" />
<hkern g1="S" 	g2="T" 	k="51" />
<hkern g1="S" 	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	k="-10" />
<hkern g1="S" 	g2="V" 	k="51" />
<hkern g1="S" 	g2="W" 	k="41" />
<hkern g1="S" 	g2="X" 	k="61" />
<hkern g1="S" 	g2="Y,Yacute,Ydieresis" 	k="102" />
<hkern g1="S" 	g2="asciicircum" 	k="-10" />
<hkern g1="S" 	g2="asterisk" 	k="10" />
<hkern g1="S" 	g2="backslash" 	k="72" />
<hkern g1="S" 	g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="-10" />
<hkern g1="S" 	g2="comma,period,ellipsis" 	k="10" />
<hkern g1="S" 	g2="d,q" 	k="-10" />
<hkern g1="S" 	g2="f" 	k="20" />
<hkern g1="S" 	g2="g" 	k="-10" />
<hkern g1="S" 	g2="hyphen,endash,emdash" 	k="-20" />
<hkern g1="S" 	g2="question" 	k="20" />
<hkern g1="S" 	g2="quotedbl,quotesingle" 	k="41" />
<hkern g1="S" 	g2="quoteleft,quotedblleft" 	k="31" />
<hkern g1="S" 	g2="quoteright,quotedblright" 	k="41" />
<hkern g1="S" 	g2="quotesinglbase,quotedblbase" 	k="10" />
<hkern g1="S" 	g2="slash" 	k="20" />
<hkern g1="S" 	g2="t" 	k="20" />
<hkern g1="S" 	g2="trademark" 	k="41" />
<hkern g1="S" 	g2="v" 	k="41" />
<hkern g1="S" 	g2="w" 	k="41" />
<hkern g1="S" 	g2="y,yacute,ydieresis" 	k="31" />
<hkern g1="S" 	g2="Z" 	k="10" />
<hkern g1="S" 	g2="x" 	k="61" />
<hkern g1="S" 	g2="z" 	k="31" />
<hkern g1="S" 	g2="bracketright,braceright" 	k="20" />
<hkern g1="T" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="184" />
<hkern g1="T" 	g2="AE" 	k="184" />
<hkern g1="T" 	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="61" />
<hkern g1="T" 	g2="J" 	k="215" />
<hkern g1="T" 	g2="S" 	k="20" />
<hkern g1="T" 	g2="T" 	k="-31" />
<hkern g1="T" 	g2="V" 	k="-41" />
<hkern g1="T" 	g2="W" 	k="-41" />
<hkern g1="T" 	g2="X" 	k="20" />
<hkern g1="T" 	g2="Y,Yacute,Ydieresis" 	k="-31" />
<hkern g1="T" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="236" />
<hkern g1="T" 	g2="ampersand" 	k="113" />
<hkern g1="T" 	g2="asciicircum" 	k="82" />
<hkern g1="T" 	g2="at" 	k="82" />
<hkern g1="T" 	g2="backslash" 	k="-20" />
<hkern g1="T" 	g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="246" />
<hkern g1="T" 	g2="colon,semicolon" 	k="51" />
<hkern g1="T" 	g2="comma,period,ellipsis" 	k="205" />
<hkern g1="T" 	g2="copyright" 	k="72" />
<hkern g1="T" 	g2="d,q" 	k="215" />
<hkern g1="T" 	g2="f" 	k="51" />
<hkern g1="T" 	g2="g" 	k="215" />
<hkern g1="T" 	g2="guilsinglleft" 	k="164" />
<hkern g1="T" 	g2="guilsinglright" 	k="82" />
<hkern g1="T" 	g2="hyphen,endash,emdash" 	k="164" />
<hkern g1="T" 	g2="periodcentered" 	k="143" />
<hkern g1="T" 	g2="question" 	k="-20" />
<hkern g1="T" 	g2="quotedbl,quotesingle" 	k="-20" />
<hkern g1="T" 	g2="quoteleft,quotedblleft" 	k="-20" />
<hkern g1="T" 	g2="quoteright,quotedblright" 	k="-20" />
<hkern g1="T" 	g2="quotesinglbase,quotedblbase" 	k="51" />
<hkern g1="T" 	g2="registered" 	k="72" />
<hkern g1="T" 	g2="s" 	k="205" />
<hkern g1="T" 	g2="slash" 	k="154" />
<hkern g1="T" 	g2="t" 	k="31" />
<hkern g1="T" 	g2="u,ugrave,uacute,ucircumflex,udieresis" 	k="133" />
<hkern g1="T" 	g2="v" 	k="143" />
<hkern g1="T" 	g2="w" 	k="143" />
<hkern g1="T" 	g2="y,yacute,ydieresis" 	k="143" />
<hkern g1="T" 	g2="Z" 	k="10" />
<hkern g1="T" 	g2="parenright" 	k="-10" />
<hkern g1="T" 	g2="x" 	k="143" />
<hkern g1="T" 	g2="z" 	k="123" />
<hkern g1="T" 	g2="germandbls" 	k="31" />
<hkern g1="T" 	g2="bracketright,braceright" 	k="-10" />
<hkern g1="T" 	g2="m,n,r,ntilde" 	k="133" />
<hkern g1="T" 	g2="p" 	k="133" />
<hkern g1="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="61" />
<hkern g1="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	g2="AE" 	k="51" />
<hkern g1="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	g2="S" 	k="-10" />
<hkern g1="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	g2="V" 	k="-20" />
<hkern g1="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	g2="X" 	k="20" />
<hkern g1="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="20" />
<hkern g1="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	g2="comma,period,ellipsis" 	k="31" />
<hkern g1="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	g2="question" 	k="-10" />
<hkern g1="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	g2="quotesinglbase,quotedblbase" 	k="20" />
<hkern g1="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	g2="slash" 	k="41" />
<hkern g1="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	g2="Z" 	k="20" />
<hkern g1="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	g2="x" 	k="20" />
<hkern g1="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	g2="z" 	k="20" />
<hkern g1="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	g2="bracketright,braceright" 	k="-10" />
<hkern g1="V" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="164" />
<hkern g1="V" 	g2="AE" 	k="164" />
<hkern g1="V" 	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="72" />
<hkern g1="V" 	g2="J" 	k="143" />
<hkern g1="V" 	g2="S" 	k="31" />
<hkern g1="V" 	g2="T" 	k="-41" />
<hkern g1="V" 	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	k="-20" />
<hkern g1="V" 	g2="W" 	k="-41" />
<hkern g1="V" 	g2="Y,Yacute,Ydieresis" 	k="-41" />
<hkern g1="V" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="143" />
<hkern g1="V" 	g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="154" />
<hkern g1="V" 	g2="colon,semicolon" 	k="51" />
<hkern g1="V" 	g2="comma,period,ellipsis" 	k="174" />
<hkern g1="V" 	g2="copyright" 	k="72" />
<hkern g1="V" 	g2="d,q" 	k="133" />
<hkern g1="V" 	g2="f" 	k="51" />
<hkern g1="V" 	g2="g" 	k="133" />
<hkern g1="V" 	g2="guilsinglleft" 	k="113" />
<hkern g1="V" 	g2="guilsinglright" 	k="72" />
<hkern g1="V" 	g2="hyphen,endash,emdash" 	k="133" />
<hkern g1="V" 	g2="quotedbl,quotesingle" 	k="-20" />
<hkern g1="V" 	g2="quoteright,quotedblright" 	k="-20" />
<hkern g1="V" 	g2="quotesinglbase,quotedblbase" 	k="51" />
<hkern g1="V" 	g2="s" 	k="102" />
<hkern g1="V" 	g2="t" 	k="31" />
<hkern g1="V" 	g2="u,ugrave,uacute,ucircumflex,udieresis" 	k="72" />
<hkern g1="V" 	g2="w" 	k="51" />
<hkern g1="V" 	g2="y,yacute,ydieresis" 	k="41" />
<hkern g1="V" 	g2="Z" 	k="20" />
<hkern g1="V" 	g2="z" 	k="92" />
<hkern g1="V" 	g2="bracketright,braceright" 	k="-20" />
<hkern g1="V" 	g2="m,n,r,ntilde" 	k="72" />
<hkern g1="W" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="123" />
<hkern g1="W" 	g2="AE" 	k="133" />
<hkern g1="W" 	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="61" />
<hkern g1="W" 	g2="J" 	k="123" />
<hkern g1="W" 	g2="S" 	k="20" />
<hkern g1="W" 	g2="T" 	k="-41" />
<hkern g1="W" 	g2="V" 	k="-41" />
<hkern g1="W" 	g2="W" 	k="-41" />
<hkern g1="W" 	g2="Y,Yacute,Ydieresis" 	k="-41" />
<hkern g1="W" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="133" />
<hkern g1="W" 	g2="ampersand" 	k="113" />
<hkern g1="W" 	g2="asciicircum" 	k="51" />
<hkern g1="W" 	g2="at" 	k="72" />
<hkern g1="W" 	g2="backslash" 	k="-31" />
<hkern g1="W" 	g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="123" />
<hkern g1="W" 	g2="colon,semicolon" 	k="31" />
<hkern g1="W" 	g2="comma,period,ellipsis" 	k="143" />
<hkern g1="W" 	g2="copyright" 	k="61" />
<hkern g1="W" 	g2="d,q" 	k="113" />
<hkern g1="W" 	g2="f" 	k="20" />
<hkern g1="W" 	g2="g" 	k="113" />
<hkern g1="W" 	g2="guilsinglleft" 	k="92" />
<hkern g1="W" 	g2="guilsinglright" 	k="51" />
<hkern g1="W" 	g2="hyphen,endash,emdash" 	k="102" />
<hkern g1="W" 	g2="periodcentered" 	k="72" />
<hkern g1="W" 	g2="question" 	k="-10" />
<hkern g1="W" 	g2="quotedbl,quotesingle" 	k="-20" />
<hkern g1="W" 	g2="quoteright,quotedblright" 	k="-20" />
<hkern g1="W" 	g2="quotesinglbase,quotedblbase" 	k="41" />
<hkern g1="W" 	g2="registered" 	k="61" />
<hkern g1="W" 	g2="s" 	k="102" />
<hkern g1="W" 	g2="slash" 	k="143" />
<hkern g1="W" 	g2="t" 	k="31" />
<hkern g1="W" 	g2="u,ugrave,uacute,ucircumflex,udieresis" 	k="72" />
<hkern g1="W" 	g2="v" 	k="31" />
<hkern g1="W" 	g2="w" 	k="31" />
<hkern g1="W" 	g2="y,yacute,ydieresis" 	k="20" />
<hkern g1="W" 	g2="Z" 	k="20" />
<hkern g1="W" 	g2="parenright" 	k="-20" />
<hkern g1="W" 	g2="x" 	k="82" />
<hkern g1="W" 	g2="z" 	k="82" />
<hkern g1="W" 	g2="germandbls" 	k="41" />
<hkern g1="W" 	g2="bracketright,braceright" 	k="-20" />
<hkern g1="W" 	g2="m,n,r,ntilde" 	k="61" />
<hkern g1="W" 	g2="p" 	k="61" />
<hkern g1="X" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="-20" />
<hkern g1="X" 	g2="AE" 	k="-31" />
<hkern g1="X" 	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="123" />
<hkern g1="X" 	g2="J" 	k="51" />
<hkern g1="X" 	g2="S" 	k="61" />
<hkern g1="X" 	g2="T" 	k="20" />
<hkern g1="X" 	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	k="20" />
<hkern g1="X" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="41" />
<hkern g1="X" 	g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="102" />
<hkern g1="X" 	g2="comma,period,ellipsis" 	k="-20" />
<hkern g1="X" 	g2="copyright" 	k="113" />
<hkern g1="X" 	g2="d,q" 	k="82" />
<hkern g1="X" 	g2="f" 	k="41" />
<hkern g1="X" 	g2="g" 	k="31" />
<hkern g1="X" 	g2="guilsinglleft" 	k="82" />
<hkern g1="X" 	g2="guilsinglright" 	k="20" />
<hkern g1="X" 	g2="hyphen,endash,emdash" 	k="102" />
<hkern g1="X" 	g2="quotedbl,quotesingle" 	k="20" />
<hkern g1="X" 	g2="quoteleft,quotedblleft" 	k="72" />
<hkern g1="X" 	g2="quoteright,quotedblright" 	k="20" />
<hkern g1="X" 	g2="quotesinglbase,quotedblbase" 	k="-10" />
<hkern g1="X" 	g2="s" 	k="51" />
<hkern g1="X" 	g2="t" 	k="72" />
<hkern g1="X" 	g2="u,ugrave,uacute,ucircumflex,udieresis" 	k="61" />
<hkern g1="X" 	g2="w" 	k="113" />
<hkern g1="X" 	g2="y,yacute,ydieresis" 	k="92" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="205" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="AE" 	k="184" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="133" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="J" 	k="225" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="S" 	k="72" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="T" 	k="-31" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="V" 	k="-41" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="W" 	k="-41" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="Y,Yacute,Ydieresis" 	k="-41" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="246" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="ampersand" 	k="154" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="asciicircum" 	k="113" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="asterisk" 	k="41" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="at" 	k="164" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="backslash" 	k="-20" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="236" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="colon,semicolon" 	k="113" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="comma,period,ellipsis" 	k="236" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="copyright" 	k="133" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="d,q" 	k="225" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="f" 	k="92" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="g" 	k="225" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="guilsinglleft" 	k="195" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="guilsinglright" 	k="133" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="hyphen,endash,emdash" 	k="225" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="periodcentered" 	k="164" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="question" 	k="31" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="quotedbl,quotesingle" 	k="-20" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="quoteleft,quotedblleft" 	k="31" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="quoteright,quotedblright" 	k="-20" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="quotesinglbase,quotedblbase" 	k="61" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="registered" 	k="133" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="s" 	k="205" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="slash" 	k="195" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="t" 	k="82" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="trademark" 	k="-10" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="u,ugrave,uacute,ucircumflex,udieresis" 	k="154" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="v" 	k="123" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="w" 	k="123" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="y,yacute,ydieresis" 	k="123" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="Z" 	k="41" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="parenright" 	k="-20" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="x" 	k="154" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="z" 	k="164" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="j" 	k="10" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="germandbls" 	k="92" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="bracketright,braceright" 	k="-20" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="m,n,r,ntilde" 	k="154" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="b,h,i,k,l,igrave,iacute,icircumflex,idieresis" 	k="10" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="p" 	k="154" />
<hkern g1="Z" 	g2="AE" 	k="-20" />
<hkern g1="Z" 	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="61" />
<hkern g1="Z" 	g2="S" 	k="10" />
<hkern g1="Z" 	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	k="20" />
<hkern g1="Z" 	g2="V" 	k="10" />
<hkern g1="Z" 	g2="Y,Yacute,Ydieresis" 	k="20" />
<hkern g1="Z" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="10" />
<hkern g1="Z" 	g2="ampersand" 	k="20" />
<hkern g1="Z" 	g2="asciicircum" 	k="20" />
<hkern g1="Z" 	g2="asterisk" 	k="10" />
<hkern g1="Z" 	g2="at" 	k="10" />
<hkern g1="Z" 	g2="backslash" 	k="10" />
<hkern g1="Z" 	g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="31" />
<hkern g1="Z" 	g2="colon,semicolon" 	k="-10" />
<hkern g1="Z" 	g2="comma,period,ellipsis" 	k="-20" />
<hkern g1="Z" 	g2="copyright" 	k="41" />
<hkern g1="Z" 	g2="d,q" 	k="20" />
<hkern g1="Z" 	g2="f" 	k="31" />
<hkern g1="Z" 	g2="g" 	k="20" />
<hkern g1="Z" 	g2="guilsinglleft" 	k="20" />
<hkern g1="Z" 	g2="hyphen,endash,emdash" 	k="20" />
<hkern g1="Z" 	g2="periodcentered" 	k="20" />
<hkern g1="Z" 	g2="question" 	k="10" />
<hkern g1="Z" 	g2="quoteleft,quotedblleft" 	k="20" />
<hkern g1="Z" 	g2="quotesinglbase,quotedblbase" 	k="-10" />
<hkern g1="Z" 	g2="registered" 	k="41" />
<hkern g1="Z" 	g2="s" 	k="10" />
<hkern g1="Z" 	g2="t" 	k="31" />
<hkern g1="Z" 	g2="u,ugrave,uacute,ucircumflex,udieresis" 	k="10" />
<hkern g1="Z" 	g2="v" 	k="61" />
<hkern g1="Z" 	g2="w" 	k="61" />
<hkern g1="Z" 	g2="y,yacute,ydieresis" 	k="61" />
<hkern g1="Z" 	g2="z" 	k="10" />
<hkern g1="Z" 	g2="germandbls" 	k="10" />
<hkern g1="numbersign,dollar,percent,parenright,less,equal,greater,question,H,I,M,N,bracketright,asciicircum,underscore,grave,bar,braceright,asciitilde,cent,sterling,currency,yen,brokenbar,section,dieresis,logicalnot,registered,macron,degree,plusminus,acute,paragraph,cedilla,Igrave,Iacute,Icircumflex,Idieresis,Ntilde,multiply,Thorn,eth,circumflex,tilde,bullet,trademark" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="20" />
<hkern g1="numbersign,dollar,percent,parenright,less,equal,greater,question,H,I,M,N,bracketright,asciicircum,underscore,grave,bar,braceright,asciitilde,cent,sterling,currency,yen,brokenbar,section,dieresis,logicalnot,registered,macron,degree,plusminus,acute,paragraph,cedilla,Igrave,Iacute,Icircumflex,Idieresis,Ntilde,multiply,Thorn,eth,circumflex,tilde,bullet,trademark" 	g2="AE" 	k="20" />
<hkern g1="numbersign,dollar,percent,parenright,less,equal,greater,question,H,I,M,N,bracketright,asciicircum,underscore,grave,bar,braceright,asciitilde,cent,sterling,currency,yen,brokenbar,section,dieresis,logicalnot,registered,macron,degree,plusminus,acute,paragraph,cedilla,Igrave,Iacute,Icircumflex,Idieresis,Ntilde,multiply,Thorn,eth,circumflex,tilde,bullet,trademark" 	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="-10" />
<hkern g1="numbersign,dollar,percent,parenright,less,equal,greater,question,H,I,M,N,bracketright,asciicircum,underscore,grave,bar,braceright,asciitilde,cent,sterling,currency,yen,brokenbar,section,dieresis,logicalnot,registered,macron,degree,plusminus,acute,paragraph,cedilla,Igrave,Iacute,Icircumflex,Idieresis,Ntilde,multiply,Thorn,eth,circumflex,tilde,bullet,trademark" 	g2="J" 	k="-31" />
<hkern g1="numbersign,dollar,percent,parenright,less,equal,greater,question,H,I,M,N,bracketright,asciicircum,underscore,grave,bar,braceright,asciitilde,cent,sterling,currency,yen,brokenbar,section,dieresis,logicalnot,registered,macron,degree,plusminus,acute,paragraph,cedilla,Igrave,Iacute,Icircumflex,Idieresis,Ntilde,multiply,Thorn,eth,circumflex,tilde,bullet,trademark" 	g2="S" 	k="-20" />
<hkern g1="numbersign,dollar,percent,parenright,less,equal,greater,question,H,I,M,N,bracketright,asciicircum,underscore,grave,bar,braceright,asciitilde,cent,sterling,currency,yen,brokenbar,section,dieresis,logicalnot,registered,macron,degree,plusminus,acute,paragraph,cedilla,Igrave,Iacute,Icircumflex,Idieresis,Ntilde,multiply,Thorn,eth,circumflex,tilde,bullet,trademark" 	g2="T" 	k="31" />
<hkern g1="numbersign,dollar,percent,parenright,less,equal,greater,question,H,I,M,N,bracketright,asciicircum,underscore,grave,bar,braceright,asciitilde,cent,sterling,currency,yen,brokenbar,section,dieresis,logicalnot,registered,macron,degree,plusminus,acute,paragraph,cedilla,Igrave,Iacute,Icircumflex,Idieresis,Ntilde,multiply,Thorn,eth,circumflex,tilde,bullet,trademark" 	g2="W" 	k="41" />
<hkern g1="numbersign,dollar,percent,parenright,less,equal,greater,question,H,I,M,N,bracketright,asciicircum,underscore,grave,bar,braceright,asciitilde,cent,sterling,currency,yen,brokenbar,section,dieresis,logicalnot,registered,macron,degree,plusminus,acute,paragraph,cedilla,Igrave,Iacute,Icircumflex,Idieresis,Ntilde,multiply,Thorn,eth,circumflex,tilde,bullet,trademark" 	g2="Y,Yacute,Ydieresis" 	k="61" />
<hkern g1="numbersign,dollar,percent,parenright,less,equal,greater,question,H,I,M,N,bracketright,asciicircum,underscore,grave,bar,braceright,asciitilde,cent,sterling,currency,yen,brokenbar,section,dieresis,logicalnot,registered,macron,degree,plusminus,acute,paragraph,cedilla,Igrave,Iacute,Icircumflex,Idieresis,Ntilde,multiply,Thorn,eth,circumflex,tilde,bullet,trademark" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="-10" />
<hkern g1="numbersign,dollar,percent,parenright,less,equal,greater,question,H,I,M,N,bracketright,asciicircum,underscore,grave,bar,braceright,asciitilde,cent,sterling,currency,yen,brokenbar,section,dieresis,logicalnot,registered,macron,degree,plusminus,acute,paragraph,cedilla,Igrave,Iacute,Icircumflex,Idieresis,Ntilde,multiply,Thorn,eth,circumflex,tilde,bullet,trademark" 	g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="-20" />
<hkern g1="numbersign,dollar,percent,parenright,less,equal,greater,question,H,I,M,N,bracketright,asciicircum,underscore,grave,bar,braceright,asciitilde,cent,sterling,currency,yen,brokenbar,section,dieresis,logicalnot,registered,macron,degree,plusminus,acute,paragraph,cedilla,Igrave,Iacute,Icircumflex,Idieresis,Ntilde,multiply,Thorn,eth,circumflex,tilde,bullet,trademark" 	g2="colon,semicolon" 	k="-10" />
<hkern g1="numbersign,dollar,percent,parenright,less,equal,greater,question,H,I,M,N,bracketright,asciicircum,underscore,grave,bar,braceright,asciitilde,cent,sterling,currency,yen,brokenbar,section,dieresis,logicalnot,registered,macron,degree,plusminus,acute,paragraph,cedilla,Igrave,Iacute,Icircumflex,Idieresis,Ntilde,multiply,Thorn,eth,circumflex,tilde,bullet,trademark" 	g2="comma,period,ellipsis" 	k="-20" />
<hkern g1="numbersign,dollar,percent,parenright,less,equal,greater,question,H,I,M,N,bracketright,asciicircum,underscore,grave,bar,braceright,asciitilde,cent,sterling,currency,yen,brokenbar,section,dieresis,logicalnot,registered,macron,degree,plusminus,acute,paragraph,cedilla,Igrave,Iacute,Icircumflex,Idieresis,Ntilde,multiply,Thorn,eth,circumflex,tilde,bullet,trademark" 	g2="d,q" 	k="-20" />
<hkern g1="numbersign,dollar,percent,parenright,less,equal,greater,question,H,I,M,N,bracketright,asciicircum,underscore,grave,bar,braceright,asciitilde,cent,sterling,currency,yen,brokenbar,section,dieresis,logicalnot,registered,macron,degree,plusminus,acute,paragraph,cedilla,Igrave,Iacute,Icircumflex,Idieresis,Ntilde,multiply,Thorn,eth,circumflex,tilde,bullet,trademark" 	g2="g" 	k="-20" />
<hkern g1="numbersign,dollar,percent,parenright,less,equal,greater,question,H,I,M,N,bracketright,asciicircum,underscore,grave,bar,braceright,asciitilde,cent,sterling,currency,yen,brokenbar,section,dieresis,logicalnot,registered,macron,degree,plusminus,acute,paragraph,cedilla,Igrave,Iacute,Icircumflex,Idieresis,Ntilde,multiply,Thorn,eth,circumflex,tilde,bullet,trademark" 	g2="guilsinglleft" 	k="-20" />
<hkern g1="numbersign,dollar,percent,parenright,less,equal,greater,question,H,I,M,N,bracketright,asciicircum,underscore,grave,bar,braceright,asciitilde,cent,sterling,currency,yen,brokenbar,section,dieresis,logicalnot,registered,macron,degree,plusminus,acute,paragraph,cedilla,Igrave,Iacute,Icircumflex,Idieresis,Ntilde,multiply,Thorn,eth,circumflex,tilde,bullet,trademark" 	g2="guilsinglright" 	k="-10" />
<hkern g1="numbersign,dollar,percent,parenright,less,equal,greater,question,H,I,M,N,bracketright,asciicircum,underscore,grave,bar,braceright,asciitilde,cent,sterling,currency,yen,brokenbar,section,dieresis,logicalnot,registered,macron,degree,plusminus,acute,paragraph,cedilla,Igrave,Iacute,Icircumflex,Idieresis,Ntilde,multiply,Thorn,eth,circumflex,tilde,bullet,trademark" 	g2="hyphen,endash,emdash" 	k="-31" />
<hkern g1="numbersign,dollar,percent,parenright,less,equal,greater,question,H,I,M,N,bracketright,asciicircum,underscore,grave,bar,braceright,asciitilde,cent,sterling,currency,yen,brokenbar,section,dieresis,logicalnot,registered,macron,degree,plusminus,acute,paragraph,cedilla,Igrave,Iacute,Icircumflex,Idieresis,Ntilde,multiply,Thorn,eth,circumflex,tilde,bullet,trademark" 	g2="quotedbl,quotesingle" 	k="10" />
<hkern g1="numbersign,dollar,percent,parenright,less,equal,greater,question,H,I,M,N,bracketright,asciicircum,underscore,grave,bar,braceright,asciitilde,cent,sterling,currency,yen,brokenbar,section,dieresis,logicalnot,registered,macron,degree,plusminus,acute,paragraph,cedilla,Igrave,Iacute,Icircumflex,Idieresis,Ntilde,multiply,Thorn,eth,circumflex,tilde,bullet,trademark" 	g2="quoteleft,quotedblleft" 	k="20" />
<hkern g1="numbersign,dollar,percent,parenright,less,equal,greater,question,H,I,M,N,bracketright,asciicircum,underscore,grave,bar,braceright,asciitilde,cent,sterling,currency,yen,brokenbar,section,dieresis,logicalnot,registered,macron,degree,plusminus,acute,paragraph,cedilla,Igrave,Iacute,Icircumflex,Idieresis,Ntilde,multiply,Thorn,eth,circumflex,tilde,bullet,trademark" 	g2="quoteright,quotedblright" 	k="10" />
<hkern g1="numbersign,dollar,percent,parenright,less,equal,greater,question,H,I,M,N,bracketright,asciicircum,underscore,grave,bar,braceright,asciitilde,cent,sterling,currency,yen,brokenbar,section,dieresis,logicalnot,registered,macron,degree,plusminus,acute,paragraph,cedilla,Igrave,Iacute,Icircumflex,Idieresis,Ntilde,multiply,Thorn,eth,circumflex,tilde,bullet,trademark" 	g2="quotesinglbase,quotedblbase" 	k="-10" />
<hkern g1="numbersign,dollar,percent,parenright,less,equal,greater,question,H,I,M,N,bracketright,asciicircum,underscore,grave,bar,braceright,asciitilde,cent,sterling,currency,yen,brokenbar,section,dieresis,logicalnot,registered,macron,degree,plusminus,acute,paragraph,cedilla,Igrave,Iacute,Icircumflex,Idieresis,Ntilde,multiply,Thorn,eth,circumflex,tilde,bullet,trademark" 	g2="s" 	k="-10" />
<hkern g1="numbersign,dollar,percent,parenright,less,equal,greater,question,H,I,M,N,bracketright,asciicircum,underscore,grave,bar,braceright,asciitilde,cent,sterling,currency,yen,brokenbar,section,dieresis,logicalnot,registered,macron,degree,plusminus,acute,paragraph,cedilla,Igrave,Iacute,Icircumflex,Idieresis,Ntilde,multiply,Thorn,eth,circumflex,tilde,bullet,trademark" 	g2="t" 	k="10" />
<hkern g1="numbersign,dollar,percent,parenright,less,equal,greater,question,H,I,M,N,bracketright,asciicircum,underscore,grave,bar,braceright,asciitilde,cent,sterling,currency,yen,brokenbar,section,dieresis,logicalnot,registered,macron,degree,plusminus,acute,paragraph,cedilla,Igrave,Iacute,Icircumflex,Idieresis,Ntilde,multiply,Thorn,eth,circumflex,tilde,bullet,trademark" 	g2="w" 	k="20" />
<hkern g1="numbersign,dollar,percent,parenright,less,equal,greater,question,H,I,M,N,bracketright,asciicircum,underscore,grave,bar,braceright,asciitilde,cent,sterling,currency,yen,brokenbar,section,dieresis,logicalnot,registered,macron,degree,plusminus,acute,paragraph,cedilla,Igrave,Iacute,Icircumflex,Idieresis,Ntilde,multiply,Thorn,eth,circumflex,tilde,bullet,trademark" 	g2="y,yacute,ydieresis" 	k="20" />
<hkern g1="numbersign,dollar,percent,parenright,less,equal,greater,question,H,I,M,N,bracketright,asciicircum,underscore,grave,bar,braceright,asciitilde,cent,sterling,currency,yen,brokenbar,section,dieresis,logicalnot,registered,macron,degree,plusminus,acute,paragraph,cedilla,Igrave,Iacute,Icircumflex,Idieresis,Ntilde,multiply,Thorn,eth,circumflex,tilde,bullet,trademark" 	g2="z" 	k="10" />
<hkern g1="numbersign,dollar,percent,parenright,less,equal,greater,question,H,I,M,N,bracketright,asciicircum,underscore,grave,bar,braceright,asciitilde,cent,sterling,currency,yen,brokenbar,section,dieresis,logicalnot,registered,macron,degree,plusminus,acute,paragraph,cedilla,Igrave,Iacute,Icircumflex,Idieresis,Ntilde,multiply,Thorn,eth,circumflex,tilde,bullet,trademark" 	g2="bracketright,braceright" 	k="10" />
<hkern g1="a,agrave,aacute,acircumflex,atilde,adieresis,aring" 	g2="asterisk" 	k="51" />
<hkern g1="a,agrave,aacute,acircumflex,atilde,adieresis,aring" 	g2="backslash" 	k="133" />
<hkern g1="a,agrave,aacute,acircumflex,atilde,adieresis,aring" 	g2="comma,period,ellipsis" 	k="-20" />
<hkern g1="a,agrave,aacute,acircumflex,atilde,adieresis,aring" 	g2="question" 	k="31" />
<hkern g1="a,agrave,aacute,acircumflex,atilde,adieresis,aring" 	g2="quotedbl,quotesingle" 	k="20" />
<hkern g1="a,agrave,aacute,acircumflex,atilde,adieresis,aring" 	g2="quoteleft,quotedblleft" 	k="31" />
<hkern g1="a,agrave,aacute,acircumflex,atilde,adieresis,aring" 	g2="quoteright,quotedblright" 	k="20" />
<hkern g1="a,agrave,aacute,acircumflex,atilde,adieresis,aring" 	g2="quotesinglbase,quotedblbase" 	k="-10" />
<hkern g1="a,agrave,aacute,acircumflex,atilde,adieresis,aring" 	g2="t" 	k="20" />
<hkern g1="a,agrave,aacute,acircumflex,atilde,adieresis,aring" 	g2="trademark" 	k="92" />
<hkern g1="a,agrave,aacute,acircumflex,atilde,adieresis,aring" 	g2="v" 	k="20" />
<hkern g1="a,agrave,aacute,acircumflex,atilde,adieresis,aring" 	g2="w" 	k="20" />
<hkern g1="a,agrave,aacute,acircumflex,atilde,adieresis,aring" 	g2="y,yacute,ydieresis" 	k="41" />
<hkern g1="b,p,thorn" 	g2="asterisk" 	k="51" />
<hkern g1="b,p,thorn" 	g2="backslash" 	k="143" />
<hkern g1="b,p,thorn" 	g2="comma,period,ellipsis" 	k="10" />
<hkern g1="b,p,thorn" 	g2="question" 	k="51" />
<hkern g1="b,p,thorn" 	g2="quotedbl,quotesingle" 	k="41" />
<hkern g1="b,p,thorn" 	g2="quoteleft,quotedblleft" 	k="51" />
<hkern g1="b,p,thorn" 	g2="quoteright,quotedblright" 	k="41" />
<hkern g1="b,p,thorn" 	g2="quotesinglbase,quotedblbase" 	k="10" />
<hkern g1="b,p,thorn" 	g2="t" 	k="31" />
<hkern g1="b,p,thorn" 	g2="trademark" 	k="102" />
<hkern g1="b,p,thorn" 	g2="v" 	k="31" />
<hkern g1="b,p,thorn" 	g2="w" 	k="31" />
<hkern g1="b,p,thorn" 	g2="y,yacute,ydieresis" 	k="41" />
<hkern g1="b,p,thorn" 	g2="at" 	k="-10" />
<hkern g1="b,p,thorn" 	g2="bracketright,braceright" 	k="41" />
<hkern g1="b,p,thorn" 	g2="f" 	k="20" />
<hkern g1="b,p,thorn" 	g2="parenright" 	k="20" />
<hkern g1="b,p,thorn" 	g2="slash" 	k="20" />
<hkern g1="b,p,thorn" 	g2="x" 	k="72" />
<hkern g1="b,p,thorn" 	g2="z" 	k="31" />
<hkern g1="c,ccedilla" 	g2="asterisk" 	k="31" />
<hkern g1="c,ccedilla" 	g2="backslash" 	k="123" />
<hkern g1="c,ccedilla" 	g2="question" 	k="41" />
<hkern g1="c,ccedilla" 	g2="quotedbl,quotesingle" 	k="10" />
<hkern g1="c,ccedilla" 	g2="quoteleft,quotedblleft" 	k="10" />
<hkern g1="c,ccedilla" 	g2="quoteright,quotedblright" 	k="10" />
<hkern g1="c,ccedilla" 	g2="trademark" 	k="92" />
<hkern g1="c,ccedilla" 	g2="v" 	k="20" />
<hkern g1="c,ccedilla" 	g2="w" 	k="20" />
<hkern g1="c,ccedilla" 	g2="y,yacute,ydieresis" 	k="20" />
<hkern g1="c,ccedilla" 	g2="at" 	k="-20" />
<hkern g1="c,ccedilla" 	g2="bracketright,braceright" 	k="31" />
<hkern g1="c,ccedilla" 	g2="f" 	k="-10" />
<hkern g1="c,ccedilla" 	g2="slash" 	k="-10" />
<hkern g1="c,ccedilla" 	g2="x" 	k="51" />
<hkern g1="c,ccedilla" 	g2="z" 	k="10" />
<hkern g1="c,ccedilla" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="20" />
<hkern g1="c,ccedilla" 	g2="AE" 	k="10" />
<hkern g1="c,ccedilla" 	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="-20" />
<hkern g1="c,ccedilla" 	g2="J" 	k="-31" />
<hkern g1="c,ccedilla" 	g2="S" 	k="-20" />
<hkern g1="c,ccedilla" 	g2="T" 	k="184" />
<hkern g1="c,ccedilla" 	g2="V" 	k="123" />
<hkern g1="c,ccedilla" 	g2="W" 	k="92" />
<hkern g1="c,ccedilla" 	g2="X" 	k="51" />
<hkern g1="c,ccedilla" 	g2="Y,Yacute,Ydieresis" 	k="195" />
<hkern g1="c,ccedilla" 	g2="Z" 	k="-20" />
<hkern g1="c,ccedilla" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="-20" />
<hkern g1="c,ccedilla" 	g2="s" 	k="-10" />
<hkern g1="d,i,l,igrave,iacute,icircumflex,idieresis" 	g2="question" 	k="-10" />
<hkern g1="d,i,l,igrave,iacute,icircumflex,idieresis" 	g2="b,h,i,k,l,igrave,iacute,icircumflex,idieresis" 	k="10" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe" 	g2="asterisk" 	k="72" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe" 	g2="backslash" 	k="143" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe" 	g2="comma,period,ellipsis" 	k="10" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe" 	g2="question" 	k="72" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe" 	g2="quotedbl,quotesingle" 	k="31" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe" 	g2="quoteleft,quotedblleft" 	k="41" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe" 	g2="quoteright,quotedblright" 	k="31" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe" 	g2="quotesinglbase,quotedblbase" 	k="10" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe" 	g2="t" 	k="20" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe" 	g2="trademark" 	k="123" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe" 	g2="v" 	k="31" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe" 	g2="w" 	k="31" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe" 	g2="y,yacute,ydieresis" 	k="41" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe" 	g2="at" 	k="-10" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe" 	g2="bracketright,braceright" 	k="31" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe" 	g2="f" 	k="20" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe" 	g2="slash" 	k="20" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe" 	g2="x" 	k="72" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe" 	g2="z" 	k="20" />
<hkern g1="f" 	g2="asterisk" 	k="-41" />
<hkern g1="f" 	g2="comma,period,ellipsis" 	k="102" />
<hkern g1="f" 	g2="question" 	k="-41" />
<hkern g1="f" 	g2="quotedbl,quotesingle" 	k="-41" />
<hkern g1="f" 	g2="quoteleft,quotedblleft" 	k="-31" />
<hkern g1="f" 	g2="quoteright,quotedblright" 	k="-41" />
<hkern g1="f" 	g2="quotesinglbase,quotedblbase" 	k="20" />
<hkern g1="f" 	g2="trademark" 	k="-10" />
<hkern g1="f" 	g2="v" 	k="-41" />
<hkern g1="f" 	g2="w" 	k="-41" />
<hkern g1="f" 	g2="y,yacute,ydieresis" 	k="-41" />
<hkern g1="f" 	g2="bracketright,braceright" 	k="-31" />
<hkern g1="f" 	g2="f" 	k="10" />
<hkern g1="f" 	g2="parenright" 	k="-31" />
<hkern g1="f" 	g2="slash" 	k="82" />
<hkern g1="f" 	g2="x" 	k="10" />
<hkern g1="f" 	g2="z" 	k="20" />
<hkern g1="f" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="31" />
<hkern g1="f" 	g2="ampersand" 	k="51" />
<hkern g1="f" 	g2="asciicircum" 	k="10" />
<hkern g1="f" 	g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="31" />
<hkern g1="f" 	g2="colon,semicolon" 	k="-10" />
<hkern g1="f" 	g2="d,q" 	k="31" />
<hkern g1="f" 	g2="g" 	k="31" />
<hkern g1="f" 	g2="guilsinglleft" 	k="41" />
<hkern g1="f" 	g2="hyphen,endash,emdash" 	k="10" />
<hkern g1="f" 	g2="periodcentered" 	k="-10" />
<hkern g1="g" 	g2="backslash" 	k="113" />
<hkern g1="g" 	g2="trademark" 	k="51" />
<hkern g1="g" 	g2="x" 	k="10" />
<hkern g1="g" 	g2="j" 	k="-61" />
<hkern g1="germandbls" 	g2="quotedbl,quotesingle" 	k="31" />
<hkern g1="germandbls" 	g2="quoteleft,quotedblleft" 	k="31" />
<hkern g1="germandbls" 	g2="quoteright,quotedblright" 	k="31" />
<hkern g1="germandbls" 	g2="t" 	k="31" />
<hkern g1="germandbls" 	g2="w" 	k="31" />
<hkern g1="germandbls" 	g2="y,yacute,ydieresis" 	k="20" />
<hkern g1="germandbls" 	g2="f" 	k="20" />
<hkern g1="germandbls" 	g2="z" 	k="20" />
<hkern g1="germandbls" 	g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="-10" />
<hkern g1="germandbls" 	g2="hyphen,endash,emdash" 	k="-10" />
<hkern g1="h,m,n,ntilde" 	g2="asterisk" 	k="41" />
<hkern g1="h,m,n,ntilde" 	g2="backslash" 	k="133" />
<hkern g1="h,m,n,ntilde" 	g2="question" 	k="41" />
<hkern g1="h,m,n,ntilde" 	g2="quotedbl,quotesingle" 	k="20" />
<hkern g1="h,m,n,ntilde" 	g2="quoteleft,quotedblleft" 	k="31" />
<hkern g1="h,m,n,ntilde" 	g2="quoteright,quotedblright" 	k="20" />
<hkern g1="h,m,n,ntilde" 	g2="t" 	k="20" />
<hkern g1="h,m,n,ntilde" 	g2="trademark" 	k="92" />
<hkern g1="h,m,n,ntilde" 	g2="v" 	k="20" />
<hkern g1="h,m,n,ntilde" 	g2="w" 	k="20" />
<hkern g1="h,m,n,ntilde" 	g2="y,yacute,ydieresis" 	k="20" />
<hkern g1="h,m,n,ntilde" 	g2="f" 	k="20" />
<hkern g1="j" 	g2="j" 	k="-72" />
<hkern g1="k" 	g2="backslash" 	k="92" />
<hkern g1="k" 	g2="comma,period,ellipsis" 	k="-10" />
<hkern g1="k" 	g2="question" 	k="-10" />
<hkern g1="k" 	g2="quotedbl,quotesingle" 	k="20" />
<hkern g1="k" 	g2="quoteleft,quotedblleft" 	k="20" />
<hkern g1="k" 	g2="quoteright,quotedblright" 	k="20" />
<hkern g1="k" 	g2="quotesinglbase,quotedblbase" 	k="-10" />
<hkern g1="k" 	g2="t" 	k="20" />
<hkern g1="k" 	g2="trademark" 	k="51" />
<hkern g1="k" 	g2="at" 	k="41" />
<hkern g1="k" 	g2="f" 	k="20" />
<hkern g1="k" 	g2="slash" 	k="-10" />
<hkern g1="k" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="41" />
<hkern g1="k" 	g2="s" 	k="41" />
<hkern g1="k" 	g2="ampersand" 	k="61" />
<hkern g1="k" 	g2="asciicircum" 	k="51" />
<hkern g1="k" 	g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="82" />
<hkern g1="k" 	g2="d,q" 	k="82" />
<hkern g1="k" 	g2="g" 	k="82" />
<hkern g1="k" 	g2="guilsinglleft" 	k="72" />
<hkern g1="k" 	g2="hyphen,endash,emdash" 	k="102" />
<hkern g1="k" 	g2="periodcentered" 	k="72" />
<hkern g1="k" 	g2="copyright" 	k="51" />
<hkern g1="k" 	g2="guilsinglright" 	k="10" />
<hkern g1="k" 	g2="registered" 	k="51" />
<hkern g1="k" 	g2="u,ugrave,uacute,ucircumflex,udieresis" 	k="31" />
<hkern g1="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash" 	g2="asterisk" 	k="72" />
<hkern g1="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash" 	g2="backslash" 	k="164" />
<hkern g1="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash" 	g2="comma,period,ellipsis" 	k="31" />
<hkern g1="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash" 	g2="question" 	k="72" />
<hkern g1="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash" 	g2="quotedbl,quotesingle" 	k="41" />
<hkern g1="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash" 	g2="quoteleft,quotedblleft" 	k="51" />
<hkern g1="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash" 	g2="quoteright,quotedblright" 	k="41" />
<hkern g1="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash" 	g2="quotesinglbase,quotedblbase" 	k="20" />
<hkern g1="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash" 	g2="t" 	k="20" />
<hkern g1="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash" 	g2="trademark" 	k="133" />
<hkern g1="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash" 	g2="v" 	k="41" />
<hkern g1="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash" 	g2="w" 	k="41" />
<hkern g1="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash" 	g2="y,yacute,ydieresis" 	k="41" />
<hkern g1="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash" 	g2="at" 	k="-10" />
<hkern g1="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash" 	g2="bracketright,braceright" 	k="41" />
<hkern g1="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash" 	g2="f" 	k="20" />
<hkern g1="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash" 	g2="parenright" 	k="10" />
<hkern g1="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash" 	g2="slash" 	k="20" />
<hkern g1="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash" 	g2="x" 	k="92" />
<hkern g1="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash" 	g2="z" 	k="31" />
<hkern g1="q" 	g2="j" 	k="-92" />
<hkern g1="r" 	g2="asterisk" 	k="-41" />
<hkern g1="r" 	g2="backslash" 	k="51" />
<hkern g1="r" 	g2="comma,period,ellipsis" 	k="133" />
<hkern g1="r" 	g2="question" 	k="-41" />
<hkern g1="r" 	g2="quotedbl,quotesingle" 	k="-41" />
<hkern g1="r" 	g2="quoteleft,quotedblleft" 	k="-41" />
<hkern g1="r" 	g2="quoteright,quotedblright" 	k="-41" />
<hkern g1="r" 	g2="quotesinglbase,quotedblbase" 	k="41" />
<hkern g1="r" 	g2="t" 	k="-20" />
<hkern g1="r" 	g2="v" 	k="-31" />
<hkern g1="r" 	g2="w" 	k="-31" />
<hkern g1="r" 	g2="y,yacute,ydieresis" 	k="-20" />
<hkern g1="r" 	g2="bracketright,braceright" 	k="20" />
<hkern g1="r" 	g2="f" 	k="-20" />
<hkern g1="r" 	g2="parenright" 	k="10" />
<hkern g1="r" 	g2="slash" 	k="92" />
<hkern g1="r" 	g2="x" 	k="20" />
<hkern g1="r" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="31" />
<hkern g1="r" 	g2="ampersand" 	k="51" />
<hkern g1="r" 	g2="asciicircum" 	k="10" />
<hkern g1="r" 	g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="20" />
<hkern g1="r" 	g2="colon,semicolon" 	k="-20" />
<hkern g1="r" 	g2="d,q" 	k="20" />
<hkern g1="r" 	g2="g" 	k="20" />
<hkern g1="r" 	g2="guilsinglleft" 	k="20" />
<hkern g1="r" 	g2="hyphen,endash,emdash" 	k="10" />
<hkern g1="r" 	g2="periodcentered" 	k="-20" />
<hkern g1="r" 	g2="copyright" 	k="-10" />
<hkern g1="r" 	g2="registered" 	k="-10" />
<hkern g1="s" 	g2="asterisk" 	k="51" />
<hkern g1="s" 	g2="backslash" 	k="143" />
<hkern g1="s" 	g2="comma,period,ellipsis" 	k="-10" />
<hkern g1="s" 	g2="question" 	k="41" />
<hkern g1="s" 	g2="quotedbl,quotesingle" 	k="20" />
<hkern g1="s" 	g2="quoteleft,quotedblleft" 	k="31" />
<hkern g1="s" 	g2="quoteright,quotedblright" 	k="20" />
<hkern g1="s" 	g2="t" 	k="31" />
<hkern g1="s" 	g2="trademark" 	k="82" />
<hkern g1="s" 	g2="v" 	k="31" />
<hkern g1="s" 	g2="w" 	k="31" />
<hkern g1="s" 	g2="y,yacute,ydieresis" 	k="31" />
<hkern g1="s" 	g2="f" 	k="20" />
<hkern g1="s" 	g2="x" 	k="41" />
<hkern g1="s" 	g2="z" 	k="10" />
<hkern g1="s" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="-10" />
<hkern g1="s" 	g2="s" 	k="-10" />
<hkern g1="s" 	g2="asciicircum" 	k="-10" />
<hkern g1="s" 	g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="-10" />
<hkern g1="s" 	g2="colon,semicolon" 	k="-10" />
<hkern g1="s" 	g2="d,q" 	k="-10" />
<hkern g1="s" 	g2="g" 	k="-10" />
<hkern g1="s" 	g2="guilsinglleft" 	k="10" />
<hkern g1="s" 	g2="hyphen,endash,emdash" 	k="-10" />
<hkern g1="t" 	g2="asterisk" 	k="-20" />
<hkern g1="t" 	g2="backslash" 	k="61" />
<hkern g1="t" 	g2="comma,period,ellipsis" 	k="-20" />
<hkern g1="t" 	g2="question" 	k="-10" />
<hkern g1="t" 	g2="quotedbl,quotesingle" 	k="-20" />
<hkern g1="t" 	g2="quoteleft,quotedblleft" 	k="-20" />
<hkern g1="t" 	g2="quoteright,quotedblright" 	k="-20" />
<hkern g1="t" 	g2="quotesinglbase,quotedblbase" 	k="-10" />
<hkern g1="t" 	g2="t" 	k="10" />
<hkern g1="t" 	g2="v" 	k="-41" />
<hkern g1="t" 	g2="w" 	k="-41" />
<hkern g1="t" 	g2="y,yacute,ydieresis" 	k="-31" />
<hkern g1="t" 	g2="f" 	k="10" />
<hkern g1="t" 	g2="parenright" 	k="-20" />
<hkern g1="t" 	g2="slash" 	k="-20" />
<hkern g1="t" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="-20" />
<hkern g1="t" 	g2="s" 	k="-20" />
<hkern g1="t" 	g2="ampersand" 	k="10" />
<hkern g1="t" 	g2="asciicircum" 	k="10" />
<hkern g1="t" 	g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="10" />
<hkern g1="t" 	g2="colon,semicolon" 	k="-20" />
<hkern g1="t" 	g2="d,q" 	k="10" />
<hkern g1="t" 	g2="g" 	k="10" />
<hkern g1="t" 	g2="guilsinglleft" 	k="-10" />
<hkern g1="t" 	g2="hyphen,endash,emdash" 	k="10" />
<hkern g1="t" 	g2="periodcentered" 	k="-10" />
<hkern g1="t" 	g2="copyright" 	k="-10" />
<hkern g1="t" 	g2="registered" 	k="-10" />
<hkern g1="u,ugrave,uacute,ucircumflex,udieresis" 	g2="backslash" 	k="113" />
<hkern g1="u,ugrave,uacute,ucircumflex,udieresis" 	g2="trademark" 	k="51" />
<hkern g1="u,ugrave,uacute,ucircumflex,udieresis" 	g2="j" 	k="-31" />
<hkern g1="v" 	g2="comma,period,ellipsis" 	k="113" />
<hkern g1="v" 	g2="quotedbl,quotesingle" 	k="-41" />
<hkern g1="v" 	g2="quoteleft,quotedblleft" 	k="-41" />
<hkern g1="v" 	g2="quoteright,quotedblright" 	k="-41" />
<hkern g1="v" 	g2="quotesinglbase,quotedblbase" 	k="41" />
<hkern g1="v" 	g2="t" 	k="-41" />
<hkern g1="v" 	g2="w" 	k="-41" />
<hkern g1="v" 	g2="y,yacute,ydieresis" 	k="-41" />
<hkern g1="v" 	g2="bracketright,braceright" 	k="41" />
<hkern g1="v" 	g2="f" 	k="-41" />
<hkern g1="v" 	g2="z" 	k="20" />
<hkern g1="v" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="41" />
<hkern g1="v" 	g2="s" 	k="10" />
<hkern g1="v" 	g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="41" />
<hkern g1="v" 	g2="colon,semicolon" 	k="-10" />
<hkern g1="v" 	g2="d,q" 	k="31" />
<hkern g1="v" 	g2="g" 	k="31" />
<hkern g1="v" 	g2="guilsinglleft" 	k="51" />
<hkern g1="v" 	g2="hyphen,endash,emdash" 	k="41" />
<hkern g1="w" 	g2="asterisk" 	k="-31" />
<hkern g1="w" 	g2="backslash" 	k="82" />
<hkern g1="w" 	g2="comma,period,ellipsis" 	k="113" />
<hkern g1="w" 	g2="question" 	k="-51" />
<hkern g1="w" 	g2="quotedbl,quotesingle" 	k="-41" />
<hkern g1="w" 	g2="quoteleft,quotedblleft" 	k="-41" />
<hkern g1="w" 	g2="quoteright,quotedblright" 	k="-41" />
<hkern g1="w" 	g2="quotesinglbase,quotedblbase" 	k="41" />
<hkern g1="w" 	g2="t" 	k="-41" />
<hkern g1="w" 	g2="v" 	k="-41" />
<hkern g1="w" 	g2="w" 	k="-41" />
<hkern g1="w" 	g2="y,yacute,ydieresis" 	k="-41" />
<hkern g1="w" 	g2="bracketright,braceright" 	k="41" />
<hkern g1="w" 	g2="f" 	k="-41" />
<hkern g1="w" 	g2="parenright" 	k="10" />
<hkern g1="w" 	g2="slash" 	k="82" />
<hkern g1="w" 	g2="z" 	k="20" />
<hkern g1="w" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="41" />
<hkern g1="w" 	g2="s" 	k="10" />
<hkern g1="w" 	g2="ampersand" 	k="61" />
<hkern g1="w" 	g2="asciicircum" 	k="20" />
<hkern g1="w" 	g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="41" />
<hkern g1="w" 	g2="colon,semicolon" 	k="-10" />
<hkern g1="w" 	g2="d,q" 	k="31" />
<hkern g1="w" 	g2="g" 	k="31" />
<hkern g1="w" 	g2="guilsinglleft" 	k="51" />
<hkern g1="w" 	g2="hyphen,endash,emdash" 	k="41" />
<hkern g1="w" 	g2="periodcentered" 	k="10" />
<hkern g1="x" 	g2="quotedbl,quotesingle" 	k="10" />
<hkern g1="x" 	g2="quoteleft,quotedblleft" 	k="20" />
<hkern g1="x" 	g2="quoteright,quotedblright" 	k="10" />
<hkern g1="x" 	g2="t" 	k="41" />
<hkern g1="x" 	g2="f" 	k="20" />
<hkern g1="x" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="31" />
<hkern g1="x" 	g2="s" 	k="51" />
<hkern g1="x" 	g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="82" />
<hkern g1="x" 	g2="d,q" 	k="72" />
<hkern g1="x" 	g2="g" 	k="72" />
<hkern g1="x" 	g2="guilsinglleft" 	k="92" />
<hkern g1="x" 	g2="hyphen,endash,emdash" 	k="92" />
<hkern g1="x" 	g2="copyright" 	k="51" />
<hkern g1="x" 	g2="guilsinglright" 	k="10" />
<hkern g1="x" 	g2="u,ugrave,uacute,ucircumflex,udieresis" 	k="31" />
<hkern g1="y,yacute,ydieresis" 	g2="asterisk" 	k="-31" />
<hkern g1="y,yacute,ydieresis" 	g2="backslash" 	k="82" />
<hkern g1="y,yacute,ydieresis" 	g2="comma,period,ellipsis" 	k="113" />
<hkern g1="y,yacute,ydieresis" 	g2="question" 	k="-51" />
<hkern g1="y,yacute,ydieresis" 	g2="quotedbl,quotesingle" 	k="-41" />
<hkern g1="y,yacute,ydieresis" 	g2="quoteleft,quotedblleft" 	k="-41" />
<hkern g1="y,yacute,ydieresis" 	g2="quoteright,quotedblright" 	k="-41" />
<hkern g1="y,yacute,ydieresis" 	g2="quotesinglbase,quotedblbase" 	k="41" />
<hkern g1="y,yacute,ydieresis" 	g2="t" 	k="-41" />
<hkern g1="y,yacute,ydieresis" 	g2="v" 	k="-41" />
<hkern g1="y,yacute,ydieresis" 	g2="w" 	k="-41" />
<hkern g1="y,yacute,ydieresis" 	g2="y,yacute,ydieresis" 	k="-41" />
<hkern g1="y,yacute,ydieresis" 	g2="bracketright,braceright" 	k="41" />
<hkern g1="y,yacute,ydieresis" 	g2="f" 	k="-41" />
<hkern g1="y,yacute,ydieresis" 	g2="parenright" 	k="10" />
<hkern g1="y,yacute,ydieresis" 	g2="slash" 	k="82" />
<hkern g1="y,yacute,ydieresis" 	g2="z" 	k="20" />
<hkern g1="y,yacute,ydieresis" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="41" />
<hkern g1="y,yacute,ydieresis" 	g2="s" 	k="10" />
<hkern g1="y,yacute,ydieresis" 	g2="ampersand" 	k="61" />
<hkern g1="y,yacute,ydieresis" 	g2="asciicircum" 	k="20" />
<hkern g1="y,yacute,ydieresis" 	g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="41" />
<hkern g1="y,yacute,ydieresis" 	g2="colon,semicolon" 	k="-10" />
<hkern g1="y,yacute,ydieresis" 	g2="d,q" 	k="31" />
<hkern g1="y,yacute,ydieresis" 	g2="g" 	k="31" />
<hkern g1="y,yacute,ydieresis" 	g2="guilsinglleft" 	k="51" />
<hkern g1="y,yacute,ydieresis" 	g2="hyphen,endash,emdash" 	k="41" />
<hkern g1="y,yacute,ydieresis" 	g2="periodcentered" 	k="10" />
<hkern g1="z" 	g2="asterisk" 	k="10" />
<hkern g1="z" 	g2="backslash" 	k="123" />
<hkern g1="z" 	g2="comma,period,ellipsis" 	k="-20" />
<hkern g1="z" 	g2="question" 	k="-10" />
<hkern g1="z" 	g2="quotesinglbase,quotedblbase" 	k="-10" />
<hkern g1="z" 	g2="trademark" 	k="31" />
<hkern g1="z" 	g2="at" 	k="20" />
<hkern g1="z" 	g2="s" 	k="10" />
<hkern g1="z" 	g2="ampersand" 	k="31" />
<hkern g1="z" 	g2="asciicircum" 	k="10" />
<hkern g1="z" 	g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="31" />
<hkern g1="z" 	g2="d,q" 	k="10" />
<hkern g1="z" 	g2="g" 	k="10" />
<hkern g1="z" 	g2="guilsinglleft" 	k="10" />
<hkern g1="z" 	g2="hyphen,endash,emdash" 	k="10" />
<hkern g1="z" 	g2="periodcentered" 	k="10" />
<hkern g1="ampersand" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="-31" />
<hkern g1="ampersand" 	g2="AE" 	k="-20" />
<hkern g1="ampersand" 	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="20" />
<hkern g1="ampersand" 	g2="J" 	k="-10" />
<hkern g1="ampersand" 	g2="S" 	k="51" />
<hkern g1="ampersand" 	g2="T" 	k="164" />
<hkern g1="ampersand" 	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	k="20" />
<hkern g1="ampersand" 	g2="W" 	k="113" />
<hkern g1="ampersand" 	g2="Y,Yacute,Ydieresis" 	k="195" />
<hkern g1="ampersand" 	g2="Z" 	k="-10" />
<hkern g1="ampersand" 	g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="31" />
<hkern g1="ampersand" 	g2="f" 	k="20" />
<hkern g1="ampersand" 	g2="j" 	k="-10" />
<hkern g1="ampersand" 	g2="t" 	k="51" />
<hkern g1="ampersand" 	g2="u,ugrave,uacute,ucircumflex,udieresis" 	k="20" />
<hkern g1="ampersand" 	g2="w" 	k="82" />
<hkern g1="ampersand" 	g2="y,yacute,ydieresis" 	k="61" />
<hkern g1="ampersand" 	g2="z" 	k="-10" />
<hkern g1="at" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="82" />
<hkern g1="at" 	g2="AE" 	k="51" />
<hkern g1="at" 	g2="T" 	k="61" />
<hkern g1="at" 	g2="W" 	k="61" />
<hkern g1="at" 	g2="Y,Yacute,Ydieresis" 	k="143" />
<hkern g1="at" 	g2="Z" 	k="31" />
<hkern g1="at" 	g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="-10" />
<hkern g1="at" 	g2="w" 	k="-10" />
<hkern g1="at" 	g2="z" 	k="31" />
<hkern g1="at" 	g2="d,q" 	k="-10" />
<hkern g1="at" 	g2="g" 	k="-10" />
<hkern g1="copyright" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="92" />
<hkern g1="copyright" 	g2="AE" 	k="82" />
<hkern g1="copyright" 	g2="T" 	k="72" />
<hkern g1="copyright" 	g2="W" 	k="61" />
<hkern g1="copyright" 	g2="Y,Yacute,Ydieresis" 	k="133" />
<hkern g1="copyright" 	g2="Z" 	k="51" />
<hkern g1="copyright" 	g2="z" 	k="10" />
<hkern g1="copyright" 	g2="V" 	k="72" />
<hkern g1="copyright" 	g2="X" 	k="113" />
<hkern g1="copyright" 	g2="x" 	k="51" />
<hkern g1="asterisk" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="205" />
<hkern g1="asterisk" 	g2="AE" 	k="174" />
<hkern g1="asterisk" 	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="10" />
<hkern g1="asterisk" 	g2="J" 	k="205" />
<hkern g1="asterisk" 	g2="S" 	k="10" />
<hkern g1="asterisk" 	g2="Y,Yacute,Ydieresis" 	k="41" />
<hkern g1="asterisk" 	g2="Z" 	k="10" />
<hkern g1="asterisk" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="72" />
<hkern g1="asterisk" 	g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="72" />
<hkern g1="asterisk" 	g2="d,q" 	k="51" />
<hkern g1="asterisk" 	g2="f" 	k="-20" />
<hkern g1="asterisk" 	g2="g" 	k="51" />
<hkern g1="asterisk" 	g2="s" 	k="51" />
<hkern g1="asterisk" 	g2="t" 	k="-20" />
<hkern g1="asterisk" 	g2="w" 	k="-31" />
<hkern g1="asterisk" 	g2="y,yacute,ydieresis" 	k="-31" />
<hkern g1="asterisk" 	g2="z" 	k="10" />
<hkern g1="backslash" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="-41" />
<hkern g1="backslash" 	g2="AE" 	k="-20" />
<hkern g1="backslash" 	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="102" />
<hkern g1="backslash" 	g2="J" 	k="20" />
<hkern g1="backslash" 	g2="S" 	k="20" />
<hkern g1="backslash" 	g2="Y,Yacute,Ydieresis" 	k="195" />
<hkern g1="backslash" 	g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="20" />
<hkern g1="backslash" 	g2="d,q" 	k="10" />
<hkern g1="backslash" 	g2="f" 	k="31" />
<hkern g1="backslash" 	g2="g" 	k="10" />
<hkern g1="backslash" 	g2="s" 	k="10" />
<hkern g1="backslash" 	g2="t" 	k="61" />
<hkern g1="backslash" 	g2="w" 	k="82" />
<hkern g1="backslash" 	g2="y,yacute,ydieresis" 	k="72" />
<hkern g1="backslash" 	g2="T" 	k="154" />
<hkern g1="backslash" 	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	k="41" />
<hkern g1="backslash" 	g2="W" 	k="143" />
<hkern g1="backslash" 	g2="u,ugrave,uacute,ucircumflex,udieresis" 	k="20" />
<hkern g1="bracketleft,braceleft" 	g2="J" 	k="51" />
<hkern g1="bracketleft,braceleft" 	g2="Y,Yacute,Ydieresis" 	k="-20" />
<hkern g1="bracketleft,braceleft" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="31" />
<hkern g1="bracketleft,braceleft" 	g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="41" />
<hkern g1="bracketleft,braceleft" 	g2="d,q" 	k="41" />
<hkern g1="bracketleft,braceleft" 	g2="f" 	k="31" />
<hkern g1="bracketleft,braceleft" 	g2="g" 	k="41" />
<hkern g1="bracketleft,braceleft" 	g2="t" 	k="10" />
<hkern g1="bracketleft,braceleft" 	g2="w" 	k="41" />
<hkern g1="bracketleft,braceleft" 	g2="T" 	k="-10" />
<hkern g1="bracketleft,braceleft" 	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	k="-10" />
<hkern g1="bracketleft,braceleft" 	g2="W" 	k="-20" />
<hkern g1="bracketleft,braceleft" 	g2="V" 	k="-20" />
<hkern g1="bracketleft,braceleft" 	g2="j" 	k="-113" />
<hkern g1="bracketleft,braceleft" 	g2="v" 	k="41" />
<hkern g1="colon,semicolon" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="-20" />
<hkern g1="colon,semicolon" 	g2="Y,Yacute,Ydieresis" 	k="113" />
<hkern g1="colon,semicolon" 	g2="Z" 	k="-10" />
<hkern g1="colon,semicolon" 	g2="f" 	k="-20" />
<hkern g1="colon,semicolon" 	g2="s" 	k="-20" />
<hkern g1="colon,semicolon" 	g2="w" 	k="-10" />
<hkern g1="colon,semicolon" 	g2="y,yacute,ydieresis" 	k="-20" />
<hkern g1="colon,semicolon" 	g2="T" 	k="51" />
<hkern g1="colon,semicolon" 	g2="W" 	k="31" />
<hkern g1="colon,semicolon" 	g2="V" 	k="51" />
<hkern g1="colon,semicolon" 	g2="j" 	k="-41" />
<hkern g1="colon,semicolon" 	g2="v" 	k="-10" />
<hkern g1="comma,period,ellipsis" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="-41" />
<hkern g1="comma,period,ellipsis" 	g2="AE" 	k="-41" />
<hkern g1="comma,period,ellipsis" 	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="72" />
<hkern g1="comma,period,ellipsis" 	g2="J" 	k="-31" />
<hkern g1="comma,period,ellipsis" 	g2="S" 	k="-20" />
<hkern g1="comma,period,ellipsis" 	g2="Y,Yacute,Ydieresis" 	k="236" />
<hkern g1="comma,period,ellipsis" 	g2="Z" 	k="-20" />
<hkern g1="comma,period,ellipsis" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="-20" />
<hkern g1="comma,period,ellipsis" 	g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="31" />
<hkern g1="comma,period,ellipsis" 	g2="d,q" 	k="10" />
<hkern g1="comma,period,ellipsis" 	g2="f" 	k="31" />
<hkern g1="comma,period,ellipsis" 	g2="g" 	k="10" />
<hkern g1="comma,period,ellipsis" 	g2="s" 	k="-20" />
<hkern g1="comma,period,ellipsis" 	g2="t" 	k="82" />
<hkern g1="comma,period,ellipsis" 	g2="w" 	k="113" />
<hkern g1="comma,period,ellipsis" 	g2="y,yacute,ydieresis" 	k="51" />
<hkern g1="comma,period,ellipsis" 	g2="z" 	k="-20" />
<hkern g1="comma,period,ellipsis" 	g2="T" 	k="205" />
<hkern g1="comma,period,ellipsis" 	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	k="31" />
<hkern g1="comma,period,ellipsis" 	g2="W" 	k="143" />
<hkern g1="comma,period,ellipsis" 	g2="u,ugrave,uacute,ucircumflex,udieresis" 	k="10" />
<hkern g1="comma,period,ellipsis" 	g2="V" 	k="174" />
<hkern g1="comma,period,ellipsis" 	g2="j" 	k="-20" />
<hkern g1="comma,period,ellipsis" 	g2="v" 	k="113" />
<hkern g1="comma,period,ellipsis" 	g2="X" 	k="-20" />
<hkern g1="exclamdown" 	g2="Y,Yacute,Ydieresis" 	k="61" />
<hkern g1="exclamdown" 	g2="T" 	k="10" />
<hkern g1="exclamdown" 	g2="W" 	k="31" />
<hkern g1="exclamdown" 	g2="j" 	k="-92" />
<hkern g1="guilsinglleft" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="10" />
<hkern g1="guilsinglleft" 	g2="Y,Yacute,Ydieresis" 	k="133" />
<hkern g1="guilsinglleft" 	g2="T" 	k="82" />
<hkern g1="guilsinglleft" 	g2="W" 	k="51" />
<hkern g1="guilsinglleft" 	g2="V" 	k="72" />
<hkern g1="guilsinglleft" 	g2="X" 	k="20" />
<hkern g1="guilsinglleft" 	g2="x" 	k="10" />
<hkern g1="guilsinglright" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="51" />
<hkern g1="guilsinglright" 	g2="AE" 	k="31" />
<hkern g1="guilsinglright" 	g2="J" 	k="-31" />
<hkern g1="guilsinglright" 	g2="Y,Yacute,Ydieresis" 	k="195" />
<hkern g1="guilsinglright" 	g2="Z" 	k="10" />
<hkern g1="guilsinglright" 	g2="f" 	k="10" />
<hkern g1="guilsinglright" 	g2="s" 	k="10" />
<hkern g1="guilsinglright" 	g2="t" 	k="10" />
<hkern g1="guilsinglright" 	g2="w" 	k="51" />
<hkern g1="guilsinglright" 	g2="y,yacute,ydieresis" 	k="41" />
<hkern g1="guilsinglright" 	g2="z" 	k="10" />
<hkern g1="guilsinglright" 	g2="T" 	k="164" />
<hkern g1="guilsinglright" 	g2="W" 	k="92" />
<hkern g1="guilsinglright" 	g2="V" 	k="113" />
<hkern g1="guilsinglright" 	g2="v" 	k="51" />
<hkern g1="guilsinglright" 	g2="X" 	k="82" />
<hkern g1="guilsinglright" 	g2="x" 	k="82" />
<hkern g1="hyphen,endash,emdash" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="41" />
<hkern g1="hyphen,endash,emdash" 	g2="AE" 	k="10" />
<hkern g1="hyphen,endash,emdash" 	g2="J" 	k="-31" />
<hkern g1="hyphen,endash,emdash" 	g2="S" 	k="-20" />
<hkern g1="hyphen,endash,emdash" 	g2="Y,Yacute,Ydieresis" 	k="225" />
<hkern g1="hyphen,endash,emdash" 	g2="Z" 	k="10" />
<hkern g1="hyphen,endash,emdash" 	g2="t" 	k="20" />
<hkern g1="hyphen,endash,emdash" 	g2="w" 	k="41" />
<hkern g1="hyphen,endash,emdash" 	g2="y,yacute,ydieresis" 	k="41" />
<hkern g1="hyphen,endash,emdash" 	g2="z" 	k="10" />
<hkern g1="hyphen,endash,emdash" 	g2="T" 	k="164" />
<hkern g1="hyphen,endash,emdash" 	g2="W" 	k="102" />
<hkern g1="hyphen,endash,emdash" 	g2="V" 	k="133" />
<hkern g1="hyphen,endash,emdash" 	g2="v" 	k="41" />
<hkern g1="hyphen,endash,emdash" 	g2="X" 	k="102" />
<hkern g1="hyphen,endash,emdash" 	g2="x" 	k="92" />
<hkern g1="parenleft" 	g2="J" 	k="41" />
<hkern g1="parenleft" 	g2="S" 	k="10" />
<hkern g1="parenleft" 	g2="Y,Yacute,Ydieresis" 	k="-20" />
<hkern g1="parenleft" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="10" />
<hkern g1="parenleft" 	g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="10" />
<hkern g1="parenleft" 	g2="d,q" 	k="20" />
<hkern g1="parenleft" 	g2="g" 	k="20" />
<hkern g1="parenleft" 	g2="s" 	k="10" />
<hkern g1="parenleft" 	g2="t" 	k="10" />
<hkern g1="parenleft" 	g2="w" 	k="10" />
<hkern g1="parenleft" 	g2="T" 	k="-10" />
<hkern g1="parenleft" 	g2="W" 	k="-20" />
<hkern g1="parenleft" 	g2="u,ugrave,uacute,ucircumflex,udieresis" 	k="10" />
<hkern g1="parenleft" 	g2="j" 	k="-123" />
<hkern g1="periodcentered" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="82" />
<hkern g1="periodcentered" 	g2="AE" 	k="20" />
<hkern g1="periodcentered" 	g2="Y,Yacute,Ydieresis" 	k="164" />
<hkern g1="periodcentered" 	g2="Z" 	k="10" />
<hkern g1="periodcentered" 	g2="f" 	k="-20" />
<hkern g1="periodcentered" 	g2="s" 	k="-20" />
<hkern g1="periodcentered" 	g2="t" 	k="-20" />
<hkern g1="periodcentered" 	g2="w" 	k="10" />
<hkern g1="periodcentered" 	g2="y,yacute,ydieresis" 	k="10" />
<hkern g1="periodcentered" 	g2="z" 	k="10" />
<hkern g1="periodcentered" 	g2="T" 	k="143" />
<hkern g1="periodcentered" 	g2="W" 	k="72" />
<hkern g1="questiondown" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="-20" />
<hkern g1="questiondown" 	g2="AE" 	k="-20" />
<hkern g1="questiondown" 	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="61" />
<hkern g1="questiondown" 	g2="J" 	k="-10" />
<hkern g1="questiondown" 	g2="Y,Yacute,Ydieresis" 	k="225" />
<hkern g1="questiondown" 	g2="Z" 	k="-10" />
<hkern g1="questiondown" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="-10" />
<hkern g1="questiondown" 	g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="41" />
<hkern g1="questiondown" 	g2="d,q" 	k="31" />
<hkern g1="questiondown" 	g2="f" 	k="41" />
<hkern g1="questiondown" 	g2="g" 	k="20" />
<hkern g1="questiondown" 	g2="t" 	k="72" />
<hkern g1="questiondown" 	g2="w" 	k="123" />
<hkern g1="questiondown" 	g2="y,yacute,ydieresis" 	k="72" />
<hkern g1="questiondown" 	g2="T" 	k="184" />
<hkern g1="questiondown" 	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	k="51" />
<hkern g1="questiondown" 	g2="W" 	k="164" />
<hkern g1="questiondown" 	g2="j" 	k="-82" />
<hkern g1="quotedbl,quotesingle" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="184" />
<hkern g1="quotedbl,quotesingle" 	g2="AE" 	k="225" />
<hkern g1="quotedbl,quotesingle" 	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="51" />
<hkern g1="quotedbl,quotesingle" 	g2="J" 	k="236" />
<hkern g1="quotedbl,quotesingle" 	g2="S" 	k="31" />
<hkern g1="quotedbl,quotesingle" 	g2="Y,Yacute,Ydieresis" 	k="-20" />
<hkern g1="quotedbl,quotesingle" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="133" />
<hkern g1="quotedbl,quotesingle" 	g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="123" />
<hkern g1="quotedbl,quotesingle" 	g2="d,q" 	k="133" />
<hkern g1="quotedbl,quotesingle" 	g2="f" 	k="10" />
<hkern g1="quotedbl,quotesingle" 	g2="g" 	k="133" />
<hkern g1="quotedbl,quotesingle" 	g2="s" 	k="82" />
<hkern g1="quotedbl,quotesingle" 	g2="t" 	k="-10" />
<hkern g1="quotedbl,quotesingle" 	g2="w" 	k="-20" />
<hkern g1="quotedbl,quotesingle" 	g2="y,yacute,ydieresis" 	k="-20" />
<hkern g1="quotedbl,quotesingle" 	g2="z" 	k="41" />
<hkern g1="quotedbl,quotesingle" 	g2="T" 	k="-20" />
<hkern g1="quotedbl,quotesingle" 	g2="W" 	k="-20" />
<hkern g1="quotedbl,quotesingle" 	g2="u,ugrave,uacute,ucircumflex,udieresis" 	k="20" />
<hkern g1="quotedbl,quotesingle" 	g2="V" 	k="-20" />
<hkern g1="quotedbl,quotesingle" 	g2="v" 	k="-20" />
<hkern g1="quotedbl,quotesingle" 	g2="X" 	k="31" />
<hkern g1="quotedbl,quotesingle" 	g2="x" 	k="31" />
<hkern g1="quotedbl,quotesingle" 	g2="germandbls" 	k="10" />
<hkern g1="quotedbl,quotesingle" 	g2="m,n,r,ntilde" 	k="20" />
<hkern g1="quotedbl,quotesingle" 	g2="p" 	k="20" />
<hkern g1="quoteleft,quotedblleft" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="154" />
<hkern g1="quoteleft,quotedblleft" 	g2="AE" 	k="174" />
<hkern g1="quoteleft,quotedblleft" 	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="10" />
<hkern g1="quoteleft,quotedblleft" 	g2="J" 	k="195" />
<hkern g1="quoteleft,quotedblleft" 	g2="S" 	k="20" />
<hkern g1="quoteleft,quotedblleft" 	g2="Y,Yacute,Ydieresis" 	k="-10" />
<hkern g1="quoteleft,quotedblleft" 	g2="Z" 	k="20" />
<hkern g1="quoteleft,quotedblleft" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="51" />
<hkern g1="quoteleft,quotedblleft" 	g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="41" />
<hkern g1="quoteleft,quotedblleft" 	g2="d,q" 	k="51" />
<hkern g1="quoteleft,quotedblleft" 	g2="f" 	k="-20" />
<hkern g1="quoteleft,quotedblleft" 	g2="g" 	k="51" />
<hkern g1="quoteleft,quotedblleft" 	g2="s" 	k="20" />
<hkern g1="quoteleft,quotedblleft" 	g2="t" 	k="-31" />
<hkern g1="quoteleft,quotedblleft" 	g2="w" 	k="-31" />
<hkern g1="quoteleft,quotedblleft" 	g2="y,yacute,ydieresis" 	k="-31" />
<hkern g1="quoteleft,quotedblleft" 	g2="T" 	k="-20" />
<hkern g1="quoteleft,quotedblleft" 	g2="W" 	k="-20" />
<hkern g1="quoteleft,quotedblleft" 	g2="V" 	k="-20" />
<hkern g1="quoteleft,quotedblleft" 	g2="v" 	k="-31" />
<hkern g1="quoteleft,quotedblleft" 	g2="X" 	k="20" />
<hkern g1="quoteleft,quotedblleft" 	g2="x" 	k="20" />
<hkern g1="quoteleft,quotedblleft" 	g2="germandbls" 	k="10" />
<hkern g1="quoteright,quotedblright" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="184" />
<hkern g1="quoteright,quotedblright" 	g2="AE" 	k="225" />
<hkern g1="quoteright,quotedblright" 	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="51" />
<hkern g1="quoteright,quotedblright" 	g2="J" 	k="236" />
<hkern g1="quoteright,quotedblright" 	g2="S" 	k="20" />
<hkern g1="quoteright,quotedblright" 	g2="Y,Yacute,Ydieresis" 	k="-20" />
<hkern g1="quoteright,quotedblright" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="133" />
<hkern g1="quoteright,quotedblright" 	g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="123" />
<hkern g1="quoteright,quotedblright" 	g2="d,q" 	k="133" />
<hkern g1="quoteright,quotedblright" 	g2="f" 	k="10" />
<hkern g1="quoteright,quotedblright" 	g2="g" 	k="133" />
<hkern g1="quoteright,quotedblright" 	g2="s" 	k="82" />
<hkern g1="quoteright,quotedblright" 	g2="t" 	k="-10" />
<hkern g1="quoteright,quotedblright" 	g2="w" 	k="-20" />
<hkern g1="quoteright,quotedblright" 	g2="y,yacute,ydieresis" 	k="-20" />
<hkern g1="quoteright,quotedblright" 	g2="z" 	k="41" />
<hkern g1="quoteright,quotedblright" 	g2="T" 	k="-20" />
<hkern g1="quoteright,quotedblright" 	g2="W" 	k="-20" />
<hkern g1="quoteright,quotedblright" 	g2="u,ugrave,uacute,ucircumflex,udieresis" 	k="20" />
<hkern g1="quoteright,quotedblright" 	g2="V" 	k="-20" />
<hkern g1="quoteright,quotedblright" 	g2="v" 	k="-20" />
<hkern g1="quoteright,quotedblright" 	g2="X" 	k="31" />
<hkern g1="quoteright,quotedblright" 	g2="x" 	k="31" />
<hkern g1="quoteright,quotedblright" 	g2="germandbls" 	k="10" />
<hkern g1="quoteright,quotedblright" 	g2="m,n,r,ntilde" 	k="20" />
<hkern g1="quoteright,quotedblright" 	g2="p" 	k="20" />
<hkern g1="quotesinglbase,quotedblbase" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="-41" />
<hkern g1="quotesinglbase,quotedblbase" 	g2="AE" 	k="-41" />
<hkern g1="quotesinglbase,quotedblbase" 	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="72" />
<hkern g1="quotesinglbase,quotedblbase" 	g2="J" 	k="-31" />
<hkern g1="quotesinglbase,quotedblbase" 	g2="S" 	k="-20" />
<hkern g1="quotesinglbase,quotedblbase" 	g2="Y,Yacute,Ydieresis" 	k="236" />
<hkern g1="quotesinglbase,quotedblbase" 	g2="Z" 	k="-20" />
<hkern g1="quotesinglbase,quotedblbase" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="-20" />
<hkern g1="quotesinglbase,quotedblbase" 	g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="31" />
<hkern g1="quotesinglbase,quotedblbase" 	g2="d,q" 	k="10" />
<hkern g1="quotesinglbase,quotedblbase" 	g2="f" 	k="31" />
<hkern g1="quotesinglbase,quotedblbase" 	g2="g" 	k="10" />
<hkern g1="quotesinglbase,quotedblbase" 	g2="s" 	k="-20" />
<hkern g1="quotesinglbase,quotedblbase" 	g2="t" 	k="82" />
<hkern g1="quotesinglbase,quotedblbase" 	g2="w" 	k="113" />
<hkern g1="quotesinglbase,quotedblbase" 	g2="y,yacute,ydieresis" 	k="51" />
<hkern g1="quotesinglbase,quotedblbase" 	g2="z" 	k="-20" />
<hkern g1="quotesinglbase,quotedblbase" 	g2="T" 	k="205" />
<hkern g1="quotesinglbase,quotedblbase" 	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	k="31" />
<hkern g1="quotesinglbase,quotedblbase" 	g2="W" 	k="143" />
<hkern g1="quotesinglbase,quotedblbase" 	g2="u,ugrave,uacute,ucircumflex,udieresis" 	k="10" />
<hkern g1="quotesinglbase,quotedblbase" 	g2="V" 	k="174" />
<hkern g1="quotesinglbase,quotedblbase" 	g2="j" 	k="-113" />
<hkern g1="quotesinglbase,quotedblbase" 	g2="v" 	k="113" />
<hkern g1="quotesinglbase,quotedblbase" 	g2="X" 	k="-20" />
<hkern g1="slash" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="195" />
<hkern g1="slash" 	g2="AE" 	k="184" />
<hkern g1="slash" 	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="102" />
<hkern g1="slash" 	g2="J" 	k="195" />
<hkern g1="slash" 	g2="S" 	k="41" />
<hkern g1="slash" 	g2="Y,Yacute,Ydieresis" 	k="-20" />
<hkern g1="slash" 	g2="Z" 	k="41" />
<hkern g1="slash" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="164" />
<hkern g1="slash" 	g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="164" />
<hkern g1="slash" 	g2="d,q" 	k="154" />
<hkern g1="slash" 	g2="f" 	k="41" />
<hkern g1="slash" 	g2="g" 	k="154" />
<hkern g1="slash" 	g2="s" 	k="154" />
<hkern g1="slash" 	g2="t" 	k="61" />
<hkern g1="slash" 	g2="w" 	k="82" />
<hkern g1="slash" 	g2="y,yacute,ydieresis" 	k="82" />
<hkern g1="slash" 	g2="z" 	k="133" />
<hkern g1="slash" 	g2="T" 	k="-20" />
<hkern g1="slash" 	g2="W" 	k="-31" />
<hkern g1="slash" 	g2="u,ugrave,uacute,ucircumflex,udieresis" 	k="102" />
<hkern g1="slash" 	g2="m,n,r,ntilde" 	k="102" />
</font>
</defs></svg> 