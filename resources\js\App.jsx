import React, { Fragment, useEffect, useRef, useState, useCallback } from 'react';
import ReactDOM from 'react-dom/client';
import gsap from 'gsap';
import ScrollTrigger from 'gsap/ScrollTrigger';
import Footer from './partials/Footer';
import HeaderTwo from './partials/HeaderTwo'; // optional if needed
import About from './components/About';
import Methodology from './components/Methodology';
import Investigation from './components/Investigation';
import Team from './components/Team';
import News from './components/News';
import Contact from './components/Contacts';

gsap.registerPlugin(ScrollTrigger);

const App = () => {
  const [value, setValue] = useState(0);
  const [investigationSubMenu, setInvestigationSubMenu] = useState([]);
  const [showSubmenu, setShowSubmenu] = useState(false);
  const [isScrolling, setIsScrolling] = useState(false);
  const [showScrollTop, setShowScrollTop] = useState(false);
  const scrollTimeoutRef = useRef(null);

  // Enhanced smooth scroll function
  const smoothScrollTo = useCallback((targetId, offset = 100, duration = 1000) => {
    const targetElement = document.querySelector(targetId);
    if (!targetElement) return;

    setIsScrolling(true);

    // Clear any existing scroll timeout
    if (scrollTimeoutRef.current) {
      clearTimeout(scrollTimeoutRef.current);
    }

    const targetPosition = targetElement.getBoundingClientRect().top + window.scrollY - offset;
    const startPosition = window.scrollY;
    const distance = targetPosition - startPosition;
    let startTime = null;

    // Easing function for smooth animation
    const easeInOutCubic = (t) => {
      return t < 0.5 ? 4 * t * t * t : (t - 1) * (2 * t - 2) * (2 * t - 2) + 1;
    };

    const animateScroll = (currentTime) => {
      if (startTime === null) startTime = currentTime;
      const timeElapsed = currentTime - startTime;
      const progress = Math.min(timeElapsed / duration, 1);
      const ease = easeInOutCubic(progress);

      window.scrollTo(0, startPosition + distance * ease);

      if (progress < 1) {
        requestAnimationFrame(animateScroll);
      } else {
        setIsScrolling(false);
      }
    };

    requestAnimationFrame(animateScroll);

    // Set timeout to ensure scrolling state is reset
    scrollTimeoutRef.current = setTimeout(() => {
      setIsScrolling(false);
    }, duration + 100);
  }, []);

  // Smooth scroll to top function
  const scrollToTop = useCallback(() => {
    smoothScrollTo('body', 0, 800);
  }, [smoothScrollTo]);

  // Handle scroll events for scroll-to-top button visibility
  useEffect(() => {
    const handleScroll = () => {
      const scrolled = window.scrollY;
      setShowScrollTop(scrolled > 300);
    };

    window.addEventListener('scroll', handleScroll);

    // Initial check
    handleScroll();

    return () => {
      window.removeEventListener('scroll', handleScroll);
    };
  }, []);

  useEffect(() => {
    setTimeout(() => {
      function animateSplitTextAll(elements, totalDuration = 0.5) {
        if (typeof elements === "string") {
          elements = document.querySelectorAll(elements);
        }
        elements.forEach((el) => {
          const text = el.dataset.title || "";
          const chars = text.split("");
          const delayPerChar = totalDuration / chars.length;
          el.innerHTML = "";
          chars.forEach((char, i) => {
            const span = document.createElement("span");
            span.className = "fade-in-char";
            span.style.animationDelay = `${i * delayPerChar}s`;
            span.textContent = char;
            el.appendChild(span);
          });
        });
      }

      animateSplitTextAll(".animated-title");

      const isMobile = window.matchMedia("(max-width: 768px)").matches;

      if (!isMobile) {
        gsap.utils.toArray("section.single-items").forEach((dropdown) => {
          const pinTarget = dropdown.querySelector(".trriger-menus");
          if (!pinTarget) return;

          ScrollTrigger.create({
            trigger: dropdown,
            start: "top -15px",
            end: "bottom-=70 top",
            pin: pinTarget,
            pinSpacing: false,
            onEnter: () => {
              animateSplitTextAll(
                dropdown.querySelectorAll(".animated-title"),
                0.5
              );
            },
          });
        });
      }

      // Sidebar toggle with reset functionality
      document.querySelector('.sidebar-btn')?.addEventListener('click', () => {
        // Toggle sidebar
        document.querySelector('.sidebar')?.classList.toggle('active');

        // Reset all functionality to initial state
        resetAllFunctionality();
      });

      function resetAllFunctionality() {
        // Reset submenu state
        setShowSubmenu(false);

        // Reset all accordion states
        document.querySelectorAll('.menu-btn, .smenu-btn').forEach(btn => {
          btn.classList.remove('active');
        });

        document.querySelectorAll('.child-menucontent').forEach(content => {
          content.classList.remove('active');
        });

        // Reset navigation active states
        document.querySelectorAll('#mainNav .nav-link').forEach(link => {
          link.classList.remove('active');
        });

        // Enhanced smooth scroll to top
        scrollToTop();

        console.log('All functionality reset to initial state');
      }

      // Enhanced smooth scroll for nav links (excluding Investigation which has special handling)
      document.querySelectorAll("#mainNav .nav-link:not([href='#section3'])").forEach((link) => {
        link.addEventListener("click", function (e) {
          e.preventDefault();
          const targetId = this.getAttribute("href");

          // Add visual feedback
          this.style.opacity = '0.7';
          setTimeout(() => {
            this.style.opacity = '1';
          }, 200);

          // Use enhanced smooth scroll
          smoothScrollTo(targetId, 100, 1200);
        });
      });

      // Accordion toggle for .menu-btn and .smenu-btn
      const menuButtons = document.querySelectorAll(".menu-btn, .smenu-btn");
      menuButtons.forEach((button) => {
        button.addEventListener("click", function (e) {
          e.preventDefault();
          this.classList.toggle("active");

          const parent = this.parentElement;
          if (parent) {
            const contents = parent.querySelectorAll(".child-menucontent");
            contents.forEach((content) => {
              content.classList.toggle("active");
            });
          }
        });
      });

      const resetBtns = document.querySelectorAll(".reset-menu");

      resetBtns.forEach(btn => {
        btn.addEventListener("click", () => {
          document.querySelectorAll("#section3 .child-menucontent, #section3 .menu-btn").forEach(el => {
            el.classList.remove("active");
          });
        });
      });


    }, 2500);
  }, []);

  useEffect(() => {
    setTimeout(() => {
      const sections = gsap.utils.toArray(".single-items");
      const navLinks = document.querySelectorAll("#mainNav .nav-link");

      sections.forEach((section) => {
        const id = section.getAttribute("id");
        ScrollTrigger.create({
          trigger: section,
          start: "top center",
          end: "bottom center",
          onEnter: () => setActiveLink(id),
          onEnterBack: () => setActiveLink(id),
        });
      });

      function setActiveLink(id) {
        navLinks.forEach((link) => {
          const href = link.getAttribute("href");
          link.classList.toggle("active", href === `#${id}`);
        });
      }
      ScrollTrigger.refresh();
    }, 1000);
  }, [value]);

  return (
    <Fragment>
      <header>
        <div className="container header-container d-flex justify-content-between align-items-center">
          <div className="hero-block-logo-hp">
            <a href="#">
              <img
                width="160"
                alt="Logo"
                src="https://phplaravel-1312572-5496212.cloudwaysapps.com/uploads/settings/1747402069_1747379288_logo.png"
              />
            </a>
          </div>
          <div className="bergur-menu sidebar-btn">
            <svg
              width="30"
              viewBox="0 0 108 109"
              xmlns="http://www.w3.org/2000/svg"
            >
              <rect width="108" height="23" />
              <rect y="43" width="108" height="23" />
              <rect y="86" width="108" height="23" />
            </svg>
          </div>
        </div>
      </header>

      <div className="content-section">
        <div className="container">
          <div className="row">
            <div className="col-lg-3 position-relative">
              <div className="sidebar">
                <nav id="mainNav" className="navbar-nav nav flex-column">
                  <a className="nav-link" href="#section1">About</a>
                  <a className="nav-link" href="#section2">Methodology</a>
                  <a
                    className={`nav-link reset-menu ${showSubmenu ? 'submenu-open' : ''} ${isScrolling ? 'scrolling' : ''}`}
                    href="#section3"
                    onClick={(e) => {
                      e.preventDefault();
                      setShowSubmenu(!showSubmenu);

                      // Enhanced smooth scroll to Investigation section
                      smoothScrollTo('#section3', 300, 1200);
                    }}
                  >
                    Investigation
                  </a>
                  <div className={`submenu nav flex-column ${showSubmenu ? 'show' : ''}`}>
                    {investigationSubMenu.map((item, index) => (
                      <a
                        className="nav-link"
                        onClick={(e) => {
                          e.preventDefault();
                          setValue(prev => prev + 1);

                          // Enhanced smooth scroll to submenu item
                          const targetElement = document.querySelector(`#section3-${index}`);
                          if (targetElement) {
                            smoothScrollTo(`#section3-${index}`, 150, 1000);

                            // Trigger click after scroll completes
                            setTimeout(() => {
                              if (!targetElement.classList.contains('active')) {
                                targetElement.click();
                              }
                            }, 1100);
                          }
                        }}
                        href={`#section3-${index}`}
                        key={index}
                      >
                        {item.title}
                      </a>
                    ))}
                  </div>
                  <a className="nav-link" href="#section4">Team</a>
                  <a className="nav-link" href="#section5">News</a>
                  <a className="nav-link" href="#section6">Contacts</a>
                </nav>
              </div>
            </div>
            <div className="col-lg-8">
              <div className="content-area" id="main-content">
                <About />
                <Methodology />
                <Investigation setInvestigationSubMenu={setInvestigationSubMenu} setValue={setValue} />
                <Team />
                <News setValue={setValue} />
                <Contact />
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Scroll to Top Button */}
      <button
        className={`scroll-to-top ${showScrollTop ? 'visible' : ''}`}
        onClick={scrollToTop}
        style={{
          position: 'fixed',
          bottom: '30px',
          right: '30px',
          width: '50px',
          height: '50px',
          borderRadius: '50%',
          backgroundColor: '#333',
          color: '#fff',
          border: 'none',
          cursor: 'pointer',
          fontSize: '18px',
          zIndex: 1000,
          opacity: showScrollTop ? '1' : '0',
          visibility: showScrollTop ? 'visible' : 'hidden',
          transition: 'all 0.3s ease',
          transform: `translateY(${isScrolling ? '0' : showScrollTop ? '0' : '10px'}) scale(${isScrolling ? '0.9' : '1'})`,
          boxShadow: '0 4px 12px rgba(0,0,0,0.3)',
        }}
        onMouseEnter={(e) => {
          e.target.style.backgroundColor = '#555';
          e.target.style.transform = 'scale(1.1)';
        }}
        onMouseLeave={(e) => {
          e.target.style.backgroundColor = '#333';
          e.target.style.transform = 'scale(1)';
        }}
      >
        ↑
      </button>

      {/* <Footer /> */}
    </Fragment>
  );
};

const root = ReactDOM.createRoot(document.getElementById('react-root'));
root.render(<App />);
