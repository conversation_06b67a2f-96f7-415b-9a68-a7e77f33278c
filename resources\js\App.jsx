import React, { Fragment, useEffect, useRef, useState } from 'react';
import ReactDOM from 'react-dom/client';
import gsap from 'gsap';
import ScrollTrigger from 'gsap/ScrollTrigger';
import Footer from './partials/Footer';
import HeaderTwo from './partials/HeaderTwo'; // optional if needed
import About from './components/About';
import Methodology from './components/Methodology';
import Investigation from './components/Investigation';
import Team from './components/Team';
import News from './components/News';
import Contact from './components/Contacts';

gsap.registerPlugin(ScrollTrigger);

const App = () => {
  const [value, setValue] = useState(0);

  useEffect(() => {
    setTimeout(() => {
      function animateSplitTextAll(elements, totalDuration = 0.5) {
        if (typeof elements === "string") {
          elements = document.querySelectorAll(elements);
        }
        elements.forEach((el) => {
          const text = el.dataset.title || "";
          const chars = text.split("");
          const delayPerChar = totalDuration / chars.length;
          el.innerHTML = "";
          chars.forEach((char, i) => {
            const span = document.createElement("span");
            span.className = "fade-in-char";
            span.style.animationDelay = `${i * delayPerChar}s`;
            span.textContent = char;
            el.appendChild(span);
          });
        });
      }

      animateSplitTextAll(".animated-title");

      const isMobile = window.matchMedia("(max-width: 768px)").matches;

      if (!isMobile) {
        gsap.utils.toArray("section.single-items").forEach((dropdown) => {
          const pinTarget = dropdown.querySelector(".trriger-menu");
          if (!pinTarget) return;

          ScrollTrigger.create({
            trigger: dropdown,
            start: "top -15px",
            end: "bottom-=70 top",
            pin: pinTarget,
            pinSpacing: false,
            onEnter: () => {
              animateSplitTextAll(
                dropdown.querySelectorAll(".animated-title"),
                0.5
              );
            },
          });
        });
      }

      // Sidebar toggle (for mobile)
      document.querySelector('.sidebar-btn')?.addEventListener('click', () => {
        document.querySelector('.sidebar')?.classList.toggle('active');
      });

      // Smooth scroll for nav links
      document.querySelectorAll("#mainNav .nav-link").forEach((link) => {
        link.addEventListener("click", function (e) {
          e.preventDefault();
          const targetId = this.getAttribute("href");
          const targetElement = document.querySelector(targetId);
          if (targetElement) {
            const offsetTop = targetElement.getBoundingClientRect().top + window.scrollY - 100;
            window.scrollTo({
              top: offsetTop,
              behavior: "smooth",
            });
          }
        });
      });

      // Accordion toggle for .menu-btn and .smenu-btn
      const menuButtons = document.querySelectorAll(".menu-btn, .smenu-btn");
      menuButtons.forEach((button) => {
        button.addEventListener("click", function (e) {
          e.preventDefault();
          this.classList.toggle("active");

          const parent = this.parentElement;
          if (parent) {
            const contents = parent.querySelectorAll(".child-menucontent");
            contents.forEach((content) => {
              content.classList.toggle("active");
            });
          }
        });
      });
    }, 2500);
  }, []);

  useEffect(() => {
      setTimeout(() => {
        const sections = gsap.utils.toArray(".single-items");
        const navLinks = document.querySelectorAll("#mainNav .nav-link");

        sections.forEach((section) => {
          const id = section.getAttribute("id");
          ScrollTrigger.create({
              trigger: section,
              start: "top center",
              end: "bottom center",
              onEnter: () => setActiveLink(id),
              onEnterBack: () => setActiveLink(id),
          });
        });

        function setActiveLink(id) {
          navLinks.forEach((link) => {
              const href = link.getAttribute("href");
              link.classList.toggle("active", href === `#${id}`);
          });
        }
        ScrollTrigger.refresh();
      }, 1000);
  }, [value])
  

  return (
    <Fragment>
      <header>
        <div className="container header-container d-flex justify-content-between align-items-center">
          <div className="hero-block-logo-hp">
            <a href="#">
              <img
                width="160"
                alt="Logo"
                src="https://phplaravel-1312572-5496212.cloudwaysapps.com/uploads/settings/1747402069_1747379288_logo.png"
              />
            </a>
          </div>
          <div className="bergur-menu sidebar-btn">
            <svg
              width="30"
              viewBox="0 0 108 109"
              xmlns="http://www.w3.org/2000/svg"
            >
              <rect width="108" height="23" />
              <rect y="43" width="108" height="23" />
              <rect y="86" width="108" height="23" />
            </svg>
          </div>
        </div>
      </header>

      <div className="content-section">
        <div className="container">
          <div className="row">
            <div className="col-lg-3 position-relative">
              <div className="sidebar">
                <nav id="mainNav" className="navbar-nav nav flex-column">
                  <a className="nav-link" href="#section1">About</a>
                  <a className="nav-link" href="#section2">Methodology</a>
                  <a className="nav-link" href="#section3">Investigation</a>
                  <a className="nav-link" href="#section4">Team</a>
                  <a className="nav-link" href="#section5">News</a>
                  <a className="nav-link" href="#section6">Contacts</a>
                </nav>
              </div>
            </div>
            <div className="col-lg-9">
              <div className="content-area" id="main-content">
                <About />
                <Methodology />
                <Investigation setValue={setValue} />
                <Team />
                <News setValue={setValue} />
                <Contact />
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* <Footer /> */}
    </Fragment>
  );
};

const root = ReactDOM.createRoot(document.getElementById('react-root'));
root.render(<App />);
