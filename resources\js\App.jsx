import React, { Fragment, useEffect } from 'react';
import ReactDOM from 'react-dom/client';
import gsap from 'gsap';
import ScrollTrigger from 'gsap/ScrollTrigger';
import Header from './partials/Header';
import Footer from './partials/Footer';

gsap.registerPlugin(ScrollTrigger);

const App = () => {
    useEffect(() => {
        setTimeout(() => {
            // const sidebarButtons = document.querySelectorAll('.sidebar-btn');
            // sidebarButtons.forEach(button => {
            //     button.addEventListener('click', () => {
            //         document.querySelector('.sidebar').classList.toggle('active');
            //     });
            // });
            function animateSplitTextAll(elements, totalDuration = 0.5) {
                if (typeof elements === "string") {
                    elements = document.querySelectorAll(elements);
                }
                elements.forEach((el) => {
                    const text = el.dataset.title || "";
                    const chars = text.split("");
                    const delayPerChar = totalDuration / chars.length;
                    el.innerHTML = "";
                    chars.forEach((char, i) => {
                        const span = document.createElement("span");
                        span.className = "fade-in-char";
                        span.style.animationDelay = `${i * delayPerChar}s`;
                        span.textContent = char;
                        el.appendChild(span);
                    });
                });
            }

            animateSplitTextAll(".animated-title");

            const isMobile = window.matchMedia("(max-width: 768px)").matches;

            if (!isMobile) {
                gsap.utils.toArray(".nav-item.dropdown").forEach((dropdown) => {
                    const pinTarget = dropdown.querySelector(".single-menu-btn");
                    if (!pinTarget) return;

                    ScrollTrigger.create({
                        trigger: dropdown,
                        start: "top 15px",
                        end: "bottom-=70 top",
                        pin: pinTarget,
                        pinSpacing: false,
                        onEnter: () => {
                            animateSplitTextAll(
                                dropdown.querySelectorAll(".animated-title"),
                                0.5
                            );
                        },
                    });
                });
            }
        }, 3500);
    }, []);

    return (
        <Fragment>
            <header>
                <div className="container header-container">
                    <div className="hero-block-logo-hp">
                        <a onClick={() => window.location.reload()} href="#">
                            <img
                                width="160"
                                src="https://phplaravel-1312572-5496212.cloudwaysapps.com/uploads/settings/1747402069_1747379288_logo.png"
                                alt="Logo"
                            />
                        </a>
                    </div>
                    <div className="bergur-menu sidebar-btn">
                        <svg width="30" viewBox="0 0 108 109" xmlns="http://www.w3.org/2000/svg">
                            <rect width="108" height="23" />
                            <rect y="43" width="108" height="23" />
                            <rect y="86" width="108" height="23" />
                        </svg>
                    </div>
                </div>
            </header>
            <section className="sidebar hero-section-hp"  style={{ marginTop: "4em" }}>
                <section className="hero-section-hp">
                    <div className="hero-block-main-hp">
                        <div className="container-fluid">
                            <div className="row">
                                <div className="col-lg-12 col-sm-12 col-md-12 col-12 hero-block-in-hp">

                                    <div className="navigation-main-hp">
                                        <div className="navigation-in-hp">
                                            <Header />
                                        </div>

                                        <div className="container-one">
                                            <Footer />
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </section>
            </section>

            <section className="hero-section-hp" style={{ marginTop: "4em" }}>
                <div className="hero-block-main-hp">
                    <div className="container-fluid">
                        <div className="row">
                            <div className="col-lg-12 col-sm-12 col-md-12 col-12 hero-block-in-hp">
                                <div className="navigation-main-hp">
                                    <div className="navigation-in-hp">
                                        <Header />
                                    </div>

                                    <div className="container-one">
                                        <Footer />
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </section>

            <div className="clearfix"></div>
        </Fragment>
    );
};

const root = ReactDOM.createRoot(document.getElementById('react-root'));
root.render(<App />);
