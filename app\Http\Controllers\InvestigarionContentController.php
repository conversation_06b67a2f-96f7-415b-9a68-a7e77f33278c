<?php

namespace App\Http\Controllers;

use App\Models\investigarion_content;
use Illuminate\Http\Request;

class InvestigarionContentController extends Controller
{
    /**
     * Display a listing of the resource.
     */ 
    public function index()
    {
        //
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        //
    }

    /**
     * Display the specified resource.
     */
    public function show(investigarion_content $investigarion_content)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(investigarion_content $investigarion_content)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, investigarion_content $investigarion_content)
    {
        //
         $investigation_id = $request->content_id;

         investigarion_content::findOrFail($investigation_id)->update([
            'sub_title' => $request->subtitle,
            'description'=>$request->details
          
        ]);
        return redirect()
        ->route('investigation');
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(investigarion_content $investigarion_content,$id)
    {
        //
        investigarion_content::findOrFail($id)->delete();

        return redirect()
        ->route('investigation');
    }
}
