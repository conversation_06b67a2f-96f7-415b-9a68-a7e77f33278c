<?xml version="1.0" standalone="no"?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN" "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd" >
<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" version="1.1">
<metadata></metadata>
<defs>
<font id="neue_montrealregular" horiz-adv-x="1081" >
<font-face units-per-em="2048" ascent="1587" descent="-461" />
<missing-glyph horiz-adv-x="409" />
<glyph horiz-adv-x="0" />
<glyph horiz-adv-x="682" />
<glyph unicode=" "  horiz-adv-x="409" />
<glyph unicode="&#x09;" horiz-adv-x="409" />
<glyph unicode="&#xa0;" horiz-adv-x="409" />
<glyph unicode="!" horiz-adv-x="487" d="M141 98q0 50 32.5 81.5t80.5 31.5t80.5 -31.5t32.5 -81.5q0 -51 -32 -81.5t-81 -30.5t-81 30.5t-32 81.5zM162 1044v420h184v-420l-41 -667h-102z" />
<glyph unicode="&#x22;" horiz-adv-x="632" d="M86 1464h164l-21 -438h-123zM383 1464h164l-21 -438h-123z" />
<glyph unicode="#" horiz-adv-x="1185" d="M63 424l25 143h201l61 316h-223l25 143h227l76 389h143l-76 -389h242l76 389h145l-76 -389h213l-26 -143h-215l-62 -316h240l-23 -143h-245l-84 -424h-144l82 424h-242l-84 -424h-143l84 424h-197zM432 567h242l61 316h-241z" />
<glyph unicode="$" horiz-adv-x="1185" d="M33 471h184q29 -292 328 -330v514q-49 13 -86.5 25t-87 32t-86 40.5t-74.5 51t-62 65t-39.5 80.5t-15.5 98q0 170 121 274.5t330 110.5v118h123v-127q175 -28 288.5 -144t126.5 -310h-184q-26 239 -231 288v-458q53 -17 85 -27.5t77.5 -27.5t73 -31t62 -35t55 -42.5 t43 -50.5t34 -61.5t19 -73.5t7.5 -88q0 -164 -117 -273t-339 -118v-145h-123v149q-105 12 -193.5 48.5t-158 97t-111.5 150t-49 200.5zM279 1067q0 -48 19 -84.5t59 -63t81.5 -44t106.5 -37.5v430q-123 -4 -194.5 -58.5t-71.5 -142.5zM668 135q137 7 204.5 68t67.5 159 q0 101 -60 155t-212 101v-483z" />
<glyph unicode="%" horiz-adv-x="1665" d="M49 1038q0 113 35 203t111.5 146.5t183.5 56.5q83 0 147 -32.5t103.5 -89t59.5 -128.5t20 -156q0 -113 -34 -202.5t-110.5 -146t-185.5 -56.5q-107 0 -183.5 56.5t-111.5 146t-35 202.5zM193 1038q0 -121 47.5 -191.5t138.5 -70.5t138.5 70.5t47.5 191.5t-47.5 191.5 t-138.5 70.5t-138.5 -70.5t-47.5 -191.5zM389 0l744 1415h143l-744 -1415h-143zM956 377q0 113 35 202.5t111.5 146t183.5 56.5q109 0 185.5 -56.5t110.5 -146t34 -202.5q0 -84 -20 -156t-59.5 -128.5t-103.5 -89t-147 -32.5q-107 0 -183.5 56.5t-111.5 146.5t-35 203z M1100 377q0 -121 47.5 -191.5t138.5 -70.5t139 70.5t48 191.5t-48 191.5t-139 70.5t-138.5 -70.5t-47.5 -191.5z" />
<glyph unicode="&#x26;" horiz-adv-x="1202" d="M80 360q0 62 17 118t44 99t67.5 84.5t79.5 71t89 60.5q-87 108 -128.5 189t-41.5 173q0 64 21 123t62 107.5t108.5 77.5t152.5 30q157 1 250.5 -101.5t93.5 -236.5q0 -118 -73.5 -205t-227.5 -180l299 -369q62 134 84 291h164q-31 -241 -140 -424l218 -268h-195l-117 143 q-159 -172 -389 -172q-206 0 -322 108.5t-116 280.5zM244 360q0 -93 72.5 -159t201.5 -66q170 0 287 133l-346 422q-215 -144 -215 -330zM371 1143q0 -60 35.5 -123.5t103.5 -147.5q115 68 168 126t53 145q0 78 -48.5 132t-131.5 54t-131.5 -54t-48.5 -132z" />
<glyph unicode="'" horiz-adv-x="335" d="M86 1464h164l-21 -438h-123z" />
<glyph unicode="(" horiz-adv-x="622" d="M145 649q0 271 80 499.5t211 397.5h144q-120 -213 -185 -424t-65 -473q0 -257 65.5 -466.5t184.5 -422.5h-144q-130 168 -210.5 397t-80.5 492z" />
<glyph unicode=")" horiz-adv-x="622" d="M45 -240q119 213 184.5 422.5t65.5 466.5q0 262 -65 473t-185 424h143q131 -169 211 -397.5t80 -499.5q0 -263 -80.5 -492t-210.5 -397h-143z" />
<glyph unicode="*" horiz-adv-x="753" d="M90 1210l37 95l195 -64v223h110v-223l195 64l37 -95l-195 -67l123 -178l-88 -62l-127 189l-127 -189l-88 62l123 178z" />
<glyph unicode="+" horiz-adv-x="1146" d="M109 586v162h383v395h163v-395h383v-162h-383v-396h-163v396h-383z" />
<glyph unicode="," horiz-adv-x="405" d="M90 92q0 56 31.5 87.5t79.5 31.5q56 0 85 -43t29 -117q0 -45 -10 -91t-31.5 -93.5t-63.5 -82t-99 -44.5v102q47 10 74.5 49.5t27.5 96.5q-50 -16 -86.5 11.5t-36.5 92.5z" />
<glyph unicode="-" horiz-adv-x="704" d="M78 440v164h549v-164h-549z" />
<glyph unicode="." horiz-adv-x="405" d="M90 98q0 50 32.5 81.5t80.5 31.5t80 -31.5t32 -81.5q0 -51 -31.5 -81.5t-80.5 -30.5t-81 30.5t-32 81.5z" />
<glyph unicode="/" horiz-adv-x="829" d="M33 0l567 1464h164l-567 -1464h-164z" />
<glyph unicode="0" horiz-adv-x="1269" d="M90 707q0 123 19 231t61.5 201.5t105 160.5t154.5 105.5t205 38.5t205 -38.5t154.5 -105.5t105 -160.5t61.5 -201.5t19 -231t-19 -231t-61.5 -201.5t-105 -160.5t-154.5 -105t-205 -38t-205 38t-154.5 105t-105 160.5t-61.5 201.5t-19 231zM274 707q0 -94 11.5 -175.5 t38 -155.5t67.5 -126.5t103 -83.5t141 -31q98 0 169.5 46t112 128t59.5 180.5t19 217.5t-19 217.5t-59.5 180.5t-112 128.5t-169.5 46.5t-170 -46.5t-112.5 -128.5t-59.5 -181t-19 -217z" />
<glyph unicode="1" horiz-adv-x="708" d="M70 977v133q91 0 154 21t100 63.5t55.5 94.5t26.5 126h143v-1415h-184v1059q-25 -24 -52.5 -40t-50.5 -24.5t-59.5 -12.5t-60 -4.5t-72.5 -0.5z" />
<glyph unicode="2" horiz-adv-x="1138" d="M63 0q0 361 422 608q29 17 74.5 43t68.5 39t58.5 35t52.5 35t42 33t37 36t27 38t21 44t10.5 49t4.5 58q0 49 -12.5 92t-41 83t-84.5 63.5t-133 23.5q-321 0 -321 -360h-185q0 112 31 206t92 165.5t160 112t227 40.5q123 0 224 -48.5t164 -146.5t63 -229q0 -99 -38 -181.5 t-101 -141t-139.5 -109.5t-156.5 -96.5t-150 -91t-122.5 -105t-70.5 -127.5v-4h757v-164h-981z" />
<glyph unicode="3" horiz-adv-x="1165" d="M37 479h184q0 -344 363 -344q178 0 254.5 65t76.5 197q0 260 -299 260h-151v164h143q116 0 186 57.5t70 174.5q0 95 -67.5 161t-202.5 66q-336 0 -336 -289h-184q0 203 133.5 328t386.5 125q123 0 225 -44.5t166 -135t64 -211.5q0 -214 -218 -301v-4 q144 -39 206.5 -128.5t62.5 -222.5q0 -205 -135 -315.5t-367 -110.5q-269 0 -415 133.5t-146 374.5z" />
<glyph unicode="4" horiz-adv-x="1159" d="M53 332v164l682 919h185v-919h194v-164h-194v-332h-185v332h-682zM242 496h493v663z" />
<glyph unicode="5" horiz-adv-x="1132" d="M47 408h184q6 -55 26 -101t56.5 -86t97 -63t140.5 -23q150 0 241 91t91 257q0 153 -84 228t-228 75q-205 0 -309 -133h-196l147 762h778v-164h-629l-83 -428v-4q115 131 335 131q197 0 325 -134t128 -333q0 -149 -67.5 -267t-183 -181.5t-257.5 -63.5q-118 0 -215 36 t-160 97.5t-98.5 139.5t-38.5 164z" />
<glyph unicode="6" horiz-adv-x="1191" d="M90 709q0 355 137 545t400 190q190 0 311 -112t137 -275h-184q-14 106 -80.5 164.5t-183.5 58.5q-71 0 -127.5 -21.5t-94 -62t-63.5 -90.5t-40 -117t-20.5 -131t-7.5 -141v-4q52 99 153 155t226 56q204 0 331.5 -128.5t127.5 -338.5q0 -132 -57.5 -241.5t-171 -177 t-262.5 -67.5q-142 0 -245.5 51t-165.5 149.5t-91 231t-29 306.5zM295 440q0 -138 87 -221.5t239 -83.5q145 0 226 83t81 226q0 149 -81 232.5t-210 83.5q-153 0 -247.5 -88.5t-94.5 -231.5z" />
<glyph unicode="7" horiz-adv-x="1050" d="M47 1251v164h981v-143q-160 -134 -293 -345t-211 -457t-78 -470h-194q0 170 53.5 361.5t138 357t183.5 304t196 224.5v4h-776z" />
<glyph unicode="8" horiz-adv-x="1196" d="M68 397q0 71 21 129t60 100t82 71t99 53v4q-191 101 -191 303q0 180 126.5 283.5t332.5 103.5t332.5 -103.5t126.5 -283.5q0 -200 -191 -303v-4q55 -24 98 -53t82 -71t60.5 -100t21.5 -129q0 -206 -146.5 -316t-383.5 -110t-383.5 110t-146.5 316zM252 403 q0 -130 92.5 -199t253.5 -69t253.5 69t92.5 199q0 127 -93 197t-253 70t-253 -70t-93 -197zM324 1057q0 -114 72.5 -168.5t201.5 -54.5t201.5 54.5t72.5 168.5q0 112 -73 167.5t-201 55.5t-201 -55.5t-73 -167.5z" />
<glyph unicode="9" horiz-adv-x="1185" d="M82 958q0 132 57.5 241.5t171 177t262.5 67.5q142 0 245.5 -51t165.5 -149t91 -230.5t29 -306.5q0 -355 -137.5 -545.5t-399.5 -190.5q-190 0 -311 112t-137 275h184q14 -106 80.5 -164.5t183.5 -58.5q71 0 127.5 21.5t94 62t63.5 90.5t40 117t20.5 131t7.5 141v4 q-52 -99 -153 -154.5t-226 -55.5q-204 0 -331.5 128t-127.5 338zM266 971q0 -149 81 -232.5t210 -83.5q153 0 247.5 88.5t94.5 231.5q0 138 -87 221.5t-239 83.5q-145 0 -226 -83t-81 -226z" />
<glyph unicode=":" horiz-adv-x="417" d="M96 98q0 50 32.5 81.5t80.5 31.5t80.5 -31.5t32.5 -81.5q0 -51 -32 -81.5t-81 -30.5t-81 30.5t-32 81.5zM96 723q0 50 32.5 81.5t80.5 31.5t80.5 -31.5t32.5 -81.5q0 -51 -32 -82t-81 -31t-81 31t-32 82z" />
<glyph unicode=";" horiz-adv-x="417" d="M96 92q0 56 31.5 87.5t79.5 31.5q56 0 85.5 -43t29.5 -117q0 -45 -10 -91.5t-32 -93.5t-64 -81.5t-99 -44.5v102q47 10 74.5 49.5t27.5 96.5q-50 -16 -86.5 11.5t-36.5 92.5zM96 723q0 50 32.5 81.5t80.5 31.5t80.5 -31.5t32.5 -81.5q0 -51 -32 -82t-81 -31t-81 31 t-32 82z" />
<glyph unicode="&#x3c;" horiz-adv-x="1095" d="M102 545v184l834 348v-164l-700 -274v-4l700 -273v-163z" />
<glyph unicode="=" horiz-adv-x="1146" d="M109 397v164h929v-164h-929zM109 723v164h929v-164h-929z" />
<glyph unicode="&#x3e;" horiz-adv-x="1046" d="M111 199v163l700 273v4l-700 274v164l833 -348v-184z" />
<glyph unicode="?" horiz-adv-x="1032" d="M35 989q0 228 125.5 366t369.5 138q189 0 315 -106t126 -269q0 -49 -11.5 -92t-25.5 -71.5t-44.5 -61.5t-51 -51t-61.5 -51q-14 -11 -21 -17q-3 -3 -10 -8q-90 -73 -125 -118q-80 -104 -80 -273h-185q0 69 14.5 130t35 105.5t56 89.5t64 73.5t72.5 66.5q18 16 55 46 t54 46.5t39 45.5t31 62.5t9 77.5q0 92 -69.5 151.5t-186.5 59.5q-311 0 -311 -340h-184zM336 98q0 50 32.5 81.5t80.5 31.5t80 -31.5t32 -81.5q0 -51 -31.5 -81.5t-80.5 -30.5t-81 30.5t-32 81.5z" />
<glyph unicode="@" horiz-adv-x="1802" d="M68 604q0 231 108 426.5t305 312.5t441 117q183 0 337 -62.5t257.5 -170t161 -253.5t57.5 -312q0 -112 -23 -201t-60 -143.5t-85.5 -91t-96 -50.5t-96.5 -14q-93 0 -153.5 42t-102.5 124h-4q-97 -158 -295 -158q-147 0 -244.5 114t-97.5 306q0 208 110.5 338.5 t272.5 130.5q100 0 181 -52t110 -126h4l23 163h143l-66 -514q-14 -107 18.5 -166t112.5 -59q95 0 150 89.5t55 260.5q0 294 -183.5 478t-485.5 184q-157 0 -292.5 -60.5t-226 -160.5t-141.5 -228t-51 -264q0 -148 53.5 -275.5t145.5 -215.5t219 -137.5t272 -49.5 q214 0 367 62l51 -117q-167 -88 -418 -88q-179 0 -332 57.5t-264 162.5t-174 260t-63 341zM621 584q0 -123 57 -197t170 -74q124 0 195 94.5t71 237.5q0 130 -64.5 200t-162.5 70q-127 0 -196.5 -93t-69.5 -238z" />
<glyph unicode="A" horiz-adv-x="1325" d="M20 0l541 1464h205l539 -1464h-191l-129 367h-645l-129 -367h-191zM401 530h525l-260 764h-4z" />
<glyph unicode="B" horiz-adv-x="1318" d="M137 0v1464h590q224 0 351.5 -98.5t127.5 -269.5q0 -226 -227 -303v-5q124 -22 204.5 -114t80.5 -248q0 -201 -133.5 -313.5t-362.5 -112.5h-631zM322 164h464q293 0 293 262q0 119 -68.5 190.5t-183.5 71.5h-505v-524zM322 852h434q148 0 207 57.5t59 165.5 q0 104 -62 164.5t-184 60.5h-454v-448z" />
<glyph unicode="C" horiz-adv-x="1452" d="M82 733q0 218 85 391t240.5 271t352.5 98q131 0 243 -40.5t190 -111t127 -163t62 -197.5h-184q-9 46 -26.5 90t-52 92t-81 83.5t-119 59t-159.5 23.5q-101 0 -184.5 -34t-140 -91t-95.5 -134t-56.5 -161.5t-17.5 -175.5t17.5 -176t56.5 -162.5t95.5 -134.5t140 -91 t184.5 -34q89 0 162 23.5t121.5 61.5t83 90t51 106t20.5 112h184q-17 -163 -95 -288t-214.5 -197t-312.5 -72q-198 0 -353 98t-240 271.5t-85 392.5z" />
<glyph unicode="D" horiz-adv-x="1400" d="M137 0v1464h531q305 0 478 -191.5t173 -541.5t-173 -540.5t-478 -190.5h-531zM322 164h346q108 0 191.5 32t134.5 84.5t83.5 127.5t45 153t12.5 170t-12.5 170t-45 153.5t-83.5 128.5t-134.5 85t-191.5 32h-346v-1136z" />
<glyph unicode="E" horiz-adv-x="1273" d="M137 0v1464h1034v-164h-849v-460h751v-164h-751v-512h874v-164h-1059z" />
<glyph unicode="F" horiz-adv-x="1183" d="M137 0v1464h993v-164h-808v-475h653v-163h-653v-662h-185z" />
<glyph unicode="G" horiz-adv-x="1519" d="M82 733q0 218 84.5 391t240 271t355.5 98q268 0 422 -126t194 -345h-184q-31 152 -146 229.5t-286 77.5q-101 0 -185 -36t-140.5 -95t-96 -137t-57 -160.5t-17.5 -167.5q0 -88 13.5 -168.5t48 -160.5t86.5 -138.5t136 -94.5t191 -36q131 0 239.5 58t173.5 164.5t65 240.5 h-478v164h662v-762h-123l-61 207h-5q-86 -120 -197.5 -178t-275.5 -58q-208 0 -358.5 99t-225.5 269.5t-75 393.5z" />
<glyph unicode="H" horiz-adv-x="1425" d="M137 0v1464h185v-622h782v622h184v-1464h-184v678h-782v-678h-185z" />
<glyph unicode="I" horiz-adv-x="466" d="M141 0v1464h185v-1464h-185z" />
<glyph unicode="J" horiz-adv-x="1017" d="M45 446v41h184v-41q0 -59 4.5 -101t19.5 -84t41 -68t70.5 -42t106.5 -16t107 16t71 42t41 68t20 84.5t5 100.5v1018h184v-1018q0 -75 -12.5 -141t-43.5 -128.5t-78.5 -107t-122.5 -71.5t-171 -27t-171 27t-122 71.5t-77.5 107t-43 128.5t-12.5 141z" />
<glyph unicode="K" horiz-adv-x="1318" d="M137 0v1464h185v-784l735 784h225l-575 -608l616 -856h-215l-524 729l-262 -276v-453h-185z" />
<glyph unicode="L" horiz-adv-x="1134" d="M137 0v1464h185v-1300h768v-164h-953z" />
<glyph unicode="M" horiz-adv-x="1699" d="M137 0v1464h252l459 -1216h4l459 1216h252v-1464h-185v1204h-4l-442 -1204h-164l-442 1204h-4v-1204h-185z" />
<glyph unicode="N" horiz-adv-x="1419" d="M137 0v1464h225l732 -1206h4v1206h184v-1464h-225l-731 1202h-4v-1202h-185z" />
<glyph unicode="O" horiz-adv-x="1597" d="M82 733q0 219 89 391.5t253 270.5t375 98t375 -98t253 -270.5t89 -391.5t-89 -392t-253 -271.5t-375 -98.5t-375 98.5t-253 271.5t-89 392zM266 733q0 -96 19.5 -183t62 -163.5t104 -132t150.5 -87.5t197 -32q134 0 238 48.5t167 133t95 190t32 226.5t-32 226.5t-95 189 t-167 132t-238 48.5q-108 0 -197 -32t-150.5 -87t-104 -131t-62 -163t-19.5 -183z" />
<glyph unicode="P" horiz-adv-x="1234" d="M137 0v1464h582q104 0 188 -24t149.5 -73.5t101.5 -133t36 -195.5t-36 -195.5t-101.5 -133t-149.5 -73.5t-188 -24h-397v-612h-185zM322 776h417q137 0 204 64.5t67 197.5t-67 197.5t-204 64.5h-417v-524z" />
<glyph unicode="Q" horiz-adv-x="1597" d="M82 733q0 219 89 391.5t253 270.5t375 98t375 -98t253 -270.5t89 -391.5q0 -308 -175 -518l172 -172l-116 -117l-174 174q-181 -129 -424 -129q-211 0 -375 98.5t-253 271.5t-89 392zM266 733q0 -96 19.5 -183t62 -163.5t104 -132t150.5 -87.5t197 -32q173 0 303 86 l-203 203l117 117l203 -203q112 156 112 395q0 121 -32 226.5t-95 189t-167 132t-238 48.5q-108 0 -197 -32t-150.5 -87t-104 -131t-62 -163t-19.5 -183z" />
<glyph unicode="R" horiz-adv-x="1296" d="M137 0v1464h594q219 0 351 -111t132 -304q0 -154 -68.5 -245.5t-193.5 -123.5v-4q77 -15 128.5 -69.5t73 -125t33.5 -151.5t13.5 -149t9.5 -115.5t25 -53.5v-12h-199q-14 12 -18 64q-4 42 -4 93v23v11q0 59 -10 127q-12 74 -35.5 134t-81.5 100t-146 40h-419v-592h-185z M322 756h366q55 0 95 3.5t90 19.5t81.5 43.5t53.5 80t22 125.5t-22 125.5t-53.5 80t-81.5 43.5t-90 19.5t-95 3.5h-366v-544z" />
<glyph unicode="S" horiz-adv-x="1282" d="M45 492h184q8 -78 36.5 -141t81 -112.5t137 -76.5t196.5 -27q178 0 261 69.5t83 178.5q0 38 -4.5 68t-18 55.5t-28.5 44t-44.5 36t-57.5 30t-77 27t-92.5 25.5t-113.5 27q-56 13 -96.5 24t-94.5 30t-91.5 39.5t-78 52.5t-65 68.5t-40.5 87.5t-16 110q0 176 131.5 280.5 t370.5 104.5q238 0 386.5 -120t166.5 -343h-184q-21 150 -110 224.5t-259 74.5q-152 0 -234.5 -54t-82.5 -147q0 -54 17.5 -94t43 -64.5t73.5 -45.5t90 -32.5t111 -28.5q25 -6 38 -9q90 -22 135.5 -34.5t115 -36t103.5 -44.5t78 -56t63.5 -75.5t34 -97.5t14.5 -127 q0 -82 -31 -154.5t-93 -131t-166 -92.5t-238 -34q-268 0 -444 138t-191 383z" />
<glyph unicode="T" horiz-adv-x="1249" d="M45 1300v164h1159v-164h-487v-1300h-185v1300h-487z" />
<glyph unicode="U" horiz-adv-x="1347" d="M113 487v977h184v-977q0 -178 93 -265t284 -87t284 87t93 265v977h184v-977q0 -114 -33 -207t-100 -163t-176 -108t-252 -38t-252 38t-176 108t-100 163t-33 207z" />
<glyph unicode="V" horiz-adv-x="1267" d="M20 1464h195l418 -1280h2l418 1280h194l-512 -1464h-203z" />
<glyph unicode="W" horiz-adv-x="1871" d="M29 1464h194l279 -1200h4l328 1200h204l328 -1200h4l279 1200h194l-362 -1464h-205l-338 1255h-4l-338 -1255h-205z" />
<glyph unicode="X" horiz-adv-x="1261" d="M29 0l499 748l-465 716h205l361 -585h2l362 585h205l-465 -712l500 -752h-225l-377 616h-2l-375 -616h-225z" />
<glyph unicode="Y" horiz-adv-x="1292" d="M20 1464h199l424 -716h4l426 716h199l-535 -864v-600h-184v600z" />
<glyph unicode="Z" horiz-adv-x="1273" d="M61 0v164l893 1132v4h-852v164h1078v-164l-893 -1132v-4h913v-164h-1139z" />
<glyph unicode="[" horiz-adv-x="606" d="M158 -240v1786h387v-164h-203v-1458h203v-164h-387z" />
<glyph unicode="\" horiz-adv-x="796" d="M33 1464h164l567 -1464h-164z" />
<glyph unicode="]" horiz-adv-x="606" d="M61 -76h203v1458h-203v164h388v-1786h-388v164z" />
<glyph unicode="^" horiz-adv-x="927" d="M23 901l360 563h164l358 -563h-182l-258 414h-2l-256 -414h-184z" />
<glyph unicode="_" horiz-adv-x="835" d="M-4 -176h844v-164h-844v164z" />
<glyph unicode="`" horiz-adv-x="1366" d="M285 1485h194l88 -260h-133z" />
<glyph unicode="a" horiz-adv-x="1044" d="M49 268q0 70 21.5 123t56.5 88t96 62.5t122 43.5t151 33q146 27 201 57t55 103q0 152 -213 152q-147 0 -207 -47t-68 -162h-164q8 161 115.5 256.5t323.5 95.5q62 0 115.5 -10t102.5 -34t83 -61t54.5 -94.5t20.5 -130.5v-507q0 -68 23 -96q18 -21 66 -21q14 0 30 2v-115 q-49 -16 -96 -16q-79 0 -122 29.5t-64 111.5h-4q-108 -160 -355 -160q-157 0 -250.5 82t-93.5 215zM213 276q0 -161 197 -161q155 0 248.5 72t93.5 216v175q-40 -40 -242 -82q-159 -34 -228 -82t-69 -138z" />
<glyph unicode="b" horiz-adv-x="1132" d="M113 0v1464h163v-555h5q111 164 327 164q98 0 184 -38t149.5 -108t100.5 -174.5t37 -230.5t-37 -230.5t-100.5 -174.5t-149.5 -108t-184 -38q-207 0 -327 158h-5v-129h-163zM276 522q0 -185 85 -296t231 -111q103 0 178 60t110 150.5t35 196.5t-35 197t-110 151t-178 60 q-145 0 -230.5 -111t-85.5 -297z" />
<glyph unicode="c" horiz-adv-x="1062" d="M53 522q0 247 139 399t367 152q190 0 315 -113.5t144 -291.5h-164q-3 46 -20.5 90t-51 84t-91 64t-132.5 24q-72 0 -130.5 -23.5t-97 -63t-65 -93t-38 -110.5t-11.5 -118q0 -77 19 -147t58 -129.5t107.5 -95t157.5 -35.5q76 0 133.5 24t90.5 64.5t50.5 86t20.5 95.5h164 q-11 -180 -139 -297t-320 -117q-228 0 -367 152t-139 399z" />
<glyph unicode="d" horiz-adv-x="1132" d="M53 522q0 126 37 230.5t100.5 174.5t149.5 108t184 38q217 0 328 -164h4v555h164v-1464h-164v129h-4q-120 -158 -328 -158q-98 0 -184 38t-149.5 108t-100.5 174.5t-37 230.5zM217 522q0 -106 35 -196.5t110.5 -150.5t178.5 -60q145 0 230 110.5t85 296.5t-85 297 t-230 111q-78 0 -141 -34.5t-102 -92.5t-60 -130.5t-21 -150.5z" />
<glyph unicode="e" horiz-adv-x="1091" d="M53 522q0 259 135.5 405t366.5 146q242 0 364.5 -155t122.5 -439h-825q0 -97 36.5 -178t115.5 -133.5t188 -52.5q60 0 110 15t82.5 37t57 51t37 54.5t18.5 49.5h164q-43 -157 -160.5 -254t-302.5 -97q-235 0 -372.5 151t-137.5 400zM217 623h662q0 129 -90 218t-234 89 q-146 0 -242 -89.5t-96 -217.5z" />
<glyph unicode="f" horiz-adv-x="600" d="M45 901v143h145v164q0 149 65 209t181 60q57 0 117 -13v-143q-26 2 -49 2q-72 0 -106 -19q-44 -26 -44 -112v-148h199v-143h-199v-901h-164v901h-145z" />
<glyph unicode="g" horiz-adv-x="1132" d="M53 563q0 126 36.5 224.5t100.5 160t148 93.5t182 32q212 0 332 -158h4v129h164v-987q0 -206 -122 -312.5t-341 -106.5q-207 0 -333.5 94.5t-139.5 234.5h164q16 -86 89 -136t212 -50q307 0 307 278v158h-4q-111 -164 -332 -164q-98 0 -182 32t-148 93.5t-100.5 160 t-36.5 224.5zM217 563q0 -161 84.5 -263.5t239.5 -102.5q149 0 232 92.5t83 273.5t-83 274t-232 93q-155 0 -239.5 -103t-84.5 -264z" />
<glyph unicode="h" d="M113 0v1464h163v-544h5q48 65 134.5 109t209.5 44q73 0 135 -20.5t111 -61t76.5 -108t27.5 -154.5v-729h-164v688q0 109 -55 175.5t-172 66.5q-137 0 -222.5 -78.5t-85.5 -200.5v-651h-163z" />
<glyph unicode="i" horiz-adv-x="430" d="M113 1346q0 44 32 71t70 27q39 0 70.5 -27t31.5 -71t-31.5 -71.5t-70.5 -27.5q-37 0 -69.5 27.5t-32.5 71.5zM133 0v1044h164v-1044h-164z" />
<glyph unicode="j" horiz-adv-x="411" d="M-57 -207q31 -2 56 -2q61 0 84 14q32 20 32 107v1132h164v-1116q0 -138 -42.5 -209t-142.5 -71q-70 0 -151 22v123zM94 1346q0 44 32.5 71t70.5 27q39 0 70.5 -27t31.5 -71t-31.5 -71.5t-70.5 -27.5q-38 0 -70.5 27.5t-32.5 71.5z" />
<glyph unicode="k" horiz-adv-x="1001" d="M113 0v1464h163v-868l480 448h205l-408 -387l451 -657h-195l-371 561l-162 -149v-412h-163z" />
<glyph unicode="l" horiz-adv-x="401" d="M119 0v1464h164v-1464h-164z" />
<glyph unicode="m" horiz-adv-x="1624" d="M113 0v1044h163v-124h5q114 153 303 153q212 0 278 -180h4q132 180 338 180q156 0 235 -88.5t79 -255.5v-729h-164v688q0 56 -7 95.5t-26.5 75t-59 53.5t-98.5 18q-121 0 -193.5 -78t-72.5 -201v-651h-164v688q0 56 -7 95.5t-26 75t-58.5 53.5t-98.5 18q-121 0 -194 -78 t-73 -201v-651h-163z" />
<glyph unicode="n" d="M113 0v1044h163v-124h5q48 65 134.5 109t209.5 44q73 0 135 -20.5t111 -61t76.5 -108t27.5 -154.5v-729h-164v688q0 109 -55 175.5t-172 66.5q-137 0 -222.5 -78.5t-85.5 -200.5v-651h-163z" />
<glyph unicode="o" horiz-adv-x="1128" d="M53 522q0 -248 138 -399.5t374 -151.5q235 0 372.5 152t137.5 399t-137.5 399t-372.5 152q-236 0 -374 -151.5t-138 -399.5zM217 522q0 177 89.5 292.5t258.5 115.5q167 0 256.5 -115.5t89.5 -292.5t-89.5 -292t-256.5 -115q-169 0 -258.5 115t-89.5 292z" />
<glyph unicode="p" horiz-adv-x="1138" d="M113 -352v1396h163v-129h5q120 158 331 158q97 0 182 -38t148.5 -108t100 -174.5t36.5 -230.5t-36.5 -230.5t-100 -174.5t-148.5 -108t-182 -38q-220 0 -331 164h-5v-487h-163zM276 522q0 -185 85 -296t231 -111q103 0 178 60t110 150.5t35 196.5t-35 197t-110 151 t-178 60q-145 0 -230.5 -111t-85.5 -297z" />
<glyph unicode="q" horiz-adv-x="1138" d="M59 522q0 126 36.5 230.5t100 174.5t148.5 108t182 38q212 0 332 -158h4v129h164v-1396h-164v487h-4q-111 -164 -332 -164q-97 0 -182 38t-148.5 108t-100 174.5t-36.5 230.5zM223 522q0 -106 35 -196.5t110.5 -150.5t178.5 -60q145 0 230 110.5t85 296.5t-85 297 t-230 111q-78 0 -141 -34.5t-102 -92.5t-60 -130.5t-21 -150.5z" />
<glyph unicode="r" horiz-adv-x="702" d="M113 0v1044h163v-206h5q45 112 113.5 173.5t174.5 61.5q53 0 97 -6v-164h-4q-35 6 -68 6q-122 -1 -208 -81q-110 -102 -110 -271v-557h-163z" />
<glyph unicode="s" horiz-adv-x="980" d="M45 348h164q3 -40 11 -69.5t28.5 -62.5t53 -53.5t88.5 -34t132 -13.5q122 0 185 50t63 111q0 28 -7 51t-16.5 39t-28.5 29.5t-34.5 22t-44.5 17t-47.5 13t-54 12.5t-54.5 13t-62.5 14t-68 15t-64.5 19t-64 26t-53.5 35t-45.5 48t-27.5 63.5t-11.5 82.5q0 127 106 212 t285 85q187 0 295 -92.5t121 -235.5h-164q-6 29 -13.5 50.5t-25.5 48.5t-43.5 44.5t-69 29.5t-100.5 12q-107 0 -167 -39.5t-60 -102.5q0 -30 12.5 -53t28 -37t50 -27t56 -18.5t70.5 -16t70 -15.5q12 -3 38 -9q70 -17 106.5 -27t93.5 -35.5t85.5 -56t51 -84t22.5 -122.5 q0 -141 -116 -228.5t-296 -87.5q-231 0 -350 100.5t-127 276.5z" />
<glyph unicode="t" horiz-adv-x="626" d="M45 901v143h154v349h163v-349h205v-143h-205v-629q0 -85 47 -111q36 -20 109 -20q23 0 49 2v-143q-55 -12 -123 -12q-116 0 -180.5 59.5t-64.5 208.5v645h-154z" />
<glyph unicode="u" d="M106 315v729h164v-688q0 -109 55.5 -175t172.5 -66q137 0 222 78t85 200v651h164v-1044h-164v125h-4q-48 -65 -135 -109.5t-209 -44.5q-73 0 -135.5 20.5t-111 61t-76.5 108t-28 154.5z" />
<glyph unicode="v" horiz-adv-x="944" d="M4 1044h178l289 -880h2l289 880h178l-375 -1044h-186z" />
<glyph unicode="w" horiz-adv-x="1503" d="M8 1044h180l238 -866h4l234 866h174l235 -866h4l238 866h180l-334 -1044h-174l-235 862h-4l-232 -862h-174z" />
<glyph unicode="x" horiz-adv-x="1030" d="M12 0l404 535l-369 509h195l270 -399h4l270 399h195l-369 -509l406 -535h-195l-307 426h-4l-305 -426h-195z" />
<glyph unicode="y" horiz-adv-x="1001" d="M4 1044h184l320 -851h4l301 851h184l-446 -1130q-44 -110 -73 -152q-78 -113 -218 -114q-72 0 -139 20v123h24q28 -3 52 -3q40 0 70 8q48 11 87 52q24 26 40 59.5t22 81.5q1 10 1 19q0 39 -18 78z" />
<glyph unicode="z" horiz-adv-x="964" d="M70 0v123l589 774v4h-555v143h787v-98l-608 -799v-4h630v-143h-843z" />
<glyph unicode="{" horiz-adv-x="606" d="M49 571v164q36 -1 60.5 14t36.5 43.5t17 60.5t5 75v221q0 103 13.5 173.5t38 114.5t68.5 68t93.5 32.5t124.5 8.5h39v-164h-39q-78 0 -116 -41.5t-38 -148.5v-318q0 -148 -121 -219v-4q58 -34 89.5 -85.5t31.5 -135.5v-307q0 -113 37.5 -156t116.5 -43h39v-164h-39 q-74 0 -124.5 9t-93.5 34.5t-68 71t-38.5 117t-13.5 174.5v211q0 43 -5 75t-17 60.5t-36.5 43.5t-60.5 15z" />
<glyph unicode="|" horiz-adv-x="458" d="M158 -262v1786h143v-1786h-143z" />
<glyph unicode="}" horiz-adv-x="606" d="M61 -76h39q79 0 116.5 43t37.5 156v307q0 84 31.5 135.5t89.5 85.5v4q-121 71 -121 219v318q0 107 -38 148.5t-116 41.5h-39v164h39q75 0 124.5 -8.5t93.5 -32.5t68.5 -68t38 -114.5t13.5 -173.5v-221q0 -43 5 -75t17 -60.5t36.5 -43.5t60.5 -14v-164q-36 0 -60.5 -15 t-36.5 -43.5t-17 -60.5t-5 -75v-211q0 -103 -13.5 -174.5t-38.5 -117t-68 -71t-93.5 -34.5t-124.5 -9h-39v164z" />
<glyph unicode="~" horiz-adv-x="804" d="M74 553q0 107 49 174.5t133 67.5q52 0 101.5 -22t88 -44t64.5 -22q41 0 59.5 27.5t18.5 60.5h143q0 -107 -49 -174.5t-133 -67.5q-51 0 -100.5 22t-88.5 44t-65 22q-41 0 -59.5 -27.5t-18.5 -60.5h-143z" />
<glyph unicode="&#xa1;" horiz-adv-x="507" d="M141 1100q0 51 32 81.5t81 30.5t81 -30.5t32 -81.5q0 -50 -32.5 -81.5t-80.5 -31.5t-80.5 31.5t-32.5 81.5zM162 154l41 667h102l41 -667v-420h-184v420z" />
<glyph unicode="&#xa2;" horiz-adv-x="1062" d="M53 522q0 224 116.5 372.5t313.5 174.5v141h123v-139q171 -15 283 -126t129 -277h-164q-3 43 -18 83.5t-43 78.5t-76.5 64.5t-110.5 33.5v-811q121 14 181 91t67 177h164q-11 -168 -124.5 -282.5t-287.5 -129.5v-147h-123v149q-197 26 -313.5 174.5t-116.5 372.5z M217 522q0 -68 15.5 -131.5t46 -119.5t83 -95.5t121.5 -52.5v799q-69 -13 -121.5 -52.5t-83 -95.5t-46 -119.5t-15.5 -132.5z" />
<glyph unicode="&#xa3;" horiz-adv-x="1128" d="M63 135q85 55 138 165t53 245q0 20 -2 30h-168v164h113q-25 58 -35 83t-23.5 83t-13.5 115q0 53 15 107.5t50.5 110t88 98t133.5 69.5t180 27q122 0 213.5 -36.5t146.5 -103t82 -152t27 -190.5h-184q0 155 -68 236.5t-217 81.5q-134 0 -208.5 -71.5t-74.5 -190.5 q0 -30 4 -58t14.5 -60.5t16 -47t23 -55t20.5 -46.5h318v-164h-269q2 -12 2 -40q0 -112 -44.5 -204t-121.5 -147l2 -4q6 3 28.5 15t36.5 18t37 14.5t47.5 12.5t51.5 4q60 0 115 -23t105.5 -46t99.5 -23q65 0 115 22.5t114 69.5l78 -150q-43 -32 -62.5 -45.5t-59.5 -36.5 t-79.5 -32t-85.5 -9q-60 0 -119 19.5t-98 43t-90 43t-96 19.5q-62 0 -114 -21t-136 -75z" />
<glyph unicode="&#xa4;" horiz-adv-x="995" d="M37 332l133 133q-72 101 -72 240q0 140 72 241l-133 133l88 86l131 -131q110 78 242 78q133 0 239 -80l131 131l90 -86l-135 -133q72 -101 72 -239q0 -135 -70 -238l133 -133l-88 -88l-131 131q-106 -80 -241 -80q-136 0 -242 80l-131 -131zM262 705q0 -131 65.5 -198 t170.5 -67q104 0 168.5 67t64.5 198t-64.5 197.5t-168.5 66.5q-105 0 -170.5 -66.5t-65.5 -197.5z" />
<glyph unicode="&#xa5;" horiz-adv-x="1150" d="M20 1405h199l354 -676h5l354 676h198l-438 -780h334v-123h-358v-117h358v-123h-358v-262h-185v262h-358v123h358v117h-358v123h334z" />
<glyph unicode="&#xa6;" horiz-adv-x="458" d="M158 -262v764h143v-764h-143zM158 786v738h143v-738h-143z" />
<glyph unicode="&#xa7;" horiz-adv-x="1191" d="M57 684q0 219 246 270q-90 107 -90 238t98.5 216t288.5 85q102 0 179.5 -27.5t123 -76.5t69.5 -108.5t27 -129.5h-163q-2 24 -4 36.5t-8.5 37t-15.5 39t-26.5 33t-41 28.5t-59.5 17.5t-81 7.5q-223 0 -223 -146q0 -26 10.5 -52.5t24.5 -48t40.5 -46.5t47 -41.5t56 -40 t55.5 -35t57.5 -33.5t50.5 -30q188 -112 247 -157q139 -104 163 -213q6 -28 6 -56q0 -112 -64 -181.5t-180 -91.5q88 -110 88 -235q0 -131 -98.5 -216t-288.5 -85q-102 0 -179.5 27.5t-123 76.5t-69.5 108.5t-27 129.5h163q2 -24 4 -36.5t8.5 -37t15.5 -39t26.5 -33 t41 -28.5t59.5 -17.5t81 -7.5q223 0 223 145q0 34 -18.5 69.5t-41 61t-66.5 58.5t-70 50t-78.5 48.5l-67.5 40.5q-30 18 -82 49t-79.5 47.5t-69 44.5t-63.5 47t-49.5 48t-40.5 56t-22.5 61.5t-9.5 72.5zM221 705q0 -23 7.5 -44.5t17.5 -38.5t32 -36.5t39.5 -33t52.5 -34 t57.5 -33t68 -36.5t71.5 -39q151 -84 217 -134q101 18 144 55t43 99q0 25 -10 49t-36.5 50t-49.5 45t-72.5 49t-80.5 48t-97 54q-157 87 -217 133q-102 -18 -144.5 -54.5t-42.5 -98.5z" />
<glyph unicode="&#xa8;" horiz-adv-x="1040" d="M264 1380q0 43 27.5 73t75.5 30t75 -30t27 -73q0 -42 -27.5 -71t-74.5 -29q-48 0 -75.5 29t-27.5 71zM571 1380q0 43 27.5 73t75.5 30t75 -30t27 -73q0 -42 -27.5 -71t-74.5 -29q-48 0 -75.5 29t-27.5 71z" />
<glyph unicode="&#xa9;" horiz-adv-x="1673" d="M82 731q0 163 53.5 304t150.5 243.5t239.5 160.5t312.5 58t312.5 -58t238.5 -160.5t149 -243t53 -304.5q0 -163 -53 -304t-149.5 -242.5t-238.5 -159.5t-312 -58t-312.5 58t-239.5 159.5t-150.5 242.5t-53.5 304zM184 731q0 -140 46.5 -264.5t129.5 -217t207 -146.5 t271 -54q194 0 344 92.5t228.5 247t78.5 342.5q0 189 -78.5 344t-228.5 247.5t-344 92.5q-147 0 -271 -54t-207 -146.5t-129.5 -217.5t-46.5 -266zM424 731q0 207 107.5 334t302.5 127q167 0 273 -94.5t122 -235.5h-144q-3 18 -10 38.5t-25 48t-43.5 49t-69 36.5t-97.5 15 q-77 0 -132.5 -26t-85 -72.5t-42.5 -100t-13 -119.5q0 -65 13 -118t42.5 -99.5t85 -72t132.5 -25.5q67 0 116.5 20.5t75 53t37.5 60t16 54.5h144q-9 -142 -111.5 -237t-267.5 -95q-201 0 -313.5 126t-112.5 333z" />
<glyph unicode="&#xab;" horiz-adv-x="786" d="M61 424v184l281 234v-164l-195 -160v-4l195 -160v-164zM444 424v184l281 234v-164l-195 -160v-4l195 -160v-164z" />
<glyph unicode="&#xac;" horiz-adv-x="1146" d="M129 651v164h848v-520h-184v356h-664z" />
<glyph unicode="&#xad;" horiz-adv-x="704" d="M78 440v164h549v-164h-549z" />
<glyph unicode="&#xae;" d="M53 1001q0 212 141.5 354t346.5 142t346 -142t141 -354t-141 -353.5t-346 -141.5q-206 0 -347 141.5t-141 353.5zM135 1001q0 -178 117.5 -295.5t288.5 -117.5t288 117.5t117 295.5q0 177 -117.5 295.5t-287.5 118.5t-288 -118.5t-118 -295.5zM360 743v527h213 q79 0 127 -41t48 -109q0 -110 -95 -133v-2q40 -8 61 -45.5t23.5 -78.5t5.5 -76t11 -37v-5h-92q-6 6 -8 42.5t-5 73t-31 67t-81 30.5h-91v-213h-86zM446 1034h91q24 0 34.5 0.5t31 4t30 11t18 23.5t8.5 39q0 24 -8.5 39.5t-17 23t-31 11.5t-30.5 4h-35h-91v-156z" />
<glyph unicode="&#xaf;" horiz-adv-x="1024" d="M248 1290v144h528v-144h-528z" />
<glyph unicode="&#xb0;" horiz-adv-x="753" d="M70 1186q0 53 19 106t55 98.5t96.5 74t136.5 28.5q75 0 135.5 -28.5t97 -74t55.5 -98.5t19 -106t-19 -106t-55.5 -98.5t-97 -74t-135.5 -28.5q-76 0 -136.5 28.5t-96.5 74t-55 98.5t-19 106zM193 1186q0 -80 50 -132.5t134 -52.5t134 52.5t50 132.5t-50 132t-134 52 t-134 -52t-50 -132z" />
<glyph unicode="&#xb1;" horiz-adv-x="1146" d="M109 0v143h929v-143h-929zM109 629v162h383v352h163v-352h383v-162h-383v-355h-163v355h-383z" />
<glyph unicode="&#xb4;" horiz-adv-x="997" d="M430 1225l88 260h195l-150 -260h-133z" />
<glyph unicode="&#xb6;" horiz-adv-x="1124" d="M59 1069q0 187 100 291t292 104h536v-1464h-143v1300h-156v-1300h-143v686h-94q-193 0 -292.5 98.5t-99.5 284.5z" />
<glyph unicode="&#xb7;" horiz-adv-x="405" d="M96 635q0 50 32.5 81.5t80.5 31.5t80.5 -31.5t32.5 -81.5q0 -51 -32 -82t-81 -31t-81 31t-32 82z" />
<glyph unicode="&#xb8;" horiz-adv-x="1024" d="M248 -342l43 51q84 -31 153 -31q50 0 81.5 21.5t31.5 54.5q0 29 -24 42.5t-78 13.5q-48 0 -82 -15l-25 39l117 152h102l-86 -107v-4q44 7 87.5 -2t77.5 -40t34 -81q0 -70 -64 -111.5t-149 -41.5q-62 0 -122 16.5t-97 42.5z" />
<glyph unicode="&#xbb;" horiz-adv-x="786" d="M61 190v164l195 160v4l-195 160v164l281 -234v-184zM444 190v164l195 160v4l-195 160v164l281 -234v-184z" />
<glyph unicode="&#xbf;" horiz-adv-x="1032" d="M61 78q0 49 11.5 92t25.5 71.5t44.5 61.5t51 51t61.5 51q4 4 7 6t7 5t7 6q90 74 122 111q91 105 94 275v13h184q0 -69 -14.5 -130t-35 -105.5t-56 -89.5t-64 -73.5t-72.5 -66.5q-18 -16 -55 -46t-54 -46.5t-39 -45.5t-31 -62.5t-9 -77.5q0 -92 69.5 -151.5t186.5 -59.5 q311 0 311 340h184q0 -228 -125.5 -366t-369.5 -138q-189 0 -315 106t-126 269zM471 1098q0 51 32 81.5t81 30.5t80.5 -30.5t31.5 -81.5q0 -50 -32 -81.5t-80 -31.5t-80.5 31.5t-32.5 81.5z" />
<glyph unicode="&#xc0;" horiz-adv-x="1325" d="M20 0l541 1464h205l539 -1464h-191l-129 367h-645l-129 -367h-191zM401 530h525l-260 764h-4zM451 1905h194l88 -260h-133z" />
<glyph unicode="&#xc1;" horiz-adv-x="1325" d="M20 0l541 1464h205l539 -1464h-191l-129 367h-645l-129 -367h-191zM401 530h525l-260 764h-4zM594 1645l88 260h195l-150 -260h-133z" />
<glyph unicode="&#xc2;" horiz-adv-x="1325" d="M20 0l541 1464h205l539 -1464h-191l-129 367h-645l-129 -367h-191zM383 1645l188 260h185l188 -260h-164l-114 172h-4l-115 -172h-164zM401 530h525l-260 764h-4z" />
<glyph unicode="&#xc3;" horiz-adv-x="1325" d="M20 0l541 1464h205l539 -1464h-191l-129 367h-645l-129 -367h-191zM377 1696q0 96 44 144t107 48q56 0 142 -36.5t108 -36.5q47 0 47 69h123q0 -98 -43.5 -145t-107.5 -47q-56 0 -142 36.5t-108 36.5q-47 0 -47 -69h-123zM401 530h525l-260 764h-4z" />
<glyph unicode="&#xc4;" horiz-adv-x="1325" d="M20 0l541 1464h205l539 -1464h-191l-129 367h-645l-129 -367h-191zM401 530h525l-260 764h-4zM408 1800q0 43 27 73t75 30t75 -30t27 -73q0 -42 -27.5 -71t-74.5 -29t-74.5 29t-27.5 71zM715 1800q0 43 27 73t75 30t75.5 -30t27.5 -73q0 -42 -27.5 -71t-75.5 -29 q-47 0 -74.5 29t-27.5 71z" />
<glyph unicode="&#xc5;" horiz-adv-x="1325" d="M20 0l541 1464h205l539 -1464h-191l-129 367h-645l-129 -367h-191zM401 530h525l-260 764h-4zM483 1792q0 77 53 128.5t126 51.5q75 0 127.5 -51.5t52.5 -128.5q0 -78 -52.5 -128t-127.5 -50q-74 0 -126.5 50t-52.5 128zM567 1792q0 -43 28.5 -69.5t66.5 -26.5 q39 0 67.5 26.5t28.5 69.5t-29 70.5t-67 27.5t-66.5 -27.5t-28.5 -70.5z" />
<glyph unicode="&#xc6;" horiz-adv-x="2021" d="M29 0l540 1464h1350v-164h-850v-460h752v-164h-752v-512h875v-164h-1059v367h-537l-129 -367h-190zM410 530h475v764h-215z" />
<glyph unicode="&#xc7;" horiz-adv-x="1452" d="M82 733q0 218 85 391t240.5 271t352.5 98q131 0 243 -40.5t190 -111t127 -163t62 -197.5h-184q-9 46 -26.5 90t-52 92t-81 83.5t-119 59t-159.5 23.5q-101 0 -184.5 -34t-140 -91t-95.5 -134t-56.5 -161.5t-17.5 -175.5t17.5 -176t56.5 -162.5t95.5 -134.5t140 -91 t184.5 -34q89 0 162 23.5t121.5 61.5t83 90t51 106t20.5 112h184q-24 -238 -178 -390.5t-401 -164.5l-76 -94v-4q44 7 87.5 -2t77.5 -40t34 -81q0 -70 -64 -111.5t-149 -41.5q-62 0 -122 16.5t-97 42.5l43 51q84 -31 153 -31q50 0 81.5 21.5t31.5 54.5q0 29 -24.5 42.5 t-78.5 13.5q-48 0 -82 -15l-24 39l106 139q-276 23 -447 234t-171 526z" />
<glyph unicode="&#xc8;" horiz-adv-x="1273" d="M137 0v1464h1034v-164h-849v-460h751v-164h-751v-512h874v-164h-1059zM455 1905h194l88 -260h-133z" />
<glyph unicode="&#xc9;" horiz-adv-x="1273" d="M137 0v1464h1034v-164h-849v-460h751v-164h-751v-512h874v-164h-1059zM598 1645l88 260h195l-150 -260h-133z" />
<glyph unicode="&#xca;" horiz-adv-x="1273" d="M137 0v1464h1034v-164h-849v-460h751v-164h-751v-512h874v-164h-1059zM387 1645l188 260h185l188 -260h-164l-114 172h-4l-115 -172h-164z" />
<glyph unicode="&#xcb;" horiz-adv-x="1273" d="M137 0v1464h1034v-164h-849v-460h751v-164h-751v-512h874v-164h-1059zM412 1800q0 43 27 73t75 30t75 -30t27 -73q0 -42 -27.5 -71t-74.5 -29t-74.5 29t-27.5 71zM719 1800q0 43 27 73t75 30t75.5 -30t27.5 -73q0 -42 -27.5 -71t-75.5 -29q-47 0 -74.5 29t-27.5 71z" />
<glyph unicode="&#xcc;" horiz-adv-x="466" d="M20 1905h195l88 -260h-133zM141 0v1464h185v-1464h-185z" />
<glyph unicode="&#xcd;" horiz-adv-x="466" d="M141 0v1464h185v-1464h-185zM164 1645l88 260h194l-149 -260h-133z" />
<glyph unicode="&#xce;" horiz-adv-x="466" d="M-47 1645l188 260h185l188 -260h-164l-114 172h-5l-114 -172h-164zM141 0v1464h185v-1464h-185z" />
<glyph unicode="&#xcf;" horiz-adv-x="466" d="M-23 1800q0 43 27.5 73t75.5 30t75 -30t27 -73q0 -42 -27.5 -71t-74.5 -29q-48 0 -75.5 29t-27.5 71zM141 0v1464h185v-1464h-185zM285 1800q0 43 27 73t75 30t75 -30t27 -73q0 -42 -27.5 -71t-74.5 -29t-74.5 29t-27.5 71z" />
<glyph unicode="&#xd0;" horiz-adv-x="1417" d="M10 686v143h144v635h530q305 0 478 -191.5t173 -541.5t-173 -540.5t-478 -190.5h-530v686h-144zM338 164h346q108 0 191.5 32t134.5 84.5t83.5 127.5t45 153t12.5 170t-12.5 170t-45 153.5t-83.5 128.5t-134.5 85t-191.5 32h-346v-471h467v-143h-467v-522z" />
<glyph unicode="&#xd1;" horiz-adv-x="1419" d="M137 0v1464h225l732 -1206h4v1206h184v-1464h-225l-731 1202h-4v-1202h-185zM424 1696q0 96 44 144t107 48q56 0 142 -36.5t108 -36.5q47 0 47 69h123q0 -98 -43.5 -145t-107.5 -47q-56 0 -142 36.5t-108 36.5q-47 0 -47 -69h-123z" />
<glyph unicode="&#xd2;" horiz-adv-x="1597" d="M82 733q0 219 89 391.5t253 270.5t375 98t375 -98t253 -270.5t89 -391.5t-89 -392t-253 -271.5t-375 -98.5t-375 98.5t-253 271.5t-89 392zM266 733q0 -96 19.5 -183t62 -163.5t104 -132t150.5 -87.5t197 -32q134 0 238 48.5t167 133t95 190t32 226.5t-32 226.5t-95 189 t-167 132t-238 48.5q-108 0 -197 -32t-150.5 -87t-104 -131t-62 -163t-19.5 -183zM586 1905h194l88 -260h-133z" />
<glyph unicode="&#xd3;" horiz-adv-x="1597" d="M82 733q0 219 89 391.5t253 270.5t375 98t375 -98t253 -270.5t89 -391.5t-89 -392t-253 -271.5t-375 -98.5t-375 98.5t-253 271.5t-89 392zM266 733q0 -96 19.5 -183t62 -163.5t104 -132t150.5 -87.5t197 -32q134 0 238 48.5t167 133t95 190t32 226.5t-32 226.5t-95 189 t-167 132t-238 48.5q-108 0 -197 -32t-150.5 -87t-104 -131t-62 -163t-19.5 -183zM729 1645l88 260h195l-150 -260h-133z" />
<glyph unicode="&#xd4;" horiz-adv-x="1597" d="M82 733q0 219 89 391.5t253 270.5t375 98t375 -98t253 -270.5t89 -391.5t-89 -392t-253 -271.5t-375 -98.5t-375 98.5t-253 271.5t-89 392zM266 733q0 -96 19.5 -183t62 -163.5t104 -132t150.5 -87.5t197 -32q134 0 238 48.5t167 133t95 190t32 226.5t-32 226.5t-95 189 t-167 132t-238 48.5q-108 0 -197 -32t-150.5 -87t-104 -131t-62 -163t-19.5 -183zM518 1645l189 260h184l188 -260h-164l-114 172h-4l-115 -172h-164z" />
<glyph unicode="&#xd5;" horiz-adv-x="1597" d="M82 733q0 219 89 391.5t253 270.5t375 98t375 -98t253 -270.5t89 -391.5t-89 -392t-253 -271.5t-375 -98.5t-375 98.5t-253 271.5t-89 392zM266 733q0 -96 19.5 -183t62 -163.5t104 -132t150.5 -87.5t197 -32q134 0 238 48.5t167 133t95 190t32 226.5t-32 226.5t-95 189 t-167 132t-238 48.5q-108 0 -197 -32t-150.5 -87t-104 -131t-62 -163t-19.5 -183zM512 1696q0 96 44.5 144t107.5 48q56 0 141.5 -36.5t107.5 -36.5q48 0 48 69h122q0 -98 -43.5 -145t-107.5 -47q-56 0 -142 36.5t-108 36.5q-47 0 -47 -69h-123z" />
<glyph unicode="&#xd6;" horiz-adv-x="1597" d="M82 733q0 219 89 391.5t253 270.5t375 98t375 -98t253 -270.5t89 -391.5t-89 -392t-253 -271.5t-375 -98.5t-375 98.5t-253 271.5t-89 392zM266 733q0 -96 19.5 -183t62 -163.5t104 -132t150.5 -87.5t197 -32q134 0 238 48.5t167 133t95 190t32 226.5t-32 226.5t-95 189 t-167 132t-238 48.5q-108 0 -197 -32t-150.5 -87t-104 -131t-62 -163t-19.5 -183zM543 1800q0 43 27 73t75 30t75.5 -30t27.5 -73q0 -42 -27.5 -71t-75.5 -29q-47 0 -74.5 29t-27.5 71zM850 1800q0 43 27 73t75 30t75.5 -30t27.5 -73q0 -42 -27.5 -71t-75.5 -29 q-47 0 -74.5 29t-27.5 71z" />
<glyph unicode="&#xd7;" horiz-adv-x="1146" d="M147 356l310 310l-310 309l117 117l309 -310l310 310l116 -117l-309 -309l309 -310l-116 -116l-310 309l-309 -309z" />
<glyph unicode="&#xd8;" horiz-adv-x="1599" d="M78 82l158 158q-154 206 -154 493q0 219 89 391.5t253 270.5t375 98q302 0 495 -190l154 153l74 -70l-160 -159q154 -206 154 -494q0 -219 -89 -392t-253 -271.5t-375 -98.5q-299 0 -498 193l-154 -154zM266 733q0 -213 94 -368l816 817q-144 147 -377 147 q-108 0 -197 -32t-150.5 -87t-104 -131t-62 -163t-19.5 -183zM422 285q140 -150 377 -150q134 0 238 48.5t167 133t95 190t32 226.5q0 214 -94 369z" />
<glyph unicode="&#xd9;" horiz-adv-x="1347" d="M113 487v977h184v-977q0 -178 93 -265t284 -87t284 87t93 265v977h184v-977q0 -114 -33 -207t-100 -163t-176 -108t-252 -38t-252 38t-176 108t-100 163t-33 207zM461 1905h194l88 -260h-133z" />
<glyph unicode="&#xda;" horiz-adv-x="1347" d="M113 487v977h184v-977q0 -178 93 -265t284 -87t284 87t93 265v977h184v-977q0 -114 -33 -207t-100 -163t-176 -108t-252 -38t-252 38t-176 108t-100 163t-33 207zM604 1645l88 260h195l-150 -260h-133z" />
<glyph unicode="&#xdb;" horiz-adv-x="1347" d="M113 487v977h184v-977q0 -178 93 -265t284 -87t284 87t93 265v977h184v-977q0 -114 -33 -207t-100 -163t-176 -108t-252 -38t-252 38t-176 108t-100 163t-33 207zM393 1645l189 260h184l188 -260h-163l-115 172h-4l-115 -172h-164z" />
<glyph unicode="&#xdc;" horiz-adv-x="1347" d="M113 487v977h184v-977q0 -178 93 -265t284 -87t284 87t93 265v977h184v-977q0 -114 -33 -207t-100 -163t-176 -108t-252 -38t-252 38t-176 108t-100 163t-33 207zM418 1800q0 43 27 73t75 30t75.5 -30t27.5 -73q0 -42 -27.5 -71t-75.5 -29q-47 0 -74.5 29t-27.5 71z M725 1800q0 43 27 73t75 30t75.5 -30t27.5 -73q0 -42 -27.5 -71t-75.5 -29q-47 0 -74.5 29t-27.5 71z" />
<glyph unicode="&#xdd;" horiz-adv-x="1292" d="M20 1464h199l424 -716h4l426 716h199l-535 -864v-600h-184v600zM578 1645l88 260h194l-149 -260h-133z" />
<glyph unicode="&#xde;" horiz-adv-x="1228" d="M137 0v1464h185v-245h397q104 0 188 -24t149.5 -73.5t101.5 -133t36 -195.5t-36 -195.5t-101.5 -133t-149.5 -73.5t-188 -24h-397v-367h-185zM322 530h417q137 0 204 65t67 198t-67 197.5t-204 64.5h-417v-525z" />
<glyph unicode="&#xdf;" horiz-adv-x="1044" d="M113 0v1073q0 198 111.5 309t320.5 111q101 0 187 -40.5t142 -124t56 -193.5q0 -122 -65 -197.5t-177 -124.5v-4q303 -95 303 -412q0 -181 -107.5 -303.5t-271.5 -122.5q-103 0 -213 29v143q134 -48 229 -23.5t147 99t52 178.5q0 81 -24 144t-60 99t-84 59.5t-91 31.5 t-87 8h-61v144h55q73 0 136.5 22.5t109 76.5t45.5 132q0 95 -57 165.5t-164 70.5q-80 0 -134.5 -23.5t-83 -69t-40 -99t-11.5 -126.5v-1032h-163z" />
<glyph unicode="&#xe0;" horiz-adv-x="1044" d="M49 268q0 70 21.5 123t56.5 88t96 62.5t122 43.5t151 33q146 27 201 57t55 103q0 152 -213 152q-147 0 -207 -47t-68 -162h-164q8 161 115.5 256.5t323.5 95.5q62 0 115.5 -10t102.5 -34t83 -61t54.5 -94.5t20.5 -130.5v-507q0 -68 23 -95.5t96 -19.5v-115 q-49 -16 -96 -16q-79 0 -122 29.5t-64 111.5h-4q-108 -160 -355 -160q-157 0 -250.5 82t-93.5 215zM213 276q0 -161 197 -161q155 0 248.5 72t93.5 216v175q-40 -40 -242 -82q-159 -34 -228 -82t-69 -138zM326 1485h194l88 -260h-133z" />
<glyph unicode="&#xe1;" horiz-adv-x="1044" d="M49 268q0 70 21.5 123t56.5 88t96 62.5t122 43.5t151 33q146 27 201 57t55 103q0 152 -213 152q-147 0 -207 -47t-68 -162h-164q8 161 115.5 256.5t323.5 95.5q62 0 115.5 -10t102.5 -34t83 -61t54.5 -94.5t20.5 -130.5v-507q0 -68 23 -95.5t96 -19.5v-115 q-49 -16 -96 -16q-79 0 -122 29.5t-64 111.5h-4q-108 -160 -355 -160q-157 0 -250.5 82t-93.5 215zM213 276q0 -161 197 -161q155 0 248.5 72t93.5 216v175q-40 -40 -242 -82q-159 -34 -228 -82t-69 -138zM469 1225l88 260h195l-150 -260h-133z" />
<glyph unicode="&#xe2;" horiz-adv-x="1044" d="M49 268q0 70 21.5 123t56.5 88t96 62.5t122 43.5t151 33q146 27 201 57t55 103q0 152 -213 152q-147 0 -207 -47t-68 -162h-164q8 161 115.5 256.5t323.5 95.5q62 0 115.5 -10t102.5 -34t83 -61t54.5 -94.5t20.5 -130.5v-507q0 -68 23 -95.5t96 -19.5v-115 q-49 -16 -96 -16q-79 0 -122 29.5t-64 111.5h-4q-108 -160 -355 -160q-157 0 -250.5 82t-93.5 215zM213 276q0 -161 197 -161q155 0 248.5 72t93.5 216v175q-40 -40 -242 -82q-159 -34 -228 -82t-69 -138zM258 1225l188 260h185l188 -260h-164l-114 172h-4l-115 -172h-164z " />
<glyph unicode="&#xe3;" horiz-adv-x="1044" d="M49 268q0 70 21.5 123t56.5 88t96 62.5t122 43.5t151 33q146 27 201 57t55 103q0 152 -213 152q-147 0 -207 -47t-68 -162h-164q8 161 115.5 256.5t323.5 95.5q62 0 115.5 -10t102.5 -34t83 -61t54.5 -94.5t20.5 -130.5v-507q0 -68 23 -95.5t96 -19.5v-115 q-49 -16 -96 -16q-79 0 -122 29.5t-64 111.5h-4q-108 -160 -355 -160q-157 0 -250.5 82t-93.5 215zM213 276q0 -161 197 -161q155 0 248.5 72t93.5 216v175q-40 -40 -242 -82q-159 -34 -228 -82t-69 -138zM252 1276q0 96 44 144t107 48q56 0 142 -36.5t108 -36.5q47 0 47 69 h123q0 -98 -43.5 -145t-107.5 -47q-56 0 -142 37t-108 37q-47 0 -47 -70h-123z" />
<glyph unicode="&#xe4;" horiz-adv-x="1044" d="M49 268q0 70 21.5 123t56.5 88t96 62.5t122 43.5t151 33q146 27 201 57t55 103q0 152 -213 152q-147 0 -207 -47t-68 -162h-164q8 161 115.5 256.5t323.5 95.5q62 0 115.5 -10t102.5 -34t83 -61t54.5 -94.5t20.5 -130.5v-507q0 -68 23 -95.5t96 -19.5v-115 q-49 -16 -96 -16q-79 0 -122 29.5t-64 111.5h-4q-108 -160 -355 -160q-157 0 -250.5 82t-93.5 215zM213 276q0 -161 197 -161q155 0 248.5 72t93.5 216v175q-40 -40 -242 -82q-159 -34 -228 -82t-69 -138zM283 1380q0 43 27 73t75 30t75 -30t27 -73q0 -42 -27.5 -71 t-74.5 -29t-74.5 29t-27.5 71zM590 1380q0 43 27 73t75 30t75.5 -30t27.5 -73q0 -42 -27.5 -71t-75.5 -29q-47 0 -74.5 29t-27.5 71z" />
<glyph unicode="&#xe5;" horiz-adv-x="1044" d="M49 268q0 70 21.5 123t56.5 88t96 62.5t122 43.5t151 33q146 27 201 57t55 103q0 152 -213 152q-147 0 -207 -47t-68 -162h-164q8 161 115.5 256.5t323.5 95.5q62 0 115.5 -10t102.5 -34t83 -61t54.5 -94.5t20.5 -130.5v-507q0 -68 23 -95.5t96 -19.5v-115 q-49 -16 -96 -16q-79 0 -122 29.5t-64 111.5h-4q-108 -160 -355 -160q-157 0 -250.5 82t-93.5 215zM213 276q0 -161 197 -161q155 0 248.5 72t93.5 216v175q-40 -40 -242 -82q-159 -34 -228 -82t-69 -138zM358 1372q0 77 53 128.5t126 51.5q75 0 127.5 -51.5t52.5 -128.5 q0 -78 -52.5 -128t-127.5 -50q-74 0 -126.5 50t-52.5 128zM442 1372q0 -43 28.5 -69.5t66.5 -26.5q39 0 67.5 26.5t28.5 69.5t-29 70.5t-67 27.5t-66.5 -27.5t-28.5 -70.5z" />
<glyph unicode="&#xe6;" horiz-adv-x="1789" d="M49 268q0 70 21.5 123t56.5 88t96 62.5t122 43.5t151 33q146 27 201 57t55 103q0 152 -213 152q-147 0 -207 -47t-68 -162h-164q8 161 115.5 256.5t323.5 95.5q256 0 338 -158q136 158 376 158q242 0 365 -155t123 -439h-826q0 -97 36.5 -178t115.5 -133.5t188 -52.5 q74 0 132.5 21.5t93 55.5t53.5 66.5t27 63.5h163q-43 -157 -160 -254t-302 -97q-137 0 -244 54t-174 153q-68 -104 -190 -155.5t-261 -51.5q-157 0 -250.5 82t-93.5 215zM213 276q0 -161 197 -161q167 0 254.5 83t87.5 205v175q-40 -40 -242 -82q-159 -34 -228 -82t-69 -138 zM915 623h662q0 129 -90 218t-234 89q-146 0 -242 -89.5t-96 -217.5z" />
<glyph unicode="&#xe7;" horiz-adv-x="1062" d="M53 522q0 247 139 399t367 152q190 0 315 -113.5t144 -291.5h-164q-3 46 -20.5 90t-51 84t-91 64t-132.5 24q-72 0 -130.5 -23.5t-97 -63t-65 -93t-38 -110.5t-11.5 -118q0 -77 19 -147t58 -129.5t107.5 -95t157.5 -35.5q76 0 133.5 24t90.5 64.5t50.5 86t20.5 95.5h164 q-11 -174 -132 -290.5t-304 -123.5l-76 -92v-4q44 7 87.5 -2t77.5 -40t34 -81q0 -70 -64 -111.5t-149 -41.5q-63 0 -123 16.5t-97 42.5l43 51q84 -31 154 -31q50 0 81.5 21.5t31.5 54.5q0 29 -24.5 42.5t-78.5 13.5q-48 0 -82 -15l-24 39l108 141q-195 26 -311.5 174.5 t-116.5 372.5z" />
<glyph unicode="&#xe8;" horiz-adv-x="1091" d="M53 522q0 259 135.5 405t366.5 146q242 0 364.5 -155t122.5 -439h-825q0 -97 36.5 -178t115.5 -133.5t188 -52.5q60 0 110 15t82.5 37t57 51t37 54.5t18.5 49.5h164q-43 -157 -160.5 -254t-302.5 -97q-235 0 -372.5 151t-137.5 400zM217 623h662q0 129 -90 218t-234 89 q-146 0 -242 -89.5t-96 -217.5zM336 1485h194l88 -260h-133z" />
<glyph unicode="&#xe9;" horiz-adv-x="1091" d="M53 522q0 259 135.5 405t366.5 146q242 0 364.5 -155t122.5 -439h-825q0 -97 36.5 -178t115.5 -133.5t188 -52.5q60 0 110 15t82.5 37t57 51t37 54.5t18.5 49.5h164q-43 -157 -160.5 -254t-302.5 -97q-235 0 -372.5 151t-137.5 400zM217 623h662q0 129 -90 218t-234 89 q-146 0 -242 -89.5t-96 -217.5zM479 1225l88 260h195l-150 -260h-133z" />
<glyph unicode="&#xea;" horiz-adv-x="1091" d="M53 522q0 259 135.5 405t366.5 146q242 0 364.5 -155t122.5 -439h-825q0 -97 36.5 -178t115.5 -133.5t188 -52.5q60 0 110 15t82.5 37t57 51t37 54.5t18.5 49.5h164q-43 -157 -160.5 -254t-302.5 -97q-235 0 -372.5 151t-137.5 400zM217 623h662q0 129 -90 218t-234 89 q-146 0 -242 -89.5t-96 -217.5zM268 1225l189 260h184l188 -260h-163l-115 172h-4l-115 -172h-164z" />
<glyph unicode="&#xeb;" horiz-adv-x="1091" d="M53 522q0 259 135.5 405t366.5 146q242 0 364.5 -155t122.5 -439h-825q0 -97 36.5 -178t115.5 -133.5t188 -52.5q60 0 110 15t82.5 37t57 51t37 54.5t18.5 49.5h164q-43 -157 -160.5 -254t-302.5 -97q-235 0 -372.5 151t-137.5 400zM217 623h662q0 129 -90 218t-234 89 q-146 0 -242 -89.5t-96 -217.5zM293 1380q0 43 27 73t75 30t75.5 -30t27.5 -73q0 -42 -27.5 -71t-75.5 -29q-47 0 -74.5 29t-27.5 71zM600 1380q0 43 27 73t75 30t75.5 -30t27.5 -73q0 -42 -27.5 -71t-75.5 -29q-47 0 -74.5 29t-27.5 71z" />
<glyph unicode="&#xec;" horiz-adv-x="286" d="M-70 1485h195l88 -260h-133zM61 0v1044h164v-1044h-164z" />
<glyph unicode="&#xed;" horiz-adv-x="286" d="M61 0v1044h164v-1044h-164zM74 1225l88 260h194l-149 -260h-133z" />
<glyph unicode="&#xee;" horiz-adv-x="286" d="M-137 1225l188 260h185l188 -260h-164l-115 172h-4l-114 -172h-164zM61 0v1044h164v-1044h-164z" />
<glyph unicode="&#xef;" horiz-adv-x="286" d="M-113 1380q0 43 27.5 73t75.5 30t75 -30t27 -73q0 -42 -27.5 -71t-74.5 -29q-48 0 -75.5 29t-27.5 71zM61 0v1044h164v-1044h-164zM195 1380q0 43 27 73t75 30t75 -30t27 -73q0 -42 -27.5 -71t-74.5 -29t-74.5 29t-27.5 71z" />
<glyph unicode="&#xf0;" horiz-adv-x="1128" d="M53 526q0 128 40 234t109 175.5t160 107.5t193 38q97 0 185 -52t141 -138v4q-56 167 -226 334l-325 -86l-27 98l260 70q-96 81 -215 153h223q83 -47 154 -110l268 71l29 -98l-211 -55q264 -287 264 -666q0 -96 -17 -185t-56.5 -172.5t-98.5 -145.5t-148.5 -99t-199.5 -37 q-238 0 -370 151.5t-132 407.5zM217 526q0 -112 36.5 -204t115 -151.5t186.5 -59.5t190.5 59t124 152.5t41.5 203.5q0 170 -97.5 291t-258.5 121q-108 0 -186.5 -58.5t-115 -150t-36.5 -203.5z" />
<glyph unicode="&#xf1;" d="M113 0v1044h163v-124h5q48 65 134.5 109t209.5 44q73 0 135 -20.5t111 -61t76.5 -108t27.5 -154.5v-729h-164v688q0 109 -55 175.5t-172 66.5q-137 0 -222.5 -78.5t-85.5 -200.5v-651h-163zM258 1276q0 96 44.5 144t107.5 48q56 0 141.5 -36.5t107.5 -36.5q48 0 48 69 h122q0 -98 -43.5 -145t-107.5 -47q-56 0 -142 37t-108 37q-47 0 -47 -70h-123z" />
<glyph unicode="&#xf2;" horiz-adv-x="1128" d="M53 522q0 -248 138 -399.5t374 -151.5q235 0 372.5 152t137.5 399t-137.5 399t-372.5 152q-236 0 -374 -151.5t-138 -399.5zM217 522q0 177 89.5 292.5t258.5 115.5q167 0 256.5 -115.5t89.5 -292.5t-89.5 -292t-256.5 -115q-169 0 -258.5 115t-89.5 292zM352 1485h195 l88 -260h-133z" />
<glyph unicode="&#xf3;" horiz-adv-x="1128" d="M53 522q0 -248 138 -399.5t374 -151.5q235 0 372.5 152t137.5 399t-137.5 399t-372.5 152q-236 0 -374 -151.5t-138 -399.5zM217 522q0 177 89.5 292.5t258.5 115.5q167 0 256.5 -115.5t89.5 -292.5t-89.5 -292t-256.5 -115q-169 0 -258.5 115t-89.5 292zM496 1225 l88 260h194l-149 -260h-133z" />
<glyph unicode="&#xf4;" horiz-adv-x="1128" d="M53 522q0 -248 138 -399.5t374 -151.5q235 0 372.5 152t137.5 399t-137.5 399t-372.5 152q-236 0 -374 -151.5t-138 -399.5zM217 522q0 177 89.5 292.5t258.5 115.5q167 0 256.5 -115.5t89.5 -292.5t-89.5 -292t-256.5 -115q-169 0 -258.5 115t-89.5 292zM285 1225 l188 260h184l189 -260h-164l-115 172h-4l-114 -172h-164z" />
<glyph unicode="&#xf5;" horiz-adv-x="1128" d="M53 522q0 -248 138 -399.5t374 -151.5q235 0 372.5 152t137.5 399t-137.5 399t-372.5 152q-236 0 -374 -151.5t-138 -399.5zM217 522q0 177 89.5 292.5t258.5 115.5q167 0 256.5 -115.5t89.5 -292.5t-89.5 -292t-256.5 -115q-169 0 -258.5 115t-89.5 292zM279 1276 q0 96 44 144t107 48q56 0 142 -36.5t108 -36.5q47 0 47 69h123q0 -98 -44 -145t-108 -47q-56 0 -141.5 37t-107.5 37q-48 0 -48 -70h-122z" />
<glyph unicode="&#xf6;" horiz-adv-x="1128" d="M53 522q0 -248 138 -399.5t374 -151.5q235 0 372.5 152t137.5 399t-137.5 399t-372.5 152q-236 0 -374 -151.5t-138 -399.5zM217 522q0 177 89.5 292.5t258.5 115.5q167 0 256.5 -115.5t89.5 -292.5t-89.5 -292t-256.5 -115q-169 0 -258.5 115t-89.5 292zM309 1380 q0 43 27.5 73t75.5 30t75 -30t27 -73q0 -42 -27.5 -71t-74.5 -29q-48 0 -75.5 29t-27.5 71zM616 1380q0 43 27.5 73t75.5 30t75 -30t27 -73q0 -42 -27.5 -71t-74.5 -29q-48 0 -75.5 29t-27.5 71z" />
<glyph unicode="&#xf7;" horiz-adv-x="1146" d="M109 588v164h929v-164h-929zM451 328q0 54 31 83t91 29q59 0 91 -29t32 -83t-32 -83.5t-91 -29.5q-60 0 -91 29.5t-31 83.5zM451 1008q0 54 31 83t91 29q59 0 91 -29t32 -83t-32 -83.5t-91 -29.5q-60 0 -91 29.5t-31 83.5z" />
<glyph unicode="&#xf8;" horiz-adv-x="1183" d="M53 51l109 109q-109 142 -109 362q0 248 138 399.5t374 151.5q228 0 365 -143l133 135l72 -72l-144 -143q84 -134 84 -328q0 -247 -137.5 -399t-372.5 -152q-205 0 -336 115l-106 -106zM217 522q0 -147 59 -248l543 545q-93 111 -254 111q-169 0 -258.5 -115.5 t-89.5 -292.5zM338 197q92 -82 227 -82q167 0 256.5 115t89.5 292q0 121 -39 209z" />
<glyph unicode="&#xf9;" d="M106 315v729h164v-688q0 -109 55.5 -175t172.5 -66q137 0 222 78t85 200v651h164v-1044h-164v125h-4q-48 -65 -135 -109.5t-209 -44.5q-73 0 -135.5 20.5t-111 61t-76.5 108t-28 154.5zM326 1485h194l88 -260h-133z" />
<glyph unicode="&#xfa;" d="M106 315v729h164v-688q0 -109 55.5 -175t172.5 -66q137 0 222 78t85 200v651h164v-1044h-164v125h-4q-48 -65 -135 -109.5t-209 -44.5q-73 0 -135.5 20.5t-111 61t-76.5 108t-28 154.5zM469 1225l88 260h195l-150 -260h-133z" />
<glyph unicode="&#xfb;" d="M106 315v729h164v-688q0 -109 55.5 -175t172.5 -66q137 0 222 78t85 200v651h164v-1044h-164v125h-4q-48 -65 -135 -109.5t-209 -44.5q-73 0 -135.5 20.5t-111 61t-76.5 108t-28 154.5zM258 1225l188 260h185l188 -260h-164l-114 172h-4l-115 -172h-164z" />
<glyph unicode="&#xfc;" d="M106 315v729h164v-688q0 -109 55.5 -175t172.5 -66q137 0 222 78t85 200v651h164v-1044h-164v125h-4q-48 -65 -135 -109.5t-209 -44.5q-73 0 -135.5 20.5t-111 61t-76.5 108t-28 154.5zM283 1380q0 43 27 73t75 30t75 -30t27 -73q0 -42 -27.5 -71t-74.5 -29t-74.5 29 t-27.5 71zM590 1380q0 43 27 73t75 30t75.5 -30t27.5 -73q0 -42 -27.5 -71t-75.5 -29q-47 0 -74.5 29t-27.5 71z" />
<glyph unicode="&#xfd;" horiz-adv-x="1001" d="M4 1044h184l320 -851h4l301 851h184l-446 -1130q-44 -110 -73 -152q-78 -113 -218 -114q-72 0 -139 20v123h24q74 -7 122 4.5t87 52.5q24 26 40 59.5t21.5 81.5t-16.5 97zM432 1225l88 260h195l-150 -260h-133z" />
<glyph unicode="&#xfe;" horiz-adv-x="1132" d="M113 -352v1816h163v-549h5q120 158 331 158q97 0 182 -38t148.5 -108t100 -174.5t36.5 -230.5t-36.5 -230.5t-100 -174.5t-148.5 -108t-182 -38q-220 0 -331 164h-5v-487h-163zM276 522q0 -185 85 -296t231 -111q103 0 178 60t110 150.5t35 196.5t-35 197t-110 151 t-178 60q-145 0 -230.5 -111t-85.5 -297z" />
<glyph unicode="&#xff;" horiz-adv-x="1001" d="M4 1044h184l320 -851h4l301 851h184l-446 -1130q-44 -110 -73 -152q-78 -113 -218 -114q-72 0 -139 20v123h24q74 -7 122 4.5t87 52.5q24 26 40 59.5t21.5 81.5t-16.5 97zM246 1380q0 43 27 73t75 30t75.5 -30t27.5 -73q0 -42 -27.5 -71t-75.5 -29q-47 0 -74.5 29 t-27.5 71zM553 1380q0 43 27 73t75 30t75.5 -30t27.5 -73q0 -42 -27.5 -71t-75.5 -29q-47 0 -74.5 29t-27.5 71z" />
<glyph unicode="&#x152;" horiz-adv-x="2195" d="M82 733q0 218 75.5 391.5t211 271t307.5 97.5q245 0 383 -162v133h1034v-164h-850v-460h752v-164h-752v-512h875v-164h-1059v135q-137 -164 -383 -164q-171 0 -307 98.5t-211.5 272t-75.5 391.5zM266 733q0 -97 15 -183t49.5 -163t86 -132.5t128 -87.5t172.5 -32 q107 0 196 53t146 158v772q-57 105 -146 158t-196 53q-119 0 -208 -48t-141 -132.5t-77 -188.5t-25 -227z" />
<glyph unicode="&#x153;" horiz-adv-x="1980" d="M53 522q0 248 138 399.5t374 151.5q152 0 265.5 -65.5t175.5 -184.5q62 122 174 186t264 64q242 0 364.5 -155t122.5 -439h-825q0 -97 36.5 -178t115.5 -133.5t188 -52.5q60 0 110 15t82.5 37t57 51t37 54.5t18.5 49.5h164q-43 -157 -160.5 -254t-302.5 -97 q-153 0 -267 67t-177 187q-63 -121 -176.5 -187.5t-266.5 -66.5q-236 0 -374 151.5t-138 399.5zM217 522q0 -177 89.5 -292t258.5 -115q167 0 256.5 115t89.5 292t-89.5 292.5t-256.5 115.5q-169 0 -258.5 -115.5t-89.5 -292.5zM1106 623h661q0 129 -89.5 218t-233.5 89 q-146 0 -242 -89.5t-96 -217.5z" />
<glyph unicode="&#x178;" horiz-adv-x="1292" d="M20 1464h199l424 -716h4l426 716h199l-535 -864v-600h-184v600zM391 1800q0 43 27.5 73t75.5 30t75 -30t27 -73q0 -42 -27.5 -71t-74.5 -29q-48 0 -75.5 29t-27.5 71zM698 1800q0 43 27.5 73t75.5 30t75 -30t27 -73q0 -42 -27.5 -71t-74.5 -29q-48 0 -75.5 29t-27.5 71z " />
<glyph unicode="&#x2c6;" horiz-adv-x="854" d="M231 1225l189 260h184l189 -260h-164l-115 172h-4l-115 -172h-164z" />
<glyph unicode="&#x2dc;" horiz-adv-x="1024" d="M225 1276q0 96 44.5 144t107.5 48q56 0 142 -36.5t108 -36.5q47 0 47 69h123q0 -98 -44 -145t-108 -47q-56 0 -142 37t-108 37q-47 0 -47 -70h-123z" />
<glyph unicode="&#x2000;" horiz-adv-x="986" />
<glyph unicode="&#x2001;" horiz-adv-x="1972" />
<glyph unicode="&#x2002;" horiz-adv-x="986" />
<glyph unicode="&#x2003;" horiz-adv-x="1972" />
<glyph unicode="&#x2004;" horiz-adv-x="657" />
<glyph unicode="&#x2005;" horiz-adv-x="493" />
<glyph unicode="&#x2006;" horiz-adv-x="328" />
<glyph unicode="&#x2007;" horiz-adv-x="328" />
<glyph unicode="&#x2008;" horiz-adv-x="246" />
<glyph unicode="&#x2009;" horiz-adv-x="394" />
<glyph unicode="&#x200a;" horiz-adv-x="109" />
<glyph unicode="&#x2010;" horiz-adv-x="704" d="M78 440v164h549v-164h-549z" />
<glyph unicode="&#x2011;" horiz-adv-x="704" d="M78 440v164h549v-164h-549z" />
<glyph unicode="&#x2012;" horiz-adv-x="704" d="M78 440v164h549v-164h-549z" />
<glyph unicode="&#x2013;" horiz-adv-x="1024" d="M0 440v164h1024v-164h-1024z" />
<glyph unicode="&#x2014;" horiz-adv-x="2048" d="M205 438v164h1638v-164h-1638z" />
<glyph unicode="&#x2018;" horiz-adv-x="393" d="M86 1157q0 45 10 91.5t32 93.5t64 81.5t99 44.5v-102q-47 -11 -75 -49.5t-28 -95.5q50 16 86.5 -12t36.5 -93q0 -56 -31 -87.5t-79 -31.5q-56 0 -85.5 43t-29.5 117z" />
<glyph unicode="&#x2019;" horiz-adv-x="397" d="M86 1360q0 56 31.5 87.5t79.5 31.5q56 0 85 -43t29 -117q0 -45 -10 -91.5t-32 -93.5t-64 -81.5t-99 -44.5v102q47 11 75 49.5t28 95.5q-50 -16 -86.5 12t-36.5 93z" />
<glyph unicode="&#x201a;" horiz-adv-x="397" d="M86 92q0 56 31.5 87.5t79.5 31.5q56 0 85 -43t29 -117q0 -45 -10 -91.5t-32 -93.5t-64 -81.5t-99 -44.5v102q47 11 75 50t28 96q-50 -16 -86.5 11.5t-36.5 92.5z" />
<glyph unicode="&#x201c;" horiz-adv-x="698" d="M86 1157q0 45 10 91.5t32 93.5t64 81.5t99 44.5v-102q-47 -11 -75 -49.5t-28 -95.5q50 16 86.5 -12t36.5 -93q0 -56 -31 -87.5t-79 -31.5q-56 0 -85.5 43t-29.5 117zM387 1157q0 45 10 91.5t32 93.5t64 81.5t99 44.5v-102q-47 -11 -75 -49.5t-28 -95.5q50 16 86.5 -12 t36.5 -93q0 -56 -31 -87.5t-79 -31.5q-56 0 -85.5 43t-29.5 117z" />
<glyph unicode="&#x201d;" horiz-adv-x="698" d="M86 1360q0 56 31.5 87.5t79.5 31.5q56 0 85 -43t29 -117q0 -45 -10 -91.5t-32 -93.5t-64 -81.5t-99 -44.5v102q47 11 75 49.5t28 95.5q-50 -16 -86.5 12t-36.5 93zM387 1360q0 56 31.5 87.5t79.5 31.5q56 0 85 -43t29 -117q0 -45 -10 -91t-31.5 -93.5t-63.5 -82 t-99 -44.5v102q47 11 74.5 49.5t27.5 95.5q-50 -16 -86.5 12t-36.5 93z" />
<glyph unicode="&#x201e;" horiz-adv-x="700" d="M86 92q0 56 31.5 87.5t79.5 31.5q56 0 85 -43t29 -117q0 -45 -10 -91.5t-32 -93.5t-64 -81.5t-99 -44.5v102q47 11 75 50t28 96q-50 -16 -86.5 11.5t-36.5 92.5zM389 92q0 56 31.5 87.5t79.5 31.5q56 0 85 -43t29 -117q0 -45 -10 -91t-31.5 -93.5t-63.5 -82t-99 -44.5 v102q47 10 74.5 49.5t27.5 96.5q-50 -16 -86.5 11.5t-36.5 92.5z" />
<glyph unicode="&#x2022;" horiz-adv-x="630" d="M70 655q0 104 67 175t178 71t178.5 -71t67.5 -175t-67.5 -174.5t-178.5 -70.5t-178 70.5t-67 174.5z" />
<glyph unicode="&#x2026;" horiz-adv-x="1060" d="M90 98q0 50 32.5 81.5t80.5 31.5t80 -31.5t32 -81.5q0 -51 -31.5 -81.5t-80.5 -30.5t-81 30.5t-32 81.5zM418 98q0 50 32 81.5t80 31.5t80.5 -31.5t32.5 -81.5q0 -51 -32 -81.5t-81 -30.5t-80.5 30.5t-31.5 81.5zM745 98q0 50 32.5 81.5t80.5 31.5t80.5 -31.5t32.5 -81.5 q0 -51 -32 -81.5t-81 -30.5t-81 30.5t-32 81.5z" />
<glyph unicode="&#x202f;" horiz-adv-x="394" />
<glyph unicode="&#x2039;" horiz-adv-x="503" d="M102 424v184l299 234v-164l-213 -160v-4l213 -160v-164z" />
<glyph unicode="&#x203a;" horiz-adv-x="503" d="M102 190v164l213 160v4l-213 160v164l299 -234v-184z" />
<glyph unicode="&#x205f;" horiz-adv-x="493" />
<glyph unicode="&#x20ac;" horiz-adv-x="1280" d="M61 518v123h119q-2 20 -2 61q0 38 2 56h-119v123h129q37 256 178 403.5t357 147.5q109 0 197.5 -37.5t148.5 -104.5t97.5 -155.5t50.5 -194.5h-185q-10 49 -21 88t-35 86.5t-55.5 79t-82.5 53t-115 21.5q-77 0 -138.5 -29.5t-103 -83t-68 -122t-40.5 -152.5h407v-123 h-417q-3 -27 -3 -56q0 -31 3 -61h417v-123h-405q28 -174 114 -278.5t234 -104.5q65 0 117 21.5t85.5 56.5t57 84.5t34.5 100.5t15 110h185q-26 -251 -151 -394t-343 -143q-216 0 -356 146t-179 401h-129z" />
<glyph unicode="&#x2122;" horiz-adv-x="1288" d="M66 1368v96h483v-96h-184v-469h-115v469h-184zM625 899v565h163l127 -426h3l129 426h165v-565h-106v428h-4l-131 -428h-109l-129 428h-4v-428h-104z" />
<glyph unicode="&#x25fc;" horiz-adv-x="1044" d="M0 0v1044h1044v-1044h-1044z" />
<hkern u1="&#x23;" u2="&#x34;" k="31" />
<hkern u1="&#x24;" u2="&#x37;" k="20" />
<hkern u1="&#x26;" u2="x" k="-20" />
<hkern u1="&#x26;" u2="v" k="61" />
<hkern u1="&#x26;" u2="V" k="92" />
<hkern u1="&#x26;" u2="&#x37;" k="41" />
<hkern u1="&#x26;" u2="&#x32;" k="-20" />
<hkern u1="&#x26;" u2="&#x31;" k="82" />
<hkern u1="&#x28;" u2="x" k="20" />
<hkern u1="&#x28;" u2="v" k="41" />
<hkern u1="&#x28;" u2="V" k="-20" />
<hkern u1="&#x28;" u2="&#x34;" k="51" />
<hkern u1="&#x28;" u2="&#x31;" k="51" />
<hkern u1="&#x28;" u2="&#x30;" k="20" />
<hkern u1="&#x2a;" u2="x" k="20" />
<hkern u1="&#x2a;" u2="v" k="-31" />
<hkern u1="&#x2a;" u2="X" k="31" />
<hkern u1="&#x2a;" u2="&#x39;" k="20" />
<hkern u1="&#x2a;" u2="&#x38;" k="41" />
<hkern u1="&#x2a;" u2="&#x36;" k="51" />
<hkern u1="&#x2a;" u2="&#x35;" k="51" />
<hkern u1="&#x2a;" u2="&#x34;" k="205" />
<hkern u1="&#x2a;" u2="&#x33;" k="20" />
<hkern u1="&#x2a;" u2="&#x32;" k="20" />
<hkern u1="&#x2a;" u2="&#x30;" k="41" />
<hkern u1="&#x2c;" u2="j" k="-61" />
<hkern u1="&#x2c;" u2="g" k="20" />
<hkern u1="&#x2f;" u2="&#xdf;" k="51" />
<hkern u1="&#x2f;" u2="x" k="102" />
<hkern u1="&#x2f;" u2="v" k="61" />
<hkern u1="&#x2f;" u2="p" k="92" />
<hkern u1="&#x2f;" u2="V" k="-20" />
<hkern u1="&#x2f;" u2="&#x39;" k="61" />
<hkern u1="&#x2f;" u2="&#x38;" k="51" />
<hkern u1="&#x2f;" u2="&#x36;" k="82" />
<hkern u1="&#x2f;" u2="&#x35;" k="61" />
<hkern u1="&#x2f;" u2="&#x34;" k="164" />
<hkern u1="&#x2f;" u2="&#x33;" k="61" />
<hkern u1="&#x2f;" u2="&#x32;" k="61" />
<hkern u1="&#x2f;" u2="&#x31;" k="41" />
<hkern u1="&#x2f;" u2="&#x30;" k="82" />
<hkern u1="&#x2f;" u2="&#x2f;" k="266" />
<hkern u1="&#x30;" u2="&#x2122;" k="61" />
<hkern u1="&#x30;" u2="&#xb0;" k="41" />
<hkern u1="&#x30;" u2="\" k="82" />
<hkern u1="&#x30;" u2="&#x39;" k="10" />
<hkern u1="&#x30;" u2="&#x38;" k="20" />
<hkern u1="&#x30;" u2="&#x37;" k="51" />
<hkern u1="&#x30;" u2="&#x32;" k="10" />
<hkern u1="&#x30;" u2="&#x31;" k="31" />
<hkern u1="&#x30;" u2="&#x2f;" k="61" />
<hkern u1="&#x30;" u2="&#x2a;" k="41" />
<hkern u1="&#x30;" u2="&#x29;" k="20" />
<hkern u1="&#x30;" u2="&#x26;" k="20" />
<hkern u1="&#x31;" u2="&#x40;" k="-20" />
<hkern u1="&#x32;" u2="&#x2122;" k="41" />
<hkern u1="&#x32;" u2="&#xa2;" k="20" />
<hkern u1="&#x32;" u2="\" k="51" />
<hkern u1="&#x32;" u2="&#x3c;" k="20" />
<hkern u1="&#x32;" u2="&#x34;" k="61" />
<hkern u1="&#x32;" u2="&#x2f;" k="20" />
<hkern u1="&#x32;" u2="&#x26;" k="20" />
<hkern u1="&#x32;" u2="&#x23;" k="20" />
<hkern u1="&#x33;" u2="&#x2122;" k="41" />
<hkern u1="&#x33;" u2="&#xb0;" k="20" />
<hkern u1="&#x33;" u2="\" k="61" />
<hkern u1="&#x33;" u2="&#x37;" k="31" />
<hkern u1="&#x33;" u2="&#x2a;" k="31" />
<hkern u1="&#x34;" u2="&#x2122;" k="82" />
<hkern u1="&#x34;" u2="&#xb0;" k="61" />
<hkern u1="&#x34;" u2="\" k="82" />
<hkern u1="&#x34;" u2="&#x3f;" k="20" />
<hkern u1="&#x34;" u2="&#x3c;" k="20" />
<hkern u1="&#x34;" u2="&#x37;" k="20" />
<hkern u1="&#x34;" u2="&#x31;" k="61" />
<hkern u1="&#x34;" u2="&#x2a;" k="102" />
<hkern u1="&#x35;" u2="&#x2122;" k="20" />
<hkern u1="&#x35;" u2="&#xb0;" k="51" />
<hkern u1="&#x35;" u2="\" k="41" />
<hkern u1="&#x35;" u2="&#x39;" k="20" />
<hkern u1="&#x35;" u2="&#x37;" k="20" />
<hkern u1="&#x35;" u2="&#x31;" k="61" />
<hkern u1="&#x35;" u2="&#x2f;" k="20" />
<hkern u1="&#x35;" u2="&#x2a;" k="41" />
<hkern u1="&#x36;" u2="&#x2122;" k="41" />
<hkern u1="&#x36;" u2="&#xb0;" k="20" />
<hkern u1="&#x36;" u2="\" k="61" />
<hkern u1="&#x36;" u2="&#x3e;" k="31" />
<hkern u1="&#x36;" u2="&#x39;" k="20" />
<hkern u1="&#x36;" u2="&#x37;" k="31" />
<hkern u1="&#x36;" u2="&#x32;" k="20" />
<hkern u1="&#x36;" u2="&#x2f;" k="31" />
<hkern u1="&#x36;" u2="&#x2a;" k="31" />
<hkern u1="&#x37;" u2="&#x2122;" k="-41" />
<hkern u1="&#x37;" u2="&#xb7;" k="61" />
<hkern u1="&#x37;" u2="&#xb0;" k="-41" />
<hkern u1="&#x37;" u2="&#xae;" k="41" />
<hkern u1="&#x37;" u2="&#xa7;" k="31" />
<hkern u1="&#x37;" u2="&#xa2;" k="123" />
<hkern u1="&#x37;" u2="\" k="-20" />
<hkern u1="&#x37;" u2="&#x40;" k="41" />
<hkern u1="&#x37;" u2="&#x3f;" k="-20" />
<hkern u1="&#x37;" u2="&#x3d;" k="41" />
<hkern u1="&#x37;" u2="&#x3c;" k="82" />
<hkern u1="&#x37;" u2="&#x39;" k="10" />
<hkern u1="&#x37;" u2="&#x38;" k="20" />
<hkern u1="&#x37;" u2="&#x37;" k="-31" />
<hkern u1="&#x37;" u2="&#x36;" k="31" />
<hkern u1="&#x37;" u2="&#x35;" k="31" />
<hkern u1="&#x37;" u2="&#x34;" k="143" />
<hkern u1="&#x37;" u2="&#x30;" k="31" />
<hkern u1="&#x37;" u2="&#x2f;" k="164" />
<hkern u1="&#x37;" u2="&#x2a;" k="-20" />
<hkern u1="&#x37;" u2="&#x26;" k="72" />
<hkern u1="&#x37;" u2="&#x23;" k="61" />
<hkern u1="&#x38;" u2="&#x2122;" k="61" />
<hkern u1="&#x38;" u2="&#xb7;" k="-20" />
<hkern u1="&#x38;" u2="&#xb0;" k="41" />
<hkern u1="&#x38;" u2="&#xae;" k="20" />
<hkern u1="&#x38;" u2="\" k="51" />
<hkern u1="&#x38;" u2="&#x39;" k="20" />
<hkern u1="&#x38;" u2="&#x37;" k="41" />
<hkern u1="&#x38;" u2="&#x36;" k="20" />
<hkern u1="&#x38;" u2="&#x31;" k="31" />
<hkern u1="&#x38;" u2="&#x30;" k="20" />
<hkern u1="&#x38;" u2="&#x2a;" k="41" />
<hkern u1="&#x39;" u2="&#x2122;" k="41" />
<hkern u1="&#x39;" u2="&#xb0;" k="31" />
<hkern u1="&#x39;" u2="\" k="72" />
<hkern u1="&#x39;" u2="&#x37;" k="31" />
<hkern u1="&#x39;" u2="&#x32;" k="20" />
<hkern u1="&#x39;" u2="&#x2f;" k="61" />
<hkern u1="&#x39;" u2="&#x2a;" k="20" />
<hkern u1="&#x3a;" u2="j" k="-20" />
<hkern u1="&#x3d;" u2="&#x37;" k="61" />
<hkern u1="&#x3d;" u2="&#x31;" k="20" />
<hkern u1="&#x3e;" u2="&#x39;" k="20" />
<hkern u1="&#x3e;" u2="&#x37;" k="61" />
<hkern u1="&#x3e;" u2="&#x32;" k="41" />
<hkern u1="&#x3e;" u2="&#x31;" k="61" />
<hkern u1="&#x40;" u2="x" k="31" />
<hkern u1="&#x40;" u2="v" k="-20" />
<hkern u1="&#x40;" u2="X" k="82" />
<hkern u1="&#x40;" u2="V" k="61" />
<hkern u1="&#x40;" u2="&#x38;" k="20" />
<hkern u1="&#x40;" u2="&#x37;" k="41" />
<hkern u1="&#x40;" u2="&#x32;" k="20" />
<hkern u1="&#x40;" u2="&#x31;" k="20" />
<hkern u1="B" u2="&#x2122;" k="20" />
<hkern u1="B" u2="x" k="20" />
<hkern u1="B" u2="\" k="51" />
<hkern u1="B" u2="X" k="31" />
<hkern u1="B" u2="V" k="31" />
<hkern u1="B" u2="&#x40;" k="-20" />
<hkern u1="B" u2="&#x29;" k="20" />
<hkern u1="F" u2="&#xdf;" k="31" />
<hkern u1="F" u2="&#xb7;" k="41" />
<hkern u1="F" u2="x" k="92" />
<hkern u1="F" u2="v" k="51" />
<hkern u1="F" u2="p" k="72" />
<hkern u1="F" u2="&#x40;" k="51" />
<hkern u1="F" u2="&#x2f;" k="143" />
<hkern u1="F" u2="&#x2a;" k="-20" />
<hkern u1="F" u2="&#x29;" k="-20" />
<hkern u1="F" u2="&#x26;" k="82" />
<hkern u1="P" u2="&#x2122;" k="-20" />
<hkern u1="P" u2="&#xae;" k="-20" />
<hkern u1="P" u2="v" k="-31" />
<hkern u1="P" u2="X" k="51" />
<hkern u1="P" u2="&#x3f;" k="-51" />
<hkern u1="P" u2="&#x2f;" k="143" />
<hkern u1="P" u2="&#x2a;" k="-20" />
<hkern u1="P" u2="&#x26;" k="61" />
<hkern u1="Q" u2="&#x2122;" k="61" />
<hkern u1="Q" u2="x" k="31" />
<hkern u1="Q" u2="\" k="82" />
<hkern u1="Q" u2="X" k="61" />
<hkern u1="Q" u2="V" k="72" />
<hkern u1="Q" u2="&#x2f;" k="31" />
<hkern u1="Q" u2="&#x2a;" k="41" />
<hkern u1="V" u2="&#x2122;" k="-31" />
<hkern u1="V" u2="&#xdf;" k="31" />
<hkern u1="V" u2="&#xb7;" k="82" />
<hkern u1="V" u2="&#xae;" k="51" />
<hkern u1="V" u2="x" k="61" />
<hkern u1="V" u2="v" k="41" />
<hkern u1="V" u2="p" k="51" />
<hkern u1="V" u2="\" k="-20" />
<hkern u1="V" u2="V" k="-31" />
<hkern u1="V" u2="&#x40;" k="82" />
<hkern u1="V" u2="&#x2f;" k="164" />
<hkern u1="V" u2="&#x29;" k="-20" />
<hkern u1="V" u2="&#x26;" k="82" />
<hkern u1="X" u2="&#xdf;" k="20" />
<hkern u1="X" u2="&#xb7;" k="82" />
<hkern u1="X" u2="&#xae;" k="82" />
<hkern u1="X" u2="v" k="72" />
<hkern u1="X" u2="&#x40;" k="41" />
<hkern u1="X" u2="&#x3f;" k="20" />
<hkern u1="X" u2="&#x2a;" k="31" />
<hkern u1="X" u2="&#x26;" k="41" />
<hkern u1="\" u2="v" k="61" />
<hkern u1="\" u2="V" k="164" />
<hkern u1="\" u2="\" k="266" />
<hkern u1="\" u2="&#x39;" k="41" />
<hkern u1="\" u2="&#x37;" k="102" />
<hkern u1="\" u2="&#x36;" k="51" />
<hkern u1="\" u2="&#x34;" k="20" />
<hkern u1="\" u2="&#x31;" k="123" />
<hkern u1="\" u2="&#x30;" k="61" />
<hkern u1="q" u2="&#x2122;" k="61" />
<hkern u1="q" u2="\" k="123" />
<hkern u1="v" u2="&#xb7;" k="20" />
<hkern u1="v" u2="v" k="-31" />
<hkern u1="v" u2="\" k="61" />
<hkern u1="v" u2="&#x3f;" k="-72" />
<hkern u1="v" u2="&#x2f;" k="61" />
<hkern u1="v" u2="&#x2a;" k="-31" />
<hkern u1="v" u2="&#x29;" k="41" />
<hkern u1="v" u2="&#x26;" k="61" />
<hkern u1="x" u2="&#x2122;" k="41" />
<hkern u1="x" u2="&#xb7;" k="61" />
<hkern u1="x" u2="&#xae;" k="31" />
<hkern u1="x" u2="\" k="102" />
<hkern u1="x" u2="&#x40;" k="41" />
<hkern u1="x" u2="&#x2a;" k="20" />
<hkern u1="x" u2="&#x29;" k="20" />
<hkern u1="x" u2="&#x26;" k="61" />
<hkern u1="&#xa1;" u2="V" k="41" />
<hkern u1="&#xa1;" u2="&#x37;" k="20" />
<hkern u1="&#xa3;" u2="&#x37;" k="20" />
<hkern u1="&#xa3;" u2="&#x34;" k="61" />
<hkern u1="&#xa5;" u2="&#x39;" k="31" />
<hkern u1="&#xa5;" u2="&#x38;" k="20" />
<hkern u1="&#xa5;" u2="&#x36;" k="41" />
<hkern u1="&#xa5;" u2="&#x32;" k="20" />
<hkern u1="&#xa5;" u2="&#x31;" k="41" />
<hkern u1="&#xa5;" u2="&#x30;" k="20" />
<hkern u1="&#xa7;" u2="&#x37;" k="41" />
<hkern u1="&#xa7;" u2="&#x33;" k="-20" />
<hkern u1="&#xa7;" u2="&#x31;" k="41" />
<hkern u1="&#xb0;" u2="&#x39;" k="20" />
<hkern u1="&#xb0;" u2="&#x38;" k="41" />
<hkern u1="&#xb0;" u2="&#x36;" k="31" />
<hkern u1="&#xb0;" u2="&#x35;" k="41" />
<hkern u1="&#xb0;" u2="&#x34;" k="195" />
<hkern u1="&#xb0;" u2="&#x32;" k="31" />
<hkern u1="&#xb0;" u2="&#x30;" k="41" />
<hkern u1="&#xb7;" u2="x" k="61" />
<hkern u1="&#xb7;" u2="v" k="20" />
<hkern u1="&#xb7;" u2="X" k="82" />
<hkern u1="&#xb7;" u2="V" k="82" />
<hkern u1="&#xb7;" u2="&#x38;" k="-20" />
<hkern u1="&#xb7;" u2="&#x37;" k="41" />
<hkern u1="&#xb7;" u2="&#x31;" k="82" />
<hkern u1="&#xbf;" u2="v" k="102" />
<hkern u1="&#xbf;" u2="V" k="184" />
<hkern u1="&#xbf;" u2="&#x37;" k="61" />
<hkern u1="&#xbf;" u2="&#x36;" k="31" />
<hkern u1="&#xbf;" u2="&#x32;" k="-31" />
<hkern u1="&#xbf;" u2="&#x31;" k="102" />
<hkern u1="&#xbf;" u2="&#x30;" k="41" />
<hkern u1="&#xdf;" u2="&#x2122;" k="41" />
<hkern u1="&#xdf;" u2="x" k="20" />
<hkern u1="&#xdf;" u2="v" k="10" />
<hkern u1="&#xdf;" u2="\" k="72" />
<hkern u1="&#xdf;" u2="&#x2f;" k="20" />
<hkern g1="bracketleft,braceleft" 	g2="four" 	k="20" />
<hkern g1="bracketleft,braceleft" 	g2="one" 	k="31" />
<hkern g1="colon,semicolon" 	g2="one" 	k="61" />
<hkern g1="colon,semicolon" 	g2="seven" 	k="20" />
<hkern g1="comma,period,ellipsis" 	g2="four" 	k="61" />
<hkern g1="comma,period,ellipsis" 	g2="one" 	k="205" />
<hkern g1="comma,period,ellipsis" 	g2="seven" 	k="61" />
<hkern g1="comma,period,ellipsis" 	g2="nine" 	k="31" />
<hkern g1="comma,period,ellipsis" 	g2="six" 	k="61" />
<hkern g1="comma,period,ellipsis" 	g2="zero" 	k="51" />
<hkern g1="guilsinglleft" 	g2="one" 	k="82" />
<hkern g1="guilsinglleft" 	g2="seven" 	k="51" />
<hkern g1="guilsinglleft" 	g2="two" 	k="20" />
<hkern g1="guilsinglright" 	g2="one" 	k="143" />
<hkern g1="guilsinglright" 	g2="seven" 	k="72" />
<hkern g1="guilsinglright" 	g2="nine" 	k="20" />
<hkern g1="guilsinglright" 	g2="two" 	k="41" />
<hkern g1="guilsinglright" 	g2="eight" 	k="20" />
<hkern g1="hyphen,endash,emdash" 	g2="one" 	k="184" />
<hkern g1="hyphen,endash,emdash" 	g2="seven" 	k="72" />
<hkern g1="hyphen,endash,emdash" 	g2="nine" 	k="20" />
<hkern g1="hyphen,endash,emdash" 	g2="two" 	k="20" />
<hkern g1="hyphen,endash,emdash" 	g2="eight" 	k="-20" />
<hkern g1="quotedbl,quotesingle" 	g2="four" 	k="133" />
<hkern g1="quotedbl,quotesingle" 	g2="one" 	k="-20" />
<hkern g1="quotedbl,quotesingle" 	g2="seven" 	k="-20" />
<hkern g1="quotedbl,quotesingle" 	g2="eight" 	k="20" />
<hkern g1="quotedbl,quotesingle" 	g2="three" 	k="-20" />
<hkern g1="quoteleft,quotedblleft" 	g2="four" 	k="164" />
<hkern g1="quoteleft,quotedblleft" 	g2="one" 	k="-31" />
<hkern g1="quoteleft,quotedblleft" 	g2="seven" 	k="-20" />
<hkern g1="quoteleft,quotedblleft" 	g2="six" 	k="20" />
<hkern g1="quoteleft,quotedblleft" 	g2="zero" 	k="41" />
<hkern g1="quoteleft,quotedblleft" 	g2="eight" 	k="31" />
<hkern g1="quoteleft,quotedblleft" 	g2="five" 	k="20" />
<hkern g1="quoteright,quotedblright" 	g2="four" 	k="205" />
<hkern g1="quoteright,quotedblright" 	g2="seven" 	k="-31" />
<hkern g1="quoteright,quotedblright" 	g2="nine" 	k="20" />
<hkern g1="quoteright,quotedblright" 	g2="six" 	k="51" />
<hkern g1="quoteright,quotedblright" 	g2="zero" 	k="51" />
<hkern g1="quoteright,quotedblright" 	g2="two" 	k="20" />
<hkern g1="quoteright,quotedblright" 	g2="eight" 	k="41" />
<hkern g1="quoteright,quotedblright" 	g2="three" 	k="20" />
<hkern g1="quoteright,quotedblright" 	g2="five" 	k="31" />
<hkern g1="quotesinglbase,quotedblbase" 	g2="four" 	k="61" />
<hkern g1="quotesinglbase,quotedblbase" 	g2="one" 	k="205" />
<hkern g1="quotesinglbase,quotedblbase" 	g2="seven" 	k="61" />
<hkern g1="quotesinglbase,quotedblbase" 	g2="nine" 	k="31" />
<hkern g1="quotesinglbase,quotedblbase" 	g2="six" 	k="61" />
<hkern g1="quotesinglbase,quotedblbase" 	g2="zero" 	k="51" />
<hkern g1="copyright" 	g2="eight" 	k="20" />
<hkern g1="copyright" 	g2="nine" 	k="20" />
<hkern g1="copyright" 	g2="one" 	k="20" />
<hkern g1="copyright" 	g2="seven" 	k="61" />
<hkern g1="copyright" 	g2="two" 	k="20" />
<hkern g1="plus,divide" 	g2="nine" 	k="20" />
<hkern g1="plus,divide" 	g2="one" 	k="61" />
<hkern g1="plus,divide" 	g2="seven" 	k="41" />
<hkern g1="plus,divide" 	g2="two" 	k="20" />
<hkern g1="eight" 	g2="copyright" 	k="20" />
<hkern g1="eight" 	g2="guilsinglleft" 	k="20" />
<hkern g1="eight" 	g2="hyphen,endash,emdash" 	k="-20" />
<hkern g1="eight" 	g2="quotedbl,quotesingle" 	k="31" />
<hkern g1="eight" 	g2="quoteleft,quotedblleft" 	k="41" />
<hkern g1="eight" 	g2="quoteright,quotedblright" 	k="31" />
<hkern g1="five" 	g2="quotedbl,quotesingle" 	k="20" />
<hkern g1="five" 	g2="quoteleft,quotedblleft" 	k="41" />
<hkern g1="five" 	g2="quoteright,quotedblright" 	k="20" />
<hkern g1="five" 	g2="percent" 	k="20" />
<hkern g1="four" 	g2="quotedbl,quotesingle" 	k="41" />
<hkern g1="four" 	g2="quoteleft,quotedblleft" 	k="61" />
<hkern g1="four" 	g2="quoteright,quotedblright" 	k="41" />
<hkern g1="four" 	g2="percent" 	k="61" />
<hkern g1="four" 	g2="plus,divide" 	k="20" />
<hkern g1="nine" 	g2="quotedbl,quotesingle" 	k="20" />
<hkern g1="nine" 	g2="quoteleft,quotedblleft" 	k="31" />
<hkern g1="nine" 	g2="quoteright,quotedblright" 	k="20" />
<hkern g1="nine" 	g2="comma,period,ellipsis" 	k="41" />
<hkern g1="nine" 	g2="quotesinglbase,quotedblbase" 	k="41" />
<hkern g1="seven" 	g2="copyright" 	k="41" />
<hkern g1="seven" 	g2="guilsinglleft" 	k="154" />
<hkern g1="seven" 	g2="hyphen,endash,emdash" 	k="133" />
<hkern g1="seven" 	g2="quotedbl,quotesingle" 	k="-51" />
<hkern g1="seven" 	g2="quoteleft,quotedblleft" 	k="-41" />
<hkern g1="seven" 	g2="quoteright,quotedblright" 	k="-51" />
<hkern g1="seven" 	g2="percent" 	k="-41" />
<hkern g1="seven" 	g2="plus,divide" 	k="31" />
<hkern g1="seven" 	g2="comma,period,ellipsis" 	k="123" />
<hkern g1="seven" 	g2="quotesinglbase,quotedblbase" 	k="123" />
<hkern g1="seven" 	g2="colon,semicolon" 	k="72" />
<hkern g1="seven" 	g2="guilsinglright" 	k="61" />
<hkern g1="six" 	g2="comma,period,ellipsis" 	k="20" />
<hkern g1="six" 	g2="quotesinglbase,quotedblbase" 	k="20" />
<hkern g1="three" 	g2="quoteleft,quotedblleft" 	k="20" />
<hkern g1="two" 	g2="guilsinglleft" 	k="61" />
<hkern g1="two" 	g2="hyphen,endash,emdash" 	k="41" />
<hkern g1="two" 	g2="percent" 	k="-61" />
<hkern g1="zero" 	g2="quotedbl,quotesingle" 	k="20" />
<hkern g1="zero" 	g2="quoteleft,quotedblleft" 	k="41" />
<hkern g1="zero" 	g2="quoteright,quotedblright" 	k="20" />
<hkern g1="zero" 	g2="comma,period,ellipsis" 	k="51" />
<hkern g1="zero" 	g2="quotesinglbase,quotedblbase" 	k="51" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="72" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="S" 	k="20" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="T" 	k="184" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	k="31" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="V" 	k="143" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="W" 	k="113" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="Y,Yacute,Ydieresis" 	k="154" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="ampersand" 	k="20" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="asterisk" 	k="184" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="at" 	k="20" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="backslash" 	k="164" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="41" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="colon,semicolon" 	k="-20" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="comma,period,ellipsis" 	k="-31" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="copyright" 	k="61" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="d,q" 	k="31" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="f" 	k="31" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="g" 	k="31" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="guilsinglleft" 	k="51" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="guilsinglright" 	k="20" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="hyphen,endash,emdash" 	k="41" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="periodcentered" 	k="61" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="question" 	k="92" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="quotedbl,quotesingle" 	k="164" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="quoteleft,quotedblleft" 	k="164" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="quoteright,quotedblright" 	k="164" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="quotesinglbase,quotedblbase" 	k="-31" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="registered" 	k="61" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="s" 	k="10" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="slash" 	k="-20" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="t" 	k="51" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="trademark" 	k="143" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="u,ugrave,uacute,ucircumflex,udieresis" 	k="31" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="v" 	k="82" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="w" 	k="82" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="y,yacute,ydieresis" 	k="82" />
<hkern g1="C,Ccedilla" 	g2="T" 	k="51" />
<hkern g1="C,Ccedilla" 	g2="V" 	k="31" />
<hkern g1="C,Ccedilla" 	g2="W" 	k="20" />
<hkern g1="C,Ccedilla" 	g2="Y,Yacute,Ydieresis" 	k="72" />
<hkern g1="C,Ccedilla" 	g2="backslash" 	k="31" />
<hkern g1="C,Ccedilla" 	g2="comma,period,ellipsis" 	k="51" />
<hkern g1="C,Ccedilla" 	g2="hyphen,endash,emdash" 	k="-20" />
<hkern g1="C,Ccedilla" 	g2="question" 	k="-20" />
<hkern g1="C,Ccedilla" 	g2="quotedbl,quotesingle" 	k="-20" />
<hkern g1="C,Ccedilla" 	g2="quoteleft,quotedblleft" 	k="-20" />
<hkern g1="C,Ccedilla" 	g2="quoteright,quotedblright" 	k="-20" />
<hkern g1="C,Ccedilla" 	g2="quotesinglbase,quotedblbase" 	k="51" />
<hkern g1="C,Ccedilla" 	g2="slash" 	k="41" />
<hkern g1="C,Ccedilla" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="31" />
<hkern g1="C,Ccedilla" 	g2="AE" 	k="31" />
<hkern g1="C,Ccedilla" 	g2="X" 	k="82" />
<hkern g1="C,Ccedilla" 	g2="Z" 	k="20" />
<hkern g1="C,Ccedilla" 	g2="x" 	k="31" />
<hkern g1="C,Ccedilla" 	g2="z" 	k="31" />
<hkern g1="D,Eth" 	g2="T" 	k="92" />
<hkern g1="D,Eth" 	g2="V" 	k="72" />
<hkern g1="D,Eth" 	g2="W" 	k="61" />
<hkern g1="D,Eth" 	g2="Y,Yacute,Ydieresis" 	k="123" />
<hkern g1="D,Eth" 	g2="asterisk" 	k="41" />
<hkern g1="D,Eth" 	g2="backslash" 	k="82" />
<hkern g1="D,Eth" 	g2="comma,period,ellipsis" 	k="102" />
<hkern g1="D,Eth" 	g2="quotedbl,quotesingle" 	k="41" />
<hkern g1="D,Eth" 	g2="quoteleft,quotedblleft" 	k="31" />
<hkern g1="D,Eth" 	g2="quoteright,quotedblright" 	k="41" />
<hkern g1="D,Eth" 	g2="quotesinglbase,quotedblbase" 	k="102" />
<hkern g1="D,Eth" 	g2="slash" 	k="82" />
<hkern g1="D,Eth" 	g2="trademark" 	k="61" />
<hkern g1="D,Eth" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="72" />
<hkern g1="D,Eth" 	g2="AE" 	k="92" />
<hkern g1="D,Eth" 	g2="X" 	k="123" />
<hkern g1="D,Eth" 	g2="Z" 	k="61" />
<hkern g1="D,Eth" 	g2="x" 	k="31" />
<hkern g1="D,Eth" 	g2="z" 	k="31" />
<hkern g1="D,Eth" 	g2="J" 	k="20" />
<hkern g1="D,Eth" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="20" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE" 	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="31" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE" 	g2="S" 	k="20" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE" 	g2="ampersand" 	k="20" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE" 	g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="41" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE" 	g2="comma,period,ellipsis" 	k="-41" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE" 	g2="d,q" 	k="31" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE" 	g2="f" 	k="31" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE" 	g2="g" 	k="31" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE" 	g2="guilsinglleft" 	k="41" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE" 	g2="quotesinglbase,quotedblbase" 	k="-41" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE" 	g2="s" 	k="20" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE" 	g2="t" 	k="31" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE" 	g2="u,ugrave,uacute,ucircumflex,udieresis" 	k="20" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE" 	g2="v" 	k="41" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE" 	g2="w" 	k="41" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE" 	g2="y,yacute,ydieresis" 	k="41" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="-20" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE" 	g2="z" 	k="10" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE" 	g2="germandbls" 	k="20" />
<hkern g1="F" 	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="41" />
<hkern g1="F" 	g2="S" 	k="31" />
<hkern g1="F" 	g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="92" />
<hkern g1="F" 	g2="colon,semicolon" 	k="51" />
<hkern g1="F" 	g2="comma,period,ellipsis" 	k="164" />
<hkern g1="F" 	g2="d,q" 	k="82" />
<hkern g1="F" 	g2="f" 	k="20" />
<hkern g1="F" 	g2="g" 	k="82" />
<hkern g1="F" 	g2="guilsinglleft" 	k="92" />
<hkern g1="F" 	g2="guilsinglright" 	k="31" />
<hkern g1="F" 	g2="hyphen,endash,emdash" 	k="51" />
<hkern g1="F" 	g2="quotedbl,quotesingle" 	k="-20" />
<hkern g1="F" 	g2="quoteleft,quotedblleft" 	k="-20" />
<hkern g1="F" 	g2="quoteright,quotedblright" 	k="-20" />
<hkern g1="F" 	g2="quotesinglbase,quotedblbase" 	k="164" />
<hkern g1="F" 	g2="s" 	k="82" />
<hkern g1="F" 	g2="t" 	k="31" />
<hkern g1="F" 	g2="u,ugrave,uacute,ucircumflex,udieresis" 	k="72" />
<hkern g1="F" 	g2="w" 	k="51" />
<hkern g1="F" 	g2="y,yacute,ydieresis" 	k="51" />
<hkern g1="F" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="133" />
<hkern g1="F" 	g2="AE" 	k="164" />
<hkern g1="F" 	g2="z" 	k="72" />
<hkern g1="F" 	g2="J" 	k="143" />
<hkern g1="F" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="92" />
<hkern g1="F" 	g2="bracketright,braceright" 	k="-31" />
<hkern g1="F" 	g2="m,n,r,ntilde" 	k="72" />
<hkern g1="G" 	g2="T" 	k="82" />
<hkern g1="G" 	g2="V" 	k="51" />
<hkern g1="G" 	g2="W" 	k="41" />
<hkern g1="G" 	g2="Y,Yacute,Ydieresis" 	k="82" />
<hkern g1="G" 	g2="asterisk" 	k="31" />
<hkern g1="G" 	g2="backslash" 	k="82" />
<hkern g1="G" 	g2="quotedbl,quotesingle" 	k="20" />
<hkern g1="G" 	g2="quoteleft,quotedblleft" 	k="20" />
<hkern g1="G" 	g2="quoteright,quotedblright" 	k="20" />
<hkern g1="G" 	g2="trademark" 	k="41" />
<hkern g1="G" 	g2="X" 	k="20" />
<hkern g1="J" 	g2="comma,period,ellipsis" 	k="31" />
<hkern g1="J" 	g2="question" 	k="-20" />
<hkern g1="J" 	g2="quotesinglbase,quotedblbase" 	k="31" />
<hkern g1="J" 	g2="slash" 	k="31" />
<hkern g1="J" 	g2="AE" 	k="20" />
<hkern g1="J" 	g2="z" 	k="20" />
<hkern g1="K" 	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="113" />
<hkern g1="K" 	g2="S" 	k="61" />
<hkern g1="K" 	g2="ampersand" 	k="41" />
<hkern g1="K" 	g2="asterisk" 	k="41" />
<hkern g1="K" 	g2="at" 	k="41" />
<hkern g1="K" 	g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="82" />
<hkern g1="K" 	g2="comma,period,ellipsis" 	k="-31" />
<hkern g1="K" 	g2="copyright" 	k="61" />
<hkern g1="K" 	g2="d,q" 	k="72" />
<hkern g1="K" 	g2="f" 	k="41" />
<hkern g1="K" 	g2="g" 	k="72" />
<hkern g1="K" 	g2="guilsinglleft" 	k="143" />
<hkern g1="K" 	g2="guilsinglright" 	k="41" />
<hkern g1="K" 	g2="hyphen,endash,emdash" 	k="143" />
<hkern g1="K" 	g2="periodcentered" 	k="143" />
<hkern g1="K" 	g2="question" 	k="51" />
<hkern g1="K" 	g2="quoteleft,quotedblleft" 	k="31" />
<hkern g1="K" 	g2="quotesinglbase,quotedblbase" 	k="-31" />
<hkern g1="K" 	g2="registered" 	k="61" />
<hkern g1="K" 	g2="s" 	k="41" />
<hkern g1="K" 	g2="slash" 	k="-20" />
<hkern g1="K" 	g2="t" 	k="61" />
<hkern g1="K" 	g2="u,ugrave,uacute,ucircumflex,udieresis" 	k="41" />
<hkern g1="K" 	g2="v" 	k="82" />
<hkern g1="K" 	g2="w" 	k="82" />
<hkern g1="K" 	g2="y,yacute,ydieresis" 	k="82" />
<hkern g1="K" 	g2="J" 	k="31" />
<hkern g1="K" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="20" />
<hkern g1="K" 	g2="bracketright,braceright" 	k="-20" />
<hkern g1="L" 	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="92" />
<hkern g1="L" 	g2="S" 	k="31" />
<hkern g1="L" 	g2="T" 	k="205" />
<hkern g1="L" 	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	k="41" />
<hkern g1="L" 	g2="V" 	k="174" />
<hkern g1="L" 	g2="W" 	k="143" />
<hkern g1="L" 	g2="Y,Yacute,Ydieresis" 	k="215" />
<hkern g1="L" 	g2="asterisk" 	k="225" />
<hkern g1="L" 	g2="at" 	k="31" />
<hkern g1="L" 	g2="backslash" 	k="205" />
<hkern g1="L" 	g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="41" />
<hkern g1="L" 	g2="colon,semicolon" 	k="-20" />
<hkern g1="L" 	g2="comma,period,ellipsis" 	k="-41" />
<hkern g1="L" 	g2="copyright" 	k="41" />
<hkern g1="L" 	g2="d,q" 	k="31" />
<hkern g1="L" 	g2="f" 	k="51" />
<hkern g1="L" 	g2="g" 	k="41" />
<hkern g1="L" 	g2="guilsinglleft" 	k="61" />
<hkern g1="L" 	g2="hyphen,endash,emdash" 	k="61" />
<hkern g1="L" 	g2="periodcentered" 	k="61" />
<hkern g1="L" 	g2="question" 	k="102" />
<hkern g1="L" 	g2="quotedbl,quotesingle" 	k="154" />
<hkern g1="L" 	g2="quoteleft,quotedblleft" 	k="164" />
<hkern g1="L" 	g2="quoteright,quotedblright" 	k="154" />
<hkern g1="L" 	g2="quotesinglbase,quotedblbase" 	k="-41" />
<hkern g1="L" 	g2="registered" 	k="41" />
<hkern g1="L" 	g2="s" 	k="20" />
<hkern g1="L" 	g2="slash" 	k="-20" />
<hkern g1="L" 	g2="t" 	k="51" />
<hkern g1="L" 	g2="trademark" 	k="164" />
<hkern g1="L" 	g2="u,ugrave,uacute,ucircumflex,udieresis" 	k="31" />
<hkern g1="L" 	g2="v" 	k="113" />
<hkern g1="L" 	g2="w" 	k="113" />
<hkern g1="L" 	g2="y,yacute,ydieresis" 	k="102" />
<hkern g1="L" 	g2="AE" 	k="-20" />
<hkern g1="L" 	g2="Z" 	k="-31" />
<hkern g1="O,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="T" 	k="92" />
<hkern g1="O,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="V" 	k="72" />
<hkern g1="O,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="W" 	k="61" />
<hkern g1="O,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="Y,Yacute,Ydieresis" 	k="123" />
<hkern g1="O,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="asterisk" 	k="41" />
<hkern g1="O,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="backslash" 	k="82" />
<hkern g1="O,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="comma,period,ellipsis" 	k="102" />
<hkern g1="O,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="quotedbl,quotesingle" 	k="41" />
<hkern g1="O,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="quoteleft,quotedblleft" 	k="31" />
<hkern g1="O,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="quoteright,quotedblright" 	k="41" />
<hkern g1="O,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="quotesinglbase,quotedblbase" 	k="102" />
<hkern g1="O,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="slash" 	k="82" />
<hkern g1="O,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="trademark" 	k="61" />
<hkern g1="O,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="72" />
<hkern g1="O,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="AE" 	k="92" />
<hkern g1="O,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="X" 	k="123" />
<hkern g1="O,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="Z" 	k="61" />
<hkern g1="O,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="x" 	k="31" />
<hkern g1="O,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="z" 	k="31" />
<hkern g1="O,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="J" 	k="20" />
<hkern g1="O,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="20" />
<hkern g1="P" 	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="-20" />
<hkern g1="P" 	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	k="-25" />
<hkern g1="P" 	g2="W" 	k="-20" />
<hkern g1="P" 	g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="31" />
<hkern g1="P" 	g2="comma,period,ellipsis" 	k="205" />
<hkern g1="P" 	g2="copyright" 	k="-20" />
<hkern g1="P" 	g2="d,q" 	k="20" />
<hkern g1="P" 	g2="f" 	k="-31" />
<hkern g1="P" 	g2="g" 	k="20" />
<hkern g1="P" 	g2="guilsinglleft" 	k="61" />
<hkern g1="P" 	g2="hyphen,endash,emdash" 	k="61" />
<hkern g1="P" 	g2="quotedbl,quotesingle" 	k="-41" />
<hkern g1="P" 	g2="quoteleft,quotedblleft" 	k="-41" />
<hkern g1="P" 	g2="quoteright,quotedblright" 	k="-41" />
<hkern g1="P" 	g2="quotesinglbase,quotedblbase" 	k="205" />
<hkern g1="P" 	g2="s" 	k="20" />
<hkern g1="P" 	g2="t" 	k="-20" />
<hkern g1="P" 	g2="w" 	k="-31" />
<hkern g1="P" 	g2="y,yacute,ydieresis" 	k="-31" />
<hkern g1="P" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="113" />
<hkern g1="P" 	g2="AE" 	k="133" />
<hkern g1="P" 	g2="Z" 	k="31" />
<hkern g1="P" 	g2="J" 	k="123" />
<hkern g1="P" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="41" />
<hkern g1="Q" 	g2="T" 	k="92" />
<hkern g1="Q" 	g2="W" 	k="61" />
<hkern g1="Q" 	g2="Y,Yacute,Ydieresis" 	k="123" />
<hkern g1="Q" 	g2="comma,period,ellipsis" 	k="102" />
<hkern g1="Q" 	g2="quotedbl,quotesingle" 	k="41" />
<hkern g1="Q" 	g2="quoteleft,quotedblleft" 	k="31" />
<hkern g1="Q" 	g2="quoteright,quotedblright" 	k="41" />
<hkern g1="Q" 	g2="quotesinglbase,quotedblbase" 	k="102" />
<hkern g1="Q" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="41" />
<hkern g1="Q" 	g2="AE" 	k="41" />
<hkern g1="Q" 	g2="Z" 	k="31" />
<hkern g1="Q" 	g2="z" 	k="31" />
<hkern g1="Q" 	g2="J" 	k="20" />
<hkern g1="Q" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="20" />
<hkern g1="R" 	g2="T" 	k="31" />
<hkern g1="R" 	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	k="-20" />
<hkern g1="R" 	g2="Y,Yacute,Ydieresis" 	k="31" />
<hkern g1="R" 	g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="10" />
<hkern g1="R" 	g2="comma,period,ellipsis" 	k="-20" />
<hkern g1="R" 	g2="d,q" 	k="10" />
<hkern g1="R" 	g2="g" 	k="10" />
<hkern g1="R" 	g2="guilsinglleft" 	k="31" />
<hkern g1="R" 	g2="hyphen,endash,emdash" 	k="20" />
<hkern g1="R" 	g2="question" 	k="-31" />
<hkern g1="R" 	g2="quotesinglbase,quotedblbase" 	k="-20" />
<hkern g1="R" 	g2="Z" 	k="-20" />
<hkern g1="S" 	g2="T" 	k="72" />
<hkern g1="S" 	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	k="-20" />
<hkern g1="S" 	g2="V" 	k="41" />
<hkern g1="S" 	g2="W" 	k="31" />
<hkern g1="S" 	g2="Y,Yacute,Ydieresis" 	k="82" />
<hkern g1="S" 	g2="asterisk" 	k="20" />
<hkern g1="S" 	g2="backslash" 	k="51" />
<hkern g1="S" 	g2="comma,period,ellipsis" 	k="20" />
<hkern g1="S" 	g2="hyphen,endash,emdash" 	k="-20" />
<hkern g1="S" 	g2="quotedbl,quotesingle" 	k="31" />
<hkern g1="S" 	g2="quoteleft,quotedblleft" 	k="31" />
<hkern g1="S" 	g2="quoteright,quotedblright" 	k="31" />
<hkern g1="S" 	g2="quotesinglbase,quotedblbase" 	k="20" />
<hkern g1="S" 	g2="trademark" 	k="41" />
<hkern g1="S" 	g2="v" 	k="20" />
<hkern g1="S" 	g2="w" 	k="20" />
<hkern g1="S" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="10" />
<hkern g1="S" 	g2="X" 	k="41" />
<hkern g1="S" 	g2="Z" 	k="20" />
<hkern g1="S" 	g2="x" 	k="41" />
<hkern g1="S" 	g2="z" 	k="31" />
<hkern g1="T" 	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="92" />
<hkern g1="T" 	g2="S" 	k="31" />
<hkern g1="T" 	g2="T" 	k="-31" />
<hkern g1="T" 	g2="V" 	k="-20" />
<hkern g1="T" 	g2="W" 	k="-20" />
<hkern g1="T" 	g2="Y,Yacute,Ydieresis" 	k="-20" />
<hkern g1="T" 	g2="ampersand" 	k="102" />
<hkern g1="T" 	g2="at" 	k="123" />
<hkern g1="T" 	g2="backslash" 	k="-20" />
<hkern g1="T" 	g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="225" />
<hkern g1="T" 	g2="colon,semicolon" 	k="164" />
<hkern g1="T" 	g2="comma,period,ellipsis" 	k="184" />
<hkern g1="T" 	g2="copyright" 	k="82" />
<hkern g1="T" 	g2="d,q" 	k="184" />
<hkern g1="T" 	g2="f" 	k="61" />
<hkern g1="T" 	g2="g" 	k="184" />
<hkern g1="T" 	g2="guilsinglleft" 	k="225" />
<hkern g1="T" 	g2="guilsinglright" 	k="123" />
<hkern g1="T" 	g2="hyphen,endash,emdash" 	k="164" />
<hkern g1="T" 	g2="periodcentered" 	k="123" />
<hkern g1="T" 	g2="quotedbl,quotesingle" 	k="-20" />
<hkern g1="T" 	g2="quoteleft,quotedblleft" 	k="-20" />
<hkern g1="T" 	g2="quoteright,quotedblright" 	k="-20" />
<hkern g1="T" 	g2="quotesinglbase,quotedblbase" 	k="184" />
<hkern g1="T" 	g2="registered" 	k="82" />
<hkern g1="T" 	g2="s" 	k="215" />
<hkern g1="T" 	g2="slash" 	k="184" />
<hkern g1="T" 	g2="t" 	k="61" />
<hkern g1="T" 	g2="u,ugrave,uacute,ucircumflex,udieresis" 	k="164" />
<hkern g1="T" 	g2="v" 	k="143" />
<hkern g1="T" 	g2="w" 	k="143" />
<hkern g1="T" 	g2="y,yacute,ydieresis" 	k="143" />
<hkern g1="T" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="184" />
<hkern g1="T" 	g2="AE" 	k="184" />
<hkern g1="T" 	g2="x" 	k="143" />
<hkern g1="T" 	g2="z" 	k="164" />
<hkern g1="T" 	g2="J" 	k="184" />
<hkern g1="T" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="236" />
<hkern g1="T" 	g2="germandbls" 	k="41" />
<hkern g1="T" 	g2="bracketright,braceright" 	k="-31" />
<hkern g1="T" 	g2="m,n,r,ntilde" 	k="154" />
<hkern g1="T" 	g2="p" 	k="154" />
<hkern g1="T" 	g2="parenright" 	k="-20" />
<hkern g1="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	g2="S" 	k="-20" />
<hkern g1="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	g2="V" 	k="-20" />
<hkern g1="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	g2="comma,period,ellipsis" 	k="51" />
<hkern g1="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	g2="question" 	k="-20" />
<hkern g1="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	g2="quotesinglbase,quotedblbase" 	k="51" />
<hkern g1="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	g2="slash" 	k="41" />
<hkern g1="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="31" />
<hkern g1="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	g2="AE" 	k="31" />
<hkern g1="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	g2="Z" 	k="20" />
<hkern g1="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	g2="z" 	k="20" />
<hkern g1="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	g2="bracketright,braceright" 	k="-20" />
<hkern g1="V" 	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="72" />
<hkern g1="V" 	g2="S" 	k="20" />
<hkern g1="V" 	g2="T" 	k="-20" />
<hkern g1="V" 	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	k="-20" />
<hkern g1="V" 	g2="W" 	k="-31" />
<hkern g1="V" 	g2="Y,Yacute,Ydieresis" 	k="-20" />
<hkern g1="V" 	g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="133" />
<hkern g1="V" 	g2="colon,semicolon" 	k="82" />
<hkern g1="V" 	g2="comma,period,ellipsis" 	k="184" />
<hkern g1="V" 	g2="copyright" 	k="51" />
<hkern g1="V" 	g2="d,q" 	k="102" />
<hkern g1="V" 	g2="f" 	k="31" />
<hkern g1="V" 	g2="g" 	k="102" />
<hkern g1="V" 	g2="guilsinglleft" 	k="143" />
<hkern g1="V" 	g2="guilsinglright" 	k="82" />
<hkern g1="V" 	g2="hyphen,endash,emdash" 	k="133" />
<hkern g1="V" 	g2="quotedbl,quotesingle" 	k="-20" />
<hkern g1="V" 	g2="quoteright,quotedblright" 	k="-20" />
<hkern g1="V" 	g2="quotesinglbase,quotedblbase" 	k="184" />
<hkern g1="V" 	g2="s" 	k="82" />
<hkern g1="V" 	g2="t" 	k="31" />
<hkern g1="V" 	g2="u,ugrave,uacute,ucircumflex,udieresis" 	k="61" />
<hkern g1="V" 	g2="w" 	k="41" />
<hkern g1="V" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="143" />
<hkern g1="V" 	g2="AE" 	k="164" />
<hkern g1="V" 	g2="Z" 	k="20" />
<hkern g1="V" 	g2="z" 	k="82" />
<hkern g1="V" 	g2="J" 	k="143" />
<hkern g1="V" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="123" />
<hkern g1="V" 	g2="bracketright,braceright" 	k="-20" />
<hkern g1="V" 	g2="m,n,r,ntilde" 	k="51" />
<hkern g1="W" 	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="61" />
<hkern g1="W" 	g2="S" 	k="20" />
<hkern g1="W" 	g2="T" 	k="-20" />
<hkern g1="W" 	g2="V" 	k="-31" />
<hkern g1="W" 	g2="W" 	k="-20" />
<hkern g1="W" 	g2="Y,Yacute,Ydieresis" 	k="-20" />
<hkern g1="W" 	g2="ampersand" 	k="92" />
<hkern g1="W" 	g2="at" 	k="72" />
<hkern g1="W" 	g2="backslash" 	k="-20" />
<hkern g1="W" 	g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="102" />
<hkern g1="W" 	g2="colon,semicolon" 	k="61" />
<hkern g1="W" 	g2="comma,period,ellipsis" 	k="143" />
<hkern g1="W" 	g2="copyright" 	k="51" />
<hkern g1="W" 	g2="d,q" 	k="102" />
<hkern g1="W" 	g2="f" 	k="20" />
<hkern g1="W" 	g2="g" 	k="102" />
<hkern g1="W" 	g2="guilsinglleft" 	k="123" />
<hkern g1="W" 	g2="guilsinglright" 	k="61" />
<hkern g1="W" 	g2="hyphen,endash,emdash" 	k="82" />
<hkern g1="W" 	g2="periodcentered" 	k="61" />
<hkern g1="W" 	g2="question" 	k="-20" />
<hkern g1="W" 	g2="quotedbl,quotesingle" 	k="-20" />
<hkern g1="W" 	g2="quoteright,quotedblright" 	k="-20" />
<hkern g1="W" 	g2="quotesinglbase,quotedblbase" 	k="143" />
<hkern g1="W" 	g2="registered" 	k="51" />
<hkern g1="W" 	g2="s" 	k="92" />
<hkern g1="W" 	g2="slash" 	k="143" />
<hkern g1="W" 	g2="t" 	k="20" />
<hkern g1="W" 	g2="u,ugrave,uacute,ucircumflex,udieresis" 	k="61" />
<hkern g1="W" 	g2="v" 	k="20" />
<hkern g1="W" 	g2="w" 	k="20" />
<hkern g1="W" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="113" />
<hkern g1="W" 	g2="AE" 	k="143" />
<hkern g1="W" 	g2="x" 	k="61" />
<hkern g1="W" 	g2="z" 	k="72" />
<hkern g1="W" 	g2="J" 	k="123" />
<hkern g1="W" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="113" />
<hkern g1="W" 	g2="germandbls" 	k="20" />
<hkern g1="W" 	g2="bracketright,braceright" 	k="-20" />
<hkern g1="W" 	g2="m,n,r,ntilde" 	k="51" />
<hkern g1="W" 	g2="p" 	k="51" />
<hkern g1="W" 	g2="parenright" 	k="-20" />
<hkern g1="X" 	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="123" />
<hkern g1="X" 	g2="S" 	k="41" />
<hkern g1="X" 	g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="72" />
<hkern g1="X" 	g2="copyright" 	k="82" />
<hkern g1="X" 	g2="d,q" 	k="61" />
<hkern g1="X" 	g2="f" 	k="20" />
<hkern g1="X" 	g2="g" 	k="61" />
<hkern g1="X" 	g2="guilsinglleft" 	k="113" />
<hkern g1="X" 	g2="guilsinglright" 	k="51" />
<hkern g1="X" 	g2="hyphen,endash,emdash" 	k="102" />
<hkern g1="X" 	g2="quotedbl,quotesingle" 	k="10" />
<hkern g1="X" 	g2="quoteleft,quotedblleft" 	k="31" />
<hkern g1="X" 	g2="quoteright,quotedblright" 	k="10" />
<hkern g1="X" 	g2="s" 	k="41" />
<hkern g1="X" 	g2="t" 	k="41" />
<hkern g1="X" 	g2="u,ugrave,uacute,ucircumflex,udieresis" 	k="41" />
<hkern g1="X" 	g2="w" 	k="72" />
<hkern g1="X" 	g2="J" 	k="20" />
<hkern g1="X" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="20" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="123" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="S" 	k="61" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="T" 	k="-20" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="V" 	k="-20" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="W" 	k="-20" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="Y,Yacute,Ydieresis" 	k="-20" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="ampersand" 	k="123" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="asterisk" 	k="20" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="at" 	k="143" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="backslash" 	k="-20" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="236" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="colon,semicolon" 	k="143" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="comma,period,ellipsis" 	k="205" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="copyright" 	k="113" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="d,q" 	k="184" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="f" 	k="72" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="g" 	k="184" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="guilsinglleft" 	k="225" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="guilsinglright" 	k="143" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="hyphen,endash,emdash" 	k="205" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="periodcentered" 	k="143" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="question" 	k="20" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="quotedbl,quotesingle" 	k="-20" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="quoteleft,quotedblleft" 	k="10" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="quoteright,quotedblright" 	k="-20" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="quotesinglbase,quotedblbase" 	k="205" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="registered" 	k="113" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="s" 	k="184" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="slash" 	k="205" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="t" 	k="72" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="trademark" 	k="-20" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="u,ugrave,uacute,ucircumflex,udieresis" 	k="123" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="v" 	k="92" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="w" 	k="92" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="y,yacute,ydieresis" 	k="92" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="154" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="AE" 	k="184" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="Z" 	k="20" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="x" 	k="123" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="z" 	k="143" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="J" 	k="205" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="195" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="germandbls" 	k="72" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="bracketright,braceright" 	k="-20" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="m,n,r,ntilde" 	k="102" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="p" 	k="102" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="parenright" 	k="-20" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="b,h,i,k,l,igrave,iacute,icircumflex,idieresis" 	k="20" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="j" 	k="20" />
<hkern g1="Z" 	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="92" />
<hkern g1="Z" 	g2="S" 	k="31" />
<hkern g1="Z" 	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	k="20" />
<hkern g1="Z" 	g2="V" 	k="20" />
<hkern g1="Z" 	g2="Y,Yacute,Ydieresis" 	k="20" />
<hkern g1="Z" 	g2="ampersand" 	k="51" />
<hkern g1="Z" 	g2="asterisk" 	k="20" />
<hkern g1="Z" 	g2="at" 	k="41" />
<hkern g1="Z" 	g2="backslash" 	k="20" />
<hkern g1="Z" 	g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="61" />
<hkern g1="Z" 	g2="colon,semicolon" 	k="-20" />
<hkern g1="Z" 	g2="comma,period,ellipsis" 	k="-20" />
<hkern g1="Z" 	g2="copyright" 	k="72" />
<hkern g1="Z" 	g2="d,q" 	k="51" />
<hkern g1="Z" 	g2="f" 	k="41" />
<hkern g1="Z" 	g2="g" 	k="51" />
<hkern g1="Z" 	g2="guilsinglleft" 	k="82" />
<hkern g1="Z" 	g2="hyphen,endash,emdash" 	k="82" />
<hkern g1="Z" 	g2="periodcentered" 	k="61" />
<hkern g1="Z" 	g2="question" 	k="20" />
<hkern g1="Z" 	g2="quotesinglbase,quotedblbase" 	k="-20" />
<hkern g1="Z" 	g2="registered" 	k="72" />
<hkern g1="Z" 	g2="s" 	k="20" />
<hkern g1="Z" 	g2="t" 	k="51" />
<hkern g1="Z" 	g2="u,ugrave,uacute,ucircumflex,udieresis" 	k="41" />
<hkern g1="Z" 	g2="v" 	k="61" />
<hkern g1="Z" 	g2="w" 	k="61" />
<hkern g1="Z" 	g2="y,yacute,ydieresis" 	k="72" />
<hkern g1="Z" 	g2="z" 	k="20" />
<hkern g1="Z" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="20" />
<hkern g1="Z" 	g2="germandbls" 	k="20" />
<hkern g1="numbersign,dollar,percent,parenright,less,equal,greater,question,H,I,M,N,bracketright,asciicircum,underscore,grave,bar,braceright,asciitilde,cent,sterling,currency,yen,brokenbar,section,dieresis,logicalnot,registered,macron,degree,plusminus,acute,paragraph,cedilla,Igrave,Iacute,Icircumflex,Idieresis,Ntilde,multiply,Thorn,eth,circumflex,tilde,bullet,trademark" 	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="-10" />
<hkern g1="numbersign,dollar,percent,parenright,less,equal,greater,question,H,I,M,N,bracketright,asciicircum,underscore,grave,bar,braceright,asciitilde,cent,sterling,currency,yen,brokenbar,section,dieresis,logicalnot,registered,macron,degree,plusminus,acute,paragraph,cedilla,Igrave,Iacute,Icircumflex,Idieresis,Ntilde,multiply,Thorn,eth,circumflex,tilde,bullet,trademark" 	g2="T" 	k="51" />
<hkern g1="numbersign,dollar,percent,parenright,less,equal,greater,question,H,I,M,N,bracketright,asciicircum,underscore,grave,bar,braceright,asciitilde,cent,sterling,currency,yen,brokenbar,section,dieresis,logicalnot,registered,macron,degree,plusminus,acute,paragraph,cedilla,Igrave,Iacute,Icircumflex,Idieresis,Ntilde,multiply,Thorn,eth,circumflex,tilde,bullet,trademark" 	g2="W" 	k="20" />
<hkern g1="numbersign,dollar,percent,parenright,less,equal,greater,question,H,I,M,N,bracketright,asciicircum,underscore,grave,bar,braceright,asciitilde,cent,sterling,currency,yen,brokenbar,section,dieresis,logicalnot,registered,macron,degree,plusminus,acute,paragraph,cedilla,Igrave,Iacute,Icircumflex,Idieresis,Ntilde,multiply,Thorn,eth,circumflex,tilde,bullet,trademark" 	g2="Y,Yacute,Ydieresis" 	k="41" />
<hkern g1="numbersign,dollar,percent,parenright,less,equal,greater,question,H,I,M,N,bracketright,asciicircum,underscore,grave,bar,braceright,asciitilde,cent,sterling,currency,yen,brokenbar,section,dieresis,logicalnot,registered,macron,degree,plusminus,acute,paragraph,cedilla,Igrave,Iacute,Icircumflex,Idieresis,Ntilde,multiply,Thorn,eth,circumflex,tilde,bullet,trademark" 	g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="-20" />
<hkern g1="numbersign,dollar,percent,parenright,less,equal,greater,question,H,I,M,N,bracketright,asciicircum,underscore,grave,bar,braceright,asciitilde,cent,sterling,currency,yen,brokenbar,section,dieresis,logicalnot,registered,macron,degree,plusminus,acute,paragraph,cedilla,Igrave,Iacute,Icircumflex,Idieresis,Ntilde,multiply,Thorn,eth,circumflex,tilde,bullet,trademark" 	g2="colon,semicolon" 	k="-20" />
<hkern g1="numbersign,dollar,percent,parenright,less,equal,greater,question,H,I,M,N,bracketright,asciicircum,underscore,grave,bar,braceright,asciitilde,cent,sterling,currency,yen,brokenbar,section,dieresis,logicalnot,registered,macron,degree,plusminus,acute,paragraph,cedilla,Igrave,Iacute,Icircumflex,Idieresis,Ntilde,multiply,Thorn,eth,circumflex,tilde,bullet,trademark" 	g2="comma,period,ellipsis" 	k="-20" />
<hkern g1="numbersign,dollar,percent,parenright,less,equal,greater,question,H,I,M,N,bracketright,asciicircum,underscore,grave,bar,braceright,asciitilde,cent,sterling,currency,yen,brokenbar,section,dieresis,logicalnot,registered,macron,degree,plusminus,acute,paragraph,cedilla,Igrave,Iacute,Icircumflex,Idieresis,Ntilde,multiply,Thorn,eth,circumflex,tilde,bullet,trademark" 	g2="d,q" 	k="-20" />
<hkern g1="numbersign,dollar,percent,parenright,less,equal,greater,question,H,I,M,N,bracketright,asciicircum,underscore,grave,bar,braceright,asciitilde,cent,sterling,currency,yen,brokenbar,section,dieresis,logicalnot,registered,macron,degree,plusminus,acute,paragraph,cedilla,Igrave,Iacute,Icircumflex,Idieresis,Ntilde,multiply,Thorn,eth,circumflex,tilde,bullet,trademark" 	g2="g" 	k="-20" />
<hkern g1="numbersign,dollar,percent,parenright,less,equal,greater,question,H,I,M,N,bracketright,asciicircum,underscore,grave,bar,braceright,asciitilde,cent,sterling,currency,yen,brokenbar,section,dieresis,logicalnot,registered,macron,degree,plusminus,acute,paragraph,cedilla,Igrave,Iacute,Icircumflex,Idieresis,Ntilde,multiply,Thorn,eth,circumflex,tilde,bullet,trademark" 	g2="guilsinglright" 	k="-20" />
<hkern g1="numbersign,dollar,percent,parenright,less,equal,greater,question,H,I,M,N,bracketright,asciicircum,underscore,grave,bar,braceright,asciitilde,cent,sterling,currency,yen,brokenbar,section,dieresis,logicalnot,registered,macron,degree,plusminus,acute,paragraph,cedilla,Igrave,Iacute,Icircumflex,Idieresis,Ntilde,multiply,Thorn,eth,circumflex,tilde,bullet,trademark" 	g2="hyphen,endash,emdash" 	k="-31" />
<hkern g1="numbersign,dollar,percent,parenright,less,equal,greater,question,H,I,M,N,bracketright,asciicircum,underscore,grave,bar,braceright,asciitilde,cent,sterling,currency,yen,brokenbar,section,dieresis,logicalnot,registered,macron,degree,plusminus,acute,paragraph,cedilla,Igrave,Iacute,Icircumflex,Idieresis,Ntilde,multiply,Thorn,eth,circumflex,tilde,bullet,trademark" 	g2="quotedbl,quotesingle" 	k="20" />
<hkern g1="numbersign,dollar,percent,parenright,less,equal,greater,question,H,I,M,N,bracketright,asciicircum,underscore,grave,bar,braceright,asciitilde,cent,sterling,currency,yen,brokenbar,section,dieresis,logicalnot,registered,macron,degree,plusminus,acute,paragraph,cedilla,Igrave,Iacute,Icircumflex,Idieresis,Ntilde,multiply,Thorn,eth,circumflex,tilde,bullet,trademark" 	g2="quoteleft,quotedblleft" 	k="20" />
<hkern g1="numbersign,dollar,percent,parenright,less,equal,greater,question,H,I,M,N,bracketright,asciicircum,underscore,grave,bar,braceright,asciitilde,cent,sterling,currency,yen,brokenbar,section,dieresis,logicalnot,registered,macron,degree,plusminus,acute,paragraph,cedilla,Igrave,Iacute,Icircumflex,Idieresis,Ntilde,multiply,Thorn,eth,circumflex,tilde,bullet,trademark" 	g2="quoteright,quotedblright" 	k="20" />
<hkern g1="numbersign,dollar,percent,parenright,less,equal,greater,question,H,I,M,N,bracketright,asciicircum,underscore,grave,bar,braceright,asciitilde,cent,sterling,currency,yen,brokenbar,section,dieresis,logicalnot,registered,macron,degree,plusminus,acute,paragraph,cedilla,Igrave,Iacute,Icircumflex,Idieresis,Ntilde,multiply,Thorn,eth,circumflex,tilde,bullet,trademark" 	g2="quotesinglbase,quotedblbase" 	k="-20" />
<hkern g1="numbersign,dollar,percent,parenright,less,equal,greater,question,H,I,M,N,bracketright,asciicircum,underscore,grave,bar,braceright,asciitilde,cent,sterling,currency,yen,brokenbar,section,dieresis,logicalnot,registered,macron,degree,plusminus,acute,paragraph,cedilla,Igrave,Iacute,Icircumflex,Idieresis,Ntilde,multiply,Thorn,eth,circumflex,tilde,bullet,trademark" 	g2="AE" 	k="20" />
<hkern g1="numbersign,dollar,percent,parenright,less,equal,greater,question,H,I,M,N,bracketright,asciicircum,underscore,grave,bar,braceright,asciitilde,cent,sterling,currency,yen,brokenbar,section,dieresis,logicalnot,registered,macron,degree,plusminus,acute,paragraph,cedilla,Igrave,Iacute,Icircumflex,Idieresis,Ntilde,multiply,Thorn,eth,circumflex,tilde,bullet,trademark" 	g2="z" 	k="31" />
<hkern g1="numbersign,dollar,percent,parenright,less,equal,greater,question,H,I,M,N,bracketright,asciicircum,underscore,grave,bar,braceright,asciitilde,cent,sterling,currency,yen,brokenbar,section,dieresis,logicalnot,registered,macron,degree,plusminus,acute,paragraph,cedilla,Igrave,Iacute,Icircumflex,Idieresis,Ntilde,multiply,Thorn,eth,circumflex,tilde,bullet,trademark" 	g2="J" 	k="-31" />
<hkern g1="numbersign,dollar,percent,parenright,less,equal,greater,question,H,I,M,N,bracketright,asciicircum,underscore,grave,bar,braceright,asciitilde,cent,sterling,currency,yen,brokenbar,section,dieresis,logicalnot,registered,macron,degree,plusminus,acute,paragraph,cedilla,Igrave,Iacute,Icircumflex,Idieresis,Ntilde,multiply,Thorn,eth,circumflex,tilde,bullet,trademark" 	g2="bracketright,braceright" 	k="20" />
<hkern g1="a,agrave,aacute,acircumflex,atilde,adieresis,aring" 	g2="asterisk" 	k="72" />
<hkern g1="a,agrave,aacute,acircumflex,atilde,adieresis,aring" 	g2="backslash" 	k="164" />
<hkern g1="a,agrave,aacute,acircumflex,atilde,adieresis,aring" 	g2="colon,semicolon" 	k="-20" />
<hkern g1="a,agrave,aacute,acircumflex,atilde,adieresis,aring" 	g2="comma,period,ellipsis" 	k="-31" />
<hkern g1="a,agrave,aacute,acircumflex,atilde,adieresis,aring" 	g2="f" 	k="10" />
<hkern g1="a,agrave,aacute,acircumflex,atilde,adieresis,aring" 	g2="question" 	k="31" />
<hkern g1="a,agrave,aacute,acircumflex,atilde,adieresis,aring" 	g2="quotedbl,quotesingle" 	k="61" />
<hkern g1="a,agrave,aacute,acircumflex,atilde,adieresis,aring" 	g2="quoteleft,quotedblleft" 	k="82" />
<hkern g1="a,agrave,aacute,acircumflex,atilde,adieresis,aring" 	g2="quoteright,quotedblright" 	k="61" />
<hkern g1="a,agrave,aacute,acircumflex,atilde,adieresis,aring" 	g2="quotesinglbase,quotedblbase" 	k="-31" />
<hkern g1="a,agrave,aacute,acircumflex,atilde,adieresis,aring" 	g2="slash" 	k="-41" />
<hkern g1="a,agrave,aacute,acircumflex,atilde,adieresis,aring" 	g2="t" 	k="20" />
<hkern g1="a,agrave,aacute,acircumflex,atilde,adieresis,aring" 	g2="trademark" 	k="102" />
<hkern g1="a,agrave,aacute,acircumflex,atilde,adieresis,aring" 	g2="v" 	k="20" />
<hkern g1="a,agrave,aacute,acircumflex,atilde,adieresis,aring" 	g2="w" 	k="20" />
<hkern g1="a,agrave,aacute,acircumflex,atilde,adieresis,aring" 	g2="y,yacute,ydieresis" 	k="20" />
<hkern g1="b,p,thorn" 	g2="asterisk" 	k="82" />
<hkern g1="b,p,thorn" 	g2="backslash" 	k="143" />
<hkern g1="b,p,thorn" 	g2="comma,period,ellipsis" 	k="41" />
<hkern g1="b,p,thorn" 	g2="f" 	k="20" />
<hkern g1="b,p,thorn" 	g2="question" 	k="20" />
<hkern g1="b,p,thorn" 	g2="quotedbl,quotesingle" 	k="82" />
<hkern g1="b,p,thorn" 	g2="quoteleft,quotedblleft" 	k="82" />
<hkern g1="b,p,thorn" 	g2="quoteright,quotedblright" 	k="82" />
<hkern g1="b,p,thorn" 	g2="quotesinglbase,quotedblbase" 	k="41" />
<hkern g1="b,p,thorn" 	g2="slash" 	k="20" />
<hkern g1="b,p,thorn" 	g2="t" 	k="31" />
<hkern g1="b,p,thorn" 	g2="trademark" 	k="82" />
<hkern g1="b,p,thorn" 	g2="v" 	k="20" />
<hkern g1="b,p,thorn" 	g2="w" 	k="20" />
<hkern g1="b,p,thorn" 	g2="y,yacute,ydieresis" 	k="20" />
<hkern g1="b,p,thorn" 	g2="at" 	k="-20" />
<hkern g1="b,p,thorn" 	g2="bracketright,braceright" 	k="20" />
<hkern g1="b,p,thorn" 	g2="parenright" 	k="20" />
<hkern g1="b,p,thorn" 	g2="x" 	k="51" />
<hkern g1="b,p,thorn" 	g2="z" 	k="41" />
<hkern g1="c,ccedilla" 	g2="asterisk" 	k="31" />
<hkern g1="c,ccedilla" 	g2="backslash" 	k="123" />
<hkern g1="c,ccedilla" 	g2="question" 	k="20" />
<hkern g1="c,ccedilla" 	g2="quotedbl,quotesingle" 	k="41" />
<hkern g1="c,ccedilla" 	g2="quoteleft,quotedblleft" 	k="31" />
<hkern g1="c,ccedilla" 	g2="quoteright,quotedblright" 	k="41" />
<hkern g1="c,ccedilla" 	g2="slash" 	k="-20" />
<hkern g1="c,ccedilla" 	g2="trademark" 	k="51" />
<hkern g1="c,ccedilla" 	g2="at" 	k="-20" />
<hkern g1="c,ccedilla" 	g2="x" 	k="31" />
<hkern g1="c,ccedilla" 	g2="z" 	k="10" />
<hkern g1="c,ccedilla" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="20" />
<hkern g1="c,ccedilla" 	g2="AE" 	k="10" />
<hkern g1="c,ccedilla" 	g2="J" 	k="-20" />
<hkern g1="c,ccedilla" 	g2="S" 	k="-10" />
<hkern g1="c,ccedilla" 	g2="T" 	k="184" />
<hkern g1="c,ccedilla" 	g2="V" 	k="102" />
<hkern g1="c,ccedilla" 	g2="W" 	k="61" />
<hkern g1="c,ccedilla" 	g2="X" 	k="31" />
<hkern g1="c,ccedilla" 	g2="Y,Yacute,Ydieresis" 	k="174" />
<hkern g1="c,ccedilla" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="-20" />
<hkern g1="c,ccedilla" 	g2="s" 	k="-20" />
<hkern g1="d,i,l,igrave,iacute,icircumflex,idieresis" 	g2="question" 	k="-20" />
<hkern g1="d,i,l,igrave,iacute,icircumflex,idieresis" 	g2="b,h,i,k,l,igrave,iacute,icircumflex,idieresis" 	k="20" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe" 	g2="asterisk" 	k="82" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe" 	g2="backslash" 	k="143" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe" 	g2="comma,period,ellipsis" 	k="20" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe" 	g2="f" 	k="20" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe" 	g2="question" 	k="41" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe" 	g2="quotedbl,quotesingle" 	k="61" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe" 	g2="quoteleft,quotedblleft" 	k="72" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe" 	g2="quoteright,quotedblright" 	k="61" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe" 	g2="quotesinglbase,quotedblbase" 	k="20" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe" 	g2="t" 	k="20" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe" 	g2="trademark" 	k="102" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe" 	g2="v" 	k="20" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe" 	g2="w" 	k="20" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe" 	g2="y,yacute,ydieresis" 	k="20" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe" 	g2="at" 	k="-20" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe" 	g2="x" 	k="51" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe" 	g2="z" 	k="41" />
<hkern g1="f" 	g2="asterisk" 	k="-20" />
<hkern g1="f" 	g2="colon,semicolon" 	k="20" />
<hkern g1="f" 	g2="comma,period,ellipsis" 	k="82" />
<hkern g1="f" 	g2="f" 	k="20" />
<hkern g1="f" 	g2="question" 	k="-41" />
<hkern g1="f" 	g2="quotedbl,quotesingle" 	k="-31" />
<hkern g1="f" 	g2="quoteleft,quotedblleft" 	k="-20" />
<hkern g1="f" 	g2="quoteright,quotedblright" 	k="-31" />
<hkern g1="f" 	g2="quotesinglbase,quotedblbase" 	k="82" />
<hkern g1="f" 	g2="slash" 	k="82" />
<hkern g1="f" 	g2="t" 	k="20" />
<hkern g1="f" 	g2="trademark" 	k="-41" />
<hkern g1="f" 	g2="v" 	k="-20" />
<hkern g1="f" 	g2="w" 	k="-20" />
<hkern g1="f" 	g2="y,yacute,ydieresis" 	k="-20" />
<hkern g1="f" 	g2="bracketright,braceright" 	k="-31" />
<hkern g1="f" 	g2="parenright" 	k="-20" />
<hkern g1="f" 	g2="z" 	k="31" />
<hkern g1="f" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="31" />
<hkern g1="f" 	g2="s" 	k="31" />
<hkern g1="f" 	g2="ampersand" 	k="61" />
<hkern g1="f" 	g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="31" />
<hkern g1="f" 	g2="d,q" 	k="41" />
<hkern g1="f" 	g2="g" 	k="41" />
<hkern g1="f" 	g2="guilsinglleft" 	k="72" />
<hkern g1="f" 	g2="hyphen,endash,emdash" 	k="41" />
<hkern g1="f" 	g2="periodcentered" 	k="41" />
<hkern g1="g" 	g2="backslash" 	k="123" />
<hkern g1="g" 	g2="trademark" 	k="61" />
<hkern g1="g" 	g2="j" 	k="-41" />
<hkern g1="germandbls" 	g2="quotedbl,quotesingle" 	k="31" />
<hkern g1="germandbls" 	g2="quoteleft,quotedblleft" 	k="20" />
<hkern g1="germandbls" 	g2="quoteright,quotedblright" 	k="31" />
<hkern g1="germandbls" 	g2="t" 	k="20" />
<hkern g1="germandbls" 	g2="w" 	k="10" />
<hkern g1="germandbls" 	g2="z" 	k="20" />
<hkern g1="germandbls" 	g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="-20" />
<hkern g1="germandbls" 	g2="hyphen,endash,emdash" 	k="-20" />
<hkern g1="h,m,n,ntilde" 	g2="asterisk" 	k="41" />
<hkern g1="h,m,n,ntilde" 	g2="backslash" 	k="133" />
<hkern g1="h,m,n,ntilde" 	g2="f" 	k="10" />
<hkern g1="h,m,n,ntilde" 	g2="question" 	k="20" />
<hkern g1="h,m,n,ntilde" 	g2="quotedbl,quotesingle" 	k="51" />
<hkern g1="h,m,n,ntilde" 	g2="quoteleft,quotedblleft" 	k="41" />
<hkern g1="h,m,n,ntilde" 	g2="quoteright,quotedblright" 	k="51" />
<hkern g1="h,m,n,ntilde" 	g2="t" 	k="20" />
<hkern g1="h,m,n,ntilde" 	g2="trademark" 	k="102" />
<hkern g1="h,m,n,ntilde" 	g2="v" 	k="20" />
<hkern g1="h,m,n,ntilde" 	g2="w" 	k="20" />
<hkern g1="h,m,n,ntilde" 	g2="y,yacute,ydieresis" 	k="20" />
<hkern g1="j" 	g2="j" 	k="-41" />
<hkern g1="k" 	g2="backslash" 	k="72" />
<hkern g1="k" 	g2="comma,period,ellipsis" 	k="-20" />
<hkern g1="k" 	g2="question" 	k="-20" />
<hkern g1="k" 	g2="quotesinglbase,quotedblbase" 	k="-20" />
<hkern g1="k" 	g2="slash" 	k="-20" />
<hkern g1="k" 	g2="t" 	k="20" />
<hkern g1="k" 	g2="trademark" 	k="61" />
<hkern g1="k" 	g2="at" 	k="20" />
<hkern g1="k" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="20" />
<hkern g1="k" 	g2="s" 	k="31" />
<hkern g1="k" 	g2="ampersand" 	k="41" />
<hkern g1="k" 	g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="61" />
<hkern g1="k" 	g2="d,q" 	k="61" />
<hkern g1="k" 	g2="g" 	k="61" />
<hkern g1="k" 	g2="guilsinglleft" 	k="102" />
<hkern g1="k" 	g2="hyphen,endash,emdash" 	k="102" />
<hkern g1="k" 	g2="periodcentered" 	k="82" />
<hkern g1="k" 	g2="copyright" 	k="20" />
<hkern g1="k" 	g2="guilsinglright" 	k="20" />
<hkern g1="k" 	g2="registered" 	k="20" />
<hkern g1="k" 	g2="u,ugrave,uacute,ucircumflex,udieresis" 	k="31" />
<hkern g1="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash" 	g2="asterisk" 	k="82" />
<hkern g1="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash" 	g2="backslash" 	k="164" />
<hkern g1="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash" 	g2="comma,period,ellipsis" 	k="51" />
<hkern g1="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash" 	g2="f" 	k="20" />
<hkern g1="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash" 	g2="question" 	k="51" />
<hkern g1="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash" 	g2="quotedbl,quotesingle" 	k="82" />
<hkern g1="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash" 	g2="quoteleft,quotedblleft" 	k="92" />
<hkern g1="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash" 	g2="quoteright,quotedblright" 	k="82" />
<hkern g1="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash" 	g2="quotesinglbase,quotedblbase" 	k="51" />
<hkern g1="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash" 	g2="slash" 	k="20" />
<hkern g1="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash" 	g2="t" 	k="20" />
<hkern g1="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash" 	g2="trademark" 	k="143" />
<hkern g1="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash" 	g2="v" 	k="20" />
<hkern g1="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash" 	g2="w" 	k="20" />
<hkern g1="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash" 	g2="y,yacute,ydieresis" 	k="20" />
<hkern g1="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash" 	g2="at" 	k="-20" />
<hkern g1="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash" 	g2="bracketright,braceright" 	k="20" />
<hkern g1="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash" 	g2="parenright" 	k="20" />
<hkern g1="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash" 	g2="x" 	k="72" />
<hkern g1="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash" 	g2="z" 	k="41" />
<hkern g1="q" 	g2="j" 	k="-51" />
<hkern g1="r" 	g2="asterisk" 	k="-51" />
<hkern g1="r" 	g2="backslash" 	k="51" />
<hkern g1="r" 	g2="comma,period,ellipsis" 	k="123" />
<hkern g1="r" 	g2="question" 	k="-41" />
<hkern g1="r" 	g2="quotedbl,quotesingle" 	k="-41" />
<hkern g1="r" 	g2="quoteleft,quotedblleft" 	k="-41" />
<hkern g1="r" 	g2="quoteright,quotedblright" 	k="-41" />
<hkern g1="r" 	g2="quotesinglbase,quotedblbase" 	k="123" />
<hkern g1="r" 	g2="slash" 	k="102" />
<hkern g1="r" 	g2="parenright" 	k="20" />
<hkern g1="r" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="41" />
<hkern g1="r" 	g2="ampersand" 	k="51" />
<hkern g1="r" 	g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="41" />
<hkern g1="r" 	g2="d,q" 	k="41" />
<hkern g1="r" 	g2="g" 	k="41" />
<hkern g1="r" 	g2="guilsinglleft" 	k="51" />
<hkern g1="r" 	g2="hyphen,endash,emdash" 	k="41" />
<hkern g1="r" 	g2="periodcentered" 	k="20" />
<hkern g1="r" 	g2="copyright" 	k="-31" />
<hkern g1="r" 	g2="registered" 	k="-31" />
<hkern g1="s" 	g2="asterisk" 	k="61" />
<hkern g1="s" 	g2="backslash" 	k="174" />
<hkern g1="s" 	g2="f" 	k="20" />
<hkern g1="s" 	g2="question" 	k="20" />
<hkern g1="s" 	g2="quotedbl,quotesingle" 	k="61" />
<hkern g1="s" 	g2="quoteleft,quotedblleft" 	k="61" />
<hkern g1="s" 	g2="quoteright,quotedblright" 	k="61" />
<hkern g1="s" 	g2="t" 	k="20" />
<hkern g1="s" 	g2="trademark" 	k="82" />
<hkern g1="s" 	g2="v" 	k="10" />
<hkern g1="s" 	g2="w" 	k="10" />
<hkern g1="s" 	g2="y,yacute,ydieresis" 	k="10" />
<hkern g1="s" 	g2="x" 	k="20" />
<hkern g1="s" 	g2="z" 	k="20" />
<hkern g1="s" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="-10" />
<hkern g1="s" 	g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="-20" />
<hkern g1="s" 	g2="d,q" 	k="-20" />
<hkern g1="s" 	g2="g" 	k="-20" />
<hkern g1="s" 	g2="guilsinglleft" 	k="20" />
<hkern g1="s" 	g2="hyphen,endash,emdash" 	k="-20" />
<hkern g1="t" 	g2="backslash" 	k="51" />
<hkern g1="t" 	g2="comma,period,ellipsis" 	k="-20" />
<hkern g1="t" 	g2="f" 	k="31" />
<hkern g1="t" 	g2="question" 	k="-20" />
<hkern g1="t" 	g2="quotesinglbase,quotedblbase" 	k="-20" />
<hkern g1="t" 	g2="t" 	k="31" />
<hkern g1="t" 	g2="v" 	k="-20" />
<hkern g1="t" 	g2="w" 	k="-20" />
<hkern g1="t" 	g2="ampersand" 	k="10" />
<hkern g1="t" 	g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="10" />
<hkern g1="t" 	g2="d,q" 	k="10" />
<hkern g1="t" 	g2="g" 	k="10" />
<hkern g1="t" 	g2="guilsinglleft" 	k="41" />
<hkern g1="t" 	g2="hyphen,endash,emdash" 	k="20" />
<hkern g1="t" 	g2="periodcentered" 	k="20" />
<hkern g1="t" 	g2="copyright" 	k="-20" />
<hkern g1="t" 	g2="registered" 	k="-20" />
<hkern g1="u,ugrave,uacute,ucircumflex,udieresis" 	g2="backslash" 	k="123" />
<hkern g1="u,ugrave,uacute,ucircumflex,udieresis" 	g2="trademark" 	k="61" />
<hkern g1="v" 	g2="colon,semicolon" 	k="20" />
<hkern g1="v" 	g2="comma,period,ellipsis" 	k="123" />
<hkern g1="v" 	g2="f" 	k="-20" />
<hkern g1="v" 	g2="quotedbl,quotesingle" 	k="-41" />
<hkern g1="v" 	g2="quoteleft,quotedblleft" 	k="-41" />
<hkern g1="v" 	g2="quoteright,quotedblright" 	k="-41" />
<hkern g1="v" 	g2="quotesinglbase,quotedblbase" 	k="123" />
<hkern g1="v" 	g2="t" 	k="-20" />
<hkern g1="v" 	g2="w" 	k="-31" />
<hkern g1="v" 	g2="y,yacute,ydieresis" 	k="-31" />
<hkern g1="v" 	g2="bracketright,braceright" 	k="20" />
<hkern g1="v" 	g2="z" 	k="20" />
<hkern g1="v" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="31" />
<hkern g1="v" 	g2="s" 	k="10" />
<hkern g1="v" 	g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="20" />
<hkern g1="v" 	g2="d,q" 	k="20" />
<hkern g1="v" 	g2="g" 	k="20" />
<hkern g1="v" 	g2="guilsinglleft" 	k="61" />
<hkern g1="v" 	g2="hyphen,endash,emdash" 	k="41" />
<hkern g1="w" 	g2="asterisk" 	k="-31" />
<hkern g1="w" 	g2="backslash" 	k="61" />
<hkern g1="w" 	g2="colon,semicolon" 	k="20" />
<hkern g1="w" 	g2="comma,period,ellipsis" 	k="123" />
<hkern g1="w" 	g2="f" 	k="-20" />
<hkern g1="w" 	g2="question" 	k="-72" />
<hkern g1="w" 	g2="quotedbl,quotesingle" 	k="-41" />
<hkern g1="w" 	g2="quoteleft,quotedblleft" 	k="-41" />
<hkern g1="w" 	g2="quoteright,quotedblright" 	k="-41" />
<hkern g1="w" 	g2="quotesinglbase,quotedblbase" 	k="123" />
<hkern g1="w" 	g2="slash" 	k="61" />
<hkern g1="w" 	g2="t" 	k="-20" />
<hkern g1="w" 	g2="v" 	k="-31" />
<hkern g1="w" 	g2="w" 	k="-31" />
<hkern g1="w" 	g2="y,yacute,ydieresis" 	k="-31" />
<hkern g1="w" 	g2="bracketright,braceright" 	k="20" />
<hkern g1="w" 	g2="parenright" 	k="41" />
<hkern g1="w" 	g2="z" 	k="20" />
<hkern g1="w" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="31" />
<hkern g1="w" 	g2="s" 	k="10" />
<hkern g1="w" 	g2="ampersand" 	k="61" />
<hkern g1="w" 	g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="20" />
<hkern g1="w" 	g2="d,q" 	k="20" />
<hkern g1="w" 	g2="g" 	k="20" />
<hkern g1="w" 	g2="guilsinglleft" 	k="61" />
<hkern g1="w" 	g2="hyphen,endash,emdash" 	k="41" />
<hkern g1="w" 	g2="periodcentered" 	k="20" />
<hkern g1="x" 	g2="f" 	k="20" />
<hkern g1="x" 	g2="quotedbl,quotesingle" 	k="20" />
<hkern g1="x" 	g2="quoteleft,quotedblleft" 	k="20" />
<hkern g1="x" 	g2="quoteright,quotedblright" 	k="20" />
<hkern g1="x" 	g2="t" 	k="41" />
<hkern g1="x" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="31" />
<hkern g1="x" 	g2="s" 	k="41" />
<hkern g1="x" 	g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="72" />
<hkern g1="x" 	g2="d,q" 	k="51" />
<hkern g1="x" 	g2="g" 	k="51" />
<hkern g1="x" 	g2="guilsinglleft" 	k="82" />
<hkern g1="x" 	g2="hyphen,endash,emdash" 	k="82" />
<hkern g1="x" 	g2="copyright" 	k="31" />
<hkern g1="x" 	g2="guilsinglright" 	k="20" />
<hkern g1="x" 	g2="u,ugrave,uacute,ucircumflex,udieresis" 	k="31" />
<hkern g1="y,yacute,ydieresis" 	g2="asterisk" 	k="-31" />
<hkern g1="y,yacute,ydieresis" 	g2="backslash" 	k="61" />
<hkern g1="y,yacute,ydieresis" 	g2="colon,semicolon" 	k="20" />
<hkern g1="y,yacute,ydieresis" 	g2="comma,period,ellipsis" 	k="123" />
<hkern g1="y,yacute,ydieresis" 	g2="f" 	k="-20" />
<hkern g1="y,yacute,ydieresis" 	g2="question" 	k="-72" />
<hkern g1="y,yacute,ydieresis" 	g2="quotedbl,quotesingle" 	k="-41" />
<hkern g1="y,yacute,ydieresis" 	g2="quoteleft,quotedblleft" 	k="-41" />
<hkern g1="y,yacute,ydieresis" 	g2="quoteright,quotedblright" 	k="-41" />
<hkern g1="y,yacute,ydieresis" 	g2="quotesinglbase,quotedblbase" 	k="123" />
<hkern g1="y,yacute,ydieresis" 	g2="slash" 	k="61" />
<hkern g1="y,yacute,ydieresis" 	g2="t" 	k="-20" />
<hkern g1="y,yacute,ydieresis" 	g2="v" 	k="-31" />
<hkern g1="y,yacute,ydieresis" 	g2="w" 	k="-31" />
<hkern g1="y,yacute,ydieresis" 	g2="y,yacute,ydieresis" 	k="-31" />
<hkern g1="y,yacute,ydieresis" 	g2="bracketright,braceright" 	k="20" />
<hkern g1="y,yacute,ydieresis" 	g2="parenright" 	k="41" />
<hkern g1="y,yacute,ydieresis" 	g2="z" 	k="20" />
<hkern g1="y,yacute,ydieresis" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="31" />
<hkern g1="y,yacute,ydieresis" 	g2="s" 	k="10" />
<hkern g1="y,yacute,ydieresis" 	g2="ampersand" 	k="61" />
<hkern g1="y,yacute,ydieresis" 	g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="20" />
<hkern g1="y,yacute,ydieresis" 	g2="d,q" 	k="20" />
<hkern g1="y,yacute,ydieresis" 	g2="g" 	k="20" />
<hkern g1="y,yacute,ydieresis" 	g2="guilsinglleft" 	k="61" />
<hkern g1="y,yacute,ydieresis" 	g2="hyphen,endash,emdash" 	k="41" />
<hkern g1="y,yacute,ydieresis" 	g2="periodcentered" 	k="20" />
<hkern g1="z" 	g2="asterisk" 	k="10" />
<hkern g1="z" 	g2="backslash" 	k="102" />
<hkern g1="z" 	g2="comma,period,ellipsis" 	k="-20" />
<hkern g1="z" 	g2="question" 	k="-20" />
<hkern g1="z" 	g2="quotesinglbase,quotedblbase" 	k="-20" />
<hkern g1="z" 	g2="s" 	k="20" />
<hkern g1="z" 	g2="ampersand" 	k="41" />
<hkern g1="z" 	g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="41" />
<hkern g1="z" 	g2="d,q" 	k="31" />
<hkern g1="z" 	g2="g" 	k="31" />
<hkern g1="z" 	g2="guilsinglleft" 	k="41" />
<hkern g1="z" 	g2="hyphen,endash,emdash" 	k="41" />
<hkern g1="z" 	g2="periodcentered" 	k="20" />
<hkern g1="ampersand" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="-31" />
<hkern g1="ampersand" 	g2="J" 	k="-20" />
<hkern g1="ampersand" 	g2="T" 	k="143" />
<hkern g1="ampersand" 	g2="W" 	k="92" />
<hkern g1="ampersand" 	g2="Y,Yacute,Ydieresis" 	k="174" />
<hkern g1="ampersand" 	g2="Z" 	k="-31" />
<hkern g1="ampersand" 	g2="j" 	k="-41" />
<hkern g1="ampersand" 	g2="t" 	k="20" />
<hkern g1="ampersand" 	g2="w" 	k="61" />
<hkern g1="ampersand" 	g2="y,yacute,ydieresis" 	k="41" />
<hkern g1="ampersand" 	g2="z" 	k="-20" />
<hkern g1="at" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="61" />
<hkern g1="at" 	g2="T" 	k="123" />
<hkern g1="at" 	g2="W" 	k="51" />
<hkern g1="at" 	g2="Y,Yacute,Ydieresis" 	k="123" />
<hkern g1="at" 	g2="Z" 	k="41" />
<hkern g1="at" 	g2="w" 	k="-20" />
<hkern g1="at" 	g2="z" 	k="31" />
<hkern g1="at" 	g2="AE" 	k="61" />
<hkern g1="at" 	g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="-20" />
<hkern g1="at" 	g2="d,q" 	k="-20" />
<hkern g1="at" 	g2="g" 	k="-20" />
<hkern g1="copyright" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="61" />
<hkern g1="copyright" 	g2="T" 	k="82" />
<hkern g1="copyright" 	g2="W" 	k="51" />
<hkern g1="copyright" 	g2="Y,Yacute,Ydieresis" 	k="113" />
<hkern g1="copyright" 	g2="Z" 	k="61" />
<hkern g1="copyright" 	g2="z" 	k="20" />
<hkern g1="copyright" 	g2="AE" 	k="61" />
<hkern g1="copyright" 	g2="V" 	k="51" />
<hkern g1="copyright" 	g2="X" 	k="82" />
<hkern g1="copyright" 	g2="x" 	k="31" />
<hkern g1="asterisk" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="184" />
<hkern g1="asterisk" 	g2="AE" 	k="205" />
<hkern g1="asterisk" 	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="41" />
<hkern g1="asterisk" 	g2="J" 	k="246" />
<hkern g1="asterisk" 	g2="S" 	k="20" />
<hkern g1="asterisk" 	g2="Y,Yacute,Ydieresis" 	k="20" />
<hkern g1="asterisk" 	g2="Z" 	k="31" />
<hkern g1="asterisk" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="82" />
<hkern g1="asterisk" 	g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="82" />
<hkern g1="asterisk" 	g2="d,q" 	k="82" />
<hkern g1="asterisk" 	g2="g" 	k="82" />
<hkern g1="asterisk" 	g2="s" 	k="82" />
<hkern g1="asterisk" 	g2="w" 	k="-31" />
<hkern g1="asterisk" 	g2="y,yacute,ydieresis" 	k="-31" />
<hkern g1="asterisk" 	g2="z" 	k="41" />
<hkern g1="backslash" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="-20" />
<hkern g1="backslash" 	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="82" />
<hkern g1="backslash" 	g2="Y,Yacute,Ydieresis" 	k="205" />
<hkern g1="backslash" 	g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="20" />
<hkern g1="backslash" 	g2="d,q" 	k="20" />
<hkern g1="backslash" 	g2="g" 	k="20" />
<hkern g1="backslash" 	g2="s" 	k="10" />
<hkern g1="backslash" 	g2="w" 	k="61" />
<hkern g1="backslash" 	g2="y,yacute,ydieresis" 	k="20" />
<hkern g1="backslash" 	g2="T" 	k="184" />
<hkern g1="backslash" 	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	k="41" />
<hkern g1="backslash" 	g2="W" 	k="143" />
<hkern g1="backslash" 	g2="f" 	k="41" />
<hkern g1="backslash" 	g2="t" 	k="61" />
<hkern g1="backslash" 	g2="u,ugrave,uacute,ucircumflex,udieresis" 	k="20" />
<hkern g1="bracketleft,braceleft" 	g2="J" 	k="20" />
<hkern g1="bracketleft,braceleft" 	g2="Y,Yacute,Ydieresis" 	k="-20" />
<hkern g1="bracketleft,braceleft" 	g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="20" />
<hkern g1="bracketleft,braceleft" 	g2="d,q" 	k="20" />
<hkern g1="bracketleft,braceleft" 	g2="g" 	k="20" />
<hkern g1="bracketleft,braceleft" 	g2="w" 	k="20" />
<hkern g1="bracketleft,braceleft" 	g2="T" 	k="-31" />
<hkern g1="bracketleft,braceleft" 	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	k="-20" />
<hkern g1="bracketleft,braceleft" 	g2="W" 	k="-20" />
<hkern g1="bracketleft,braceleft" 	g2="t" 	k="20" />
<hkern g1="bracketleft,braceleft" 	g2="V" 	k="-20" />
<hkern g1="bracketleft,braceleft" 	g2="j" 	k="-92" />
<hkern g1="bracketleft,braceleft" 	g2="v" 	k="20" />
<hkern g1="colon,semicolon" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="-20" />
<hkern g1="colon,semicolon" 	g2="Y,Yacute,Ydieresis" 	k="143" />
<hkern g1="colon,semicolon" 	g2="Z" 	k="-20" />
<hkern g1="colon,semicolon" 	g2="w" 	k="20" />
<hkern g1="colon,semicolon" 	g2="T" 	k="164" />
<hkern g1="colon,semicolon" 	g2="W" 	k="61" />
<hkern g1="colon,semicolon" 	g2="t" 	k="20" />
<hkern g1="colon,semicolon" 	g2="V" 	k="82" />
<hkern g1="colon,semicolon" 	g2="j" 	k="-61" />
<hkern g1="colon,semicolon" 	g2="v" 	k="20" />
<hkern g1="comma,period,ellipsis" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="-31" />
<hkern g1="comma,period,ellipsis" 	g2="AE" 	k="-20" />
<hkern g1="comma,period,ellipsis" 	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="102" />
<hkern g1="comma,period,ellipsis" 	g2="J" 	k="-20" />
<hkern g1="comma,period,ellipsis" 	g2="S" 	k="31" />
<hkern g1="comma,period,ellipsis" 	g2="Y,Yacute,Ydieresis" 	k="205" />
<hkern g1="comma,period,ellipsis" 	g2="Z" 	k="-20" />
<hkern g1="comma,period,ellipsis" 	g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="51" />
<hkern g1="comma,period,ellipsis" 	g2="d,q" 	k="41" />
<hkern g1="comma,period,ellipsis" 	g2="g" 	k="41" />
<hkern g1="comma,period,ellipsis" 	g2="w" 	k="123" />
<hkern g1="comma,period,ellipsis" 	g2="y,yacute,ydieresis" 	k="61" />
<hkern g1="comma,period,ellipsis" 	g2="z" 	k="-20" />
<hkern g1="comma,period,ellipsis" 	g2="T" 	k="184" />
<hkern g1="comma,period,ellipsis" 	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	k="51" />
<hkern g1="comma,period,ellipsis" 	g2="W" 	k="143" />
<hkern g1="comma,period,ellipsis" 	g2="f" 	k="31" />
<hkern g1="comma,period,ellipsis" 	g2="t" 	k="82" />
<hkern g1="comma,period,ellipsis" 	g2="u,ugrave,uacute,ucircumflex,udieresis" 	k="31" />
<hkern g1="comma,period,ellipsis" 	g2="V" 	k="184" />
<hkern g1="comma,period,ellipsis" 	g2="j" 	k="-20" />
<hkern g1="comma,period,ellipsis" 	g2="v" 	k="123" />
<hkern g1="exclamdown" 	g2="Y,Yacute,Ydieresis" 	k="61" />
<hkern g1="exclamdown" 	g2="T" 	k="41" />
<hkern g1="exclamdown" 	g2="W" 	k="31" />
<hkern g1="guilsinglleft" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="20" />
<hkern g1="guilsinglleft" 	g2="Y,Yacute,Ydieresis" 	k="143" />
<hkern g1="guilsinglleft" 	g2="T" 	k="143" />
<hkern g1="guilsinglleft" 	g2="W" 	k="61" />
<hkern g1="guilsinglleft" 	g2="V" 	k="82" />
<hkern g1="guilsinglleft" 	g2="X" 	k="51" />
<hkern g1="guilsinglleft" 	g2="x" 	k="20" />
<hkern g1="guilsinglright" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="51" />
<hkern g1="guilsinglright" 	g2="AE" 	k="41" />
<hkern g1="guilsinglright" 	g2="Y,Yacute,Ydieresis" 	k="225" />
<hkern g1="guilsinglright" 	g2="Z" 	k="41" />
<hkern g1="guilsinglright" 	g2="s" 	k="20" />
<hkern g1="guilsinglright" 	g2="w" 	k="61" />
<hkern g1="guilsinglright" 	g2="y,yacute,ydieresis" 	k="41" />
<hkern g1="guilsinglright" 	g2="z" 	k="41" />
<hkern g1="guilsinglright" 	g2="T" 	k="225" />
<hkern g1="guilsinglright" 	g2="W" 	k="123" />
<hkern g1="guilsinglright" 	g2="f" 	k="31" />
<hkern g1="guilsinglright" 	g2="t" 	k="20" />
<hkern g1="guilsinglright" 	g2="V" 	k="143" />
<hkern g1="guilsinglright" 	g2="v" 	k="61" />
<hkern g1="guilsinglright" 	g2="X" 	k="113" />
<hkern g1="guilsinglright" 	g2="x" 	k="82" />
<hkern g1="hyphen,endash,emdash" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="41" />
<hkern g1="hyphen,endash,emdash" 	g2="AE" 	k="20" />
<hkern g1="hyphen,endash,emdash" 	g2="S" 	k="-20" />
<hkern g1="hyphen,endash,emdash" 	g2="Y,Yacute,Ydieresis" 	k="205" />
<hkern g1="hyphen,endash,emdash" 	g2="Z" 	k="20" />
<hkern g1="hyphen,endash,emdash" 	g2="w" 	k="41" />
<hkern g1="hyphen,endash,emdash" 	g2="y,yacute,ydieresis" 	k="41" />
<hkern g1="hyphen,endash,emdash" 	g2="z" 	k="20" />
<hkern g1="hyphen,endash,emdash" 	g2="T" 	k="164" />
<hkern g1="hyphen,endash,emdash" 	g2="W" 	k="82" />
<hkern g1="hyphen,endash,emdash" 	g2="V" 	k="133" />
<hkern g1="hyphen,endash,emdash" 	g2="v" 	k="41" />
<hkern g1="hyphen,endash,emdash" 	g2="X" 	k="102" />
<hkern g1="hyphen,endash,emdash" 	g2="x" 	k="82" />
<hkern g1="parenleft" 	g2="J" 	k="31" />
<hkern g1="parenleft" 	g2="S" 	k="20" />
<hkern g1="parenleft" 	g2="Y,Yacute,Ydieresis" 	k="-20" />
<hkern g1="parenleft" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="20" />
<hkern g1="parenleft" 	g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="20" />
<hkern g1="parenleft" 	g2="d,q" 	k="20" />
<hkern g1="parenleft" 	g2="g" 	k="20" />
<hkern g1="parenleft" 	g2="s" 	k="20" />
<hkern g1="parenleft" 	g2="w" 	k="41" />
<hkern g1="parenleft" 	g2="T" 	k="-20" />
<hkern g1="parenleft" 	g2="W" 	k="-20" />
<hkern g1="parenleft" 	g2="t" 	k="20" />
<hkern g1="parenleft" 	g2="u,ugrave,uacute,ucircumflex,udieresis" 	k="20" />
<hkern g1="parenleft" 	g2="j" 	k="-102" />
<hkern g1="periodcentered" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="61" />
<hkern g1="periodcentered" 	g2="AE" 	k="61" />
<hkern g1="periodcentered" 	g2="Y,Yacute,Ydieresis" 	k="143" />
<hkern g1="periodcentered" 	g2="Z" 	k="20" />
<hkern g1="periodcentered" 	g2="w" 	k="20" />
<hkern g1="periodcentered" 	g2="y,yacute,ydieresis" 	k="20" />
<hkern g1="periodcentered" 	g2="z" 	k="20" />
<hkern g1="periodcentered" 	g2="T" 	k="123" />
<hkern g1="periodcentered" 	g2="W" 	k="61" />
<hkern g1="questiondown" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="-20" />
<hkern g1="questiondown" 	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="41" />
<hkern g1="questiondown" 	g2="J" 	k="-31" />
<hkern g1="questiondown" 	g2="Y,Yacute,Ydieresis" 	k="225" />
<hkern g1="questiondown" 	g2="Z" 	k="-31" />
<hkern g1="questiondown" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="-20" />
<hkern g1="questiondown" 	g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="20" />
<hkern g1="questiondown" 	g2="d,q" 	k="20" />
<hkern g1="questiondown" 	g2="w" 	k="102" />
<hkern g1="questiondown" 	g2="y,yacute,ydieresis" 	k="41" />
<hkern g1="questiondown" 	g2="T" 	k="174" />
<hkern g1="questiondown" 	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	k="20" />
<hkern g1="questiondown" 	g2="W" 	k="143" />
<hkern g1="questiondown" 	g2="f" 	k="31" />
<hkern g1="questiondown" 	g2="t" 	k="51" />
<hkern g1="questiondown" 	g2="j" 	k="-82" />
<hkern g1="quoteleft,quotedblleft" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="164" />
<hkern g1="quoteleft,quotedblleft" 	g2="AE" 	k="184" />
<hkern g1="quoteleft,quotedblleft" 	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="20" />
<hkern g1="quoteleft,quotedblleft" 	g2="J" 	k="225" />
<hkern g1="quoteleft,quotedblleft" 	g2="S" 	k="20" />
<hkern g1="quoteleft,quotedblleft" 	g2="Y,Yacute,Ydieresis" 	k="-20" />
<hkern g1="quoteleft,quotedblleft" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="82" />
<hkern g1="quoteleft,quotedblleft" 	g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="82" />
<hkern g1="quoteleft,quotedblleft" 	g2="d,q" 	k="113" />
<hkern g1="quoteleft,quotedblleft" 	g2="g" 	k="113" />
<hkern g1="quoteleft,quotedblleft" 	g2="s" 	k="61" />
<hkern g1="quoteleft,quotedblleft" 	g2="w" 	k="-41" />
<hkern g1="quoteleft,quotedblleft" 	g2="y,yacute,ydieresis" 	k="-41" />
<hkern g1="quoteleft,quotedblleft" 	g2="T" 	k="-20" />
<hkern g1="quoteleft,quotedblleft" 	g2="W" 	k="-20" />
<hkern g1="quoteleft,quotedblleft" 	g2="t" 	k="-20" />
<hkern g1="quoteleft,quotedblleft" 	g2="V" 	k="-20" />
<hkern g1="quoteleft,quotedblleft" 	g2="v" 	k="-41" />
<hkern g1="quoteleft,quotedblleft" 	g2="X" 	k="20" />
<hkern g1="quoteleft,quotedblleft" 	g2="germandbls" 	k="10" />
<hkern g1="quoteright,quotedblright" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="205" />
<hkern g1="quoteright,quotedblright" 	g2="AE" 	k="205" />
<hkern g1="quoteright,quotedblright" 	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="51" />
<hkern g1="quoteright,quotedblright" 	g2="J" 	k="205" />
<hkern g1="quoteright,quotedblright" 	g2="S" 	k="31" />
<hkern g1="quoteright,quotedblright" 	g2="Y,Yacute,Ydieresis" 	k="-20" />
<hkern g1="quoteright,quotedblright" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="133" />
<hkern g1="quoteright,quotedblright" 	g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="123" />
<hkern g1="quoteright,quotedblright" 	g2="d,q" 	k="174" />
<hkern g1="quoteright,quotedblright" 	g2="g" 	k="174" />
<hkern g1="quoteright,quotedblright" 	g2="s" 	k="113" />
<hkern g1="quoteright,quotedblright" 	g2="y,yacute,ydieresis" 	k="-20" />
<hkern g1="quoteright,quotedblright" 	g2="z" 	k="41" />
<hkern g1="quoteright,quotedblright" 	g2="T" 	k="-20" />
<hkern g1="quoteright,quotedblright" 	g2="W" 	k="-20" />
<hkern g1="quoteright,quotedblright" 	g2="f" 	k="10" />
<hkern g1="quoteright,quotedblright" 	g2="u,ugrave,uacute,ucircumflex,udieresis" 	k="20" />
<hkern g1="quoteright,quotedblright" 	g2="V" 	k="-20" />
<hkern g1="quoteright,quotedblright" 	g2="X" 	k="20" />
<hkern g1="quoteright,quotedblright" 	g2="x" 	k="41" />
<hkern g1="quoteright,quotedblright" 	g2="germandbls" 	k="20" />
<hkern g1="quotesinglbase,quotedblbase" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="-31" />
<hkern g1="quotesinglbase,quotedblbase" 	g2="AE" 	k="-20" />
<hkern g1="quotesinglbase,quotedblbase" 	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="102" />
<hkern g1="quotesinglbase,quotedblbase" 	g2="J" 	k="-20" />
<hkern g1="quotesinglbase,quotedblbase" 	g2="S" 	k="31" />
<hkern g1="quotesinglbase,quotedblbase" 	g2="Y,Yacute,Ydieresis" 	k="205" />
<hkern g1="quotesinglbase,quotedblbase" 	g2="Z" 	k="-20" />
<hkern g1="quotesinglbase,quotedblbase" 	g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="51" />
<hkern g1="quotesinglbase,quotedblbase" 	g2="d,q" 	k="41" />
<hkern g1="quotesinglbase,quotedblbase" 	g2="g" 	k="41" />
<hkern g1="quotesinglbase,quotedblbase" 	g2="w" 	k="123" />
<hkern g1="quotesinglbase,quotedblbase" 	g2="y,yacute,ydieresis" 	k="61" />
<hkern g1="quotesinglbase,quotedblbase" 	g2="z" 	k="-20" />
<hkern g1="quotesinglbase,quotedblbase" 	g2="T" 	k="184" />
<hkern g1="quotesinglbase,quotedblbase" 	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	k="51" />
<hkern g1="quotesinglbase,quotedblbase" 	g2="W" 	k="143" />
<hkern g1="quotesinglbase,quotedblbase" 	g2="f" 	k="31" />
<hkern g1="quotesinglbase,quotedblbase" 	g2="t" 	k="82" />
<hkern g1="quotesinglbase,quotedblbase" 	g2="u,ugrave,uacute,ucircumflex,udieresis" 	k="31" />
<hkern g1="quotesinglbase,quotedblbase" 	g2="V" 	k="184" />
<hkern g1="quotesinglbase,quotedblbase" 	g2="j" 	k="-61" />
<hkern g1="quotesinglbase,quotedblbase" 	g2="v" 	k="123" />
<hkern g1="slash" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="164" />
<hkern g1="slash" 	g2="AE" 	k="164" />
<hkern g1="slash" 	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="82" />
<hkern g1="slash" 	g2="J" 	k="184" />
<hkern g1="slash" 	g2="S" 	k="20" />
<hkern g1="slash" 	g2="Y,Yacute,Ydieresis" 	k="-20" />
<hkern g1="slash" 	g2="Z" 	k="20" />
<hkern g1="slash" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="154" />
<hkern g1="slash" 	g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="164" />
<hkern g1="slash" 	g2="d,q" 	k="143" />
<hkern g1="slash" 	g2="g" 	k="143" />
<hkern g1="slash" 	g2="s" 	k="154" />
<hkern g1="slash" 	g2="w" 	k="61" />
<hkern g1="slash" 	g2="y,yacute,ydieresis" 	k="41" />
<hkern g1="slash" 	g2="z" 	k="133" />
<hkern g1="slash" 	g2="T" 	k="-20" />
<hkern g1="slash" 	g2="W" 	k="-20" />
<hkern g1="slash" 	g2="f" 	k="41" />
<hkern g1="slash" 	g2="t" 	k="51" />
<hkern g1="slash" 	g2="u,ugrave,uacute,ucircumflex,udieresis" 	k="102" />
<hkern g1="slash" 	g2="m,n,r,ntilde" 	k="92" />
</font>
</defs></svg> 