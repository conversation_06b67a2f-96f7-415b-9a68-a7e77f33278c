<!DOCTYPE html>
<html lang="en">

<!-- index.html  Tue, 07 Jan 2020 03:35:33 GMT -->
<head>
<meta charset="UTF-8">
<meta content="width=device-width, initial-scale=1, maximum-scale=1, shrink-to-fit=no" name="viewport">
<meta name="csrf-token" content="{{ csrf_token() }}">
<title>Climet</title>

<!-- General CSS Files -->
<link rel="stylesheet" href="{{asset('backend/assets/modules/bootstrap/css/bootstrap.min.css')}}">
<link rel="stylesheet" href="{{asset('backend/assets/modules/fontawesome/css/all.min.css')}}">

<!-- CSS Libraries -->
<link rel="stylesheet" href="{{asset('backend/assets/modules/jqvmap/dist/jqvmap.min.css')}}">
<link rel="stylesheet" href="{{asset('backend/assets/modules/summernote/summernote-bs4.css')}}">
<link rel="stylesheet" href="{{asset('backend/assets/modules/owlcarousel2/dist/assets/owl.carousel.min.css')}}">
<link rel="stylesheet" href="{{asset('backend/ssets/modules/owlcarousel2/dist/assets/owl.theme.default.min.css')}}">

  <!-- Bootstrap 5 CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/css/bootstrap.min.css" rel="stylesheet">


<script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
<!-- Template CSS -->
<link rel="stylesheet" href="{{asset('backend/assets/css/style.min.css')}}">
<link rel="stylesheet" href="{{asset('backend/assets/css/components.min.css')}}">
<!-- Font Awesome CDN -->
<!-- Font Awesome v6 CDN -->
<link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.0/css/all.min.css" rel="stylesheet">
<link rel="icon" href="" type="image/x-icon"/>
<style>
    .dropzone .dz-preview.dz-error .dz-error-message {
        display:none !important;
    }

    </style>
</head>
<body class="layout-4">
<!-- Page Loader -->
<div class="page-loader-wrapper">
    <span class="loader"><span class="loader-inner"></span></span>
</div>

<div id="app">

              <link rel="stylesheet" href="https://stackpath.bootstrapcdn.com/bootstrap/4.5.2/css/bootstrap.min.css" />
    <!-- FontAwesome for icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/4.7.0/css/font-awesome.min.css" />     
                  
    <div class="main-wrapper main-wrapper-1">
        <div class="navbar-bg"></div>

        <!-- Start app top navbar -->


        <!-- Start main left sidebar menu -->
        @include ('backend.left_nav')

        @yield('main')





        <!-- Fullscreen Modal -->
<div class="modal fade" id="fullScreenModal" tabindex="-1" aria-labelledby="fullScreenModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-fullscreen">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="fullScreenModalLabel">Full Screen Modal</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <p>This is a full-screen modal window. You can place forms, images, text, or anything else you need here.</p>
                <p>Try resizing the browser to see how it behaves responsively.</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                <button type="button" class="btn btn-success">Save Changes</button>
            </div>
        </div>
    </div>
</div>

<!-- full screen modal -->


        <!-- Start app Footer part -->
        <footer class="main-footer">
            <div class="footer-left">
                 <div class="bullet"></div>  <a href="https://www.egenslab.com/?verified=true">Egens Lab</a>
            </div>
            <div class="footer-right">

            </div>
        </footer>
    </div>
</div>

<!-- General JS Scripts -->
<script src="{{asset('backend/assets/bundles/lib.vendor.bundle.js')}}"></script>
<script src="{{asset('backend/js/CodiePie.js')}}"></script>

<!-- JS Libraies -->
<script src="{{asset('backend/assets/modules/jquery.sparkline.min.js')}}"></script>
<script src="{{asset('backend/assets/modules/chart.min.js')}}"></script>
<script src="{{asset('backend/assets/modules/owlcarousel2/dist/owl.carousel.min.js')}}"></script>
<script src="{{asset('backend/assets/modules/summernote/summernote-bs4.js')}}"></script>
<script src="{{asset('backend/assets/modules/chocolat/dist/js/jquery.chocolat.min.js')}}"></script>

<!-- Page Specific JS File -->
<script src="{{asset('backend/js/page/index.js')}}"></script>

<!-- Template JS File -->
<script src="{{asset('backend/js/scripts.js')}}"></script>
<script src="{{asset('backend/js/custom.js')}}"></script>
</body>

<!-- index.html  Tue, 07 Jan 2020 03:35:33 GMT -->

<!-- Bootstrap 5 JS Bundle (includes Popper) -->
<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/js/bootstrap.bundle.min.js"></script>




<!--https://unisharp.github.io/laravel-filemanager/installation  -->

<script>
    function initSummernote(selector) {
        $(selector).summernote({
            height: 250,
            callbacks: {
                onImageUpload: function(files) {
                    sendFile(files[0], $(this));
                },
                onInit: function() {
                    let toolbar = $(this).next('.note-editor').find('.note-toolbar');
                    toolbar.append(`
                        <button type="button" class="btn btn-sm btn-light btn-lfm" data-type="Images">
                            <i class="note-icon-picture"></i> Browse
                        </button>
                    `);
                }
            }
        });
    }

    function sendFile(file, $editor) {
        var data = new FormData();
        data.append("upload", file);
        data.append("_token", $('meta[name="csrf-token"]').attr('content'));

        $.ajax({
            url: "/laravel-filemanager/upload?type=Images",
            method: "POST",
            data: data,
            contentType: false,
            processData: false,
            success: function(response) {
                if (response.url) {
                    $editor.summernote('insertImage', response.url);
                } else {
                    alert("Upload failed");
                }
            },
            error: function(jqXHR) {
                console.error("Upload error:", jqXHR);
                alert("Upload error: " + jqXHR.statusText);
            }
        });
    }

    function openLfm(callback, type = 'Images') {
        window.open('/laravel-filemanager?type=' + type, 'FileManager', 'width=900,height=600');
        window.SetUrl = callback;
    }

    $(document).ready(function() {
        const APP_URL = "{{ url('/') }}";

        // ✅ Initialize Summernote for .my-editor class
        $('.my-editor').each(function() {
            initSummernote(this);
        });

        // ✅ Initialize Summernote for .summernote class
        $('.summernote').each(function() {
            initSummernote(this);
        });

        // ✅ Add new repeater block
        $('body').on('click', '.btn-increment', function() {
            let html = $('.clone').html();
            let newElement = $(html);
            $('.image-repeater-wrapper').append(newElement);
            newElement.find('.summernote, .my-editor').each(function() {
                initSummernote(this);
            });
        });

        // ✅ Remove repeater block
        $('body').on('click', '.remove-btn', function() {
            $(this).closest('.control-group').remove();
        });

        // ✅ Handle image upload to Summernote via "Browse"
        $('body').on('click', '.btn-lfm', function() {
            let $note = $(this).closest('.note-editor').prev('.summernote, .my-editor');
            openLfm(function(urls) {
                if (typeof urls === 'string') {
                    $note.summernote('insertImage', urls);
                } else if (Array.isArray(urls)) {
                    urls.forEach(function(item) {
                        $note.summernote('insertImage', item.url);
                    });
                }
            }, 'Images');
        });

        function lfm(id, type, options) {
            let button = document.getElementById(id);
            if (!button) return;

            button.addEventListener('click', function() {
                var route_prefix = (options && options.prefix) ? options.prefix : '/laravel-filemanager';
                var target_input = document.getElementById(button.getAttribute('data-input'));
                var target_preview = document.getElementById(button.getAttribute('data-preview'));

                window.open(route_prefix + '?type=' + (options.type || 'file'), 'FileManager', 'width=900,height=600');

                window.SetUrl = function(items) {
                    var file_path = items.map(item => item.url).join(',');
                    target_input.value = file_path;
                    target_input.dispatchEvent(new Event('change'));

                    if (target_preview) {
                        target_preview.innerHTML = '';
                        items.forEach(item => {
                            let img = document.createElement('img');
                            img.setAttribute('style', 'height: 5rem');
                            img.setAttribute('src', item.thumb_url);
                            target_preview.appendChild(img);
                        });
                        target_preview.dispatchEvent(new Event('change'));
                    }
                };
            });
        }

        // ✅ Initialize LFM picker button
        lfm('lfm', 'image', {
            prefix: '/laravel-filemanager'
        });
    });
</script>

<!-- https://unisharp.github.io/laravel-filemanager/installation -->
</html>
