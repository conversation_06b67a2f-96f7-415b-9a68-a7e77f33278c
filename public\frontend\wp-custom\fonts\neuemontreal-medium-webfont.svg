<?xml version="1.0" standalone="no"?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN" "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd" >
<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" version="1.1">
<metadata></metadata>
<defs>
<font id="neue_montrealmedium" horiz-adv-x="1107" >
<font-face units-per-em="2048" ascent="1583" descent="-465" />
<missing-glyph horiz-adv-x="389" />
<glyph horiz-adv-x="0" />
<glyph horiz-adv-x="682" />
<glyph unicode=" "  horiz-adv-x="389" />
<glyph unicode="&#x09;" horiz-adv-x="389" />
<glyph unicode="&#xa0;" horiz-adv-x="389" />
<glyph unicode="!" horiz-adv-x="503" d="M127 117q0 59 37.5 96t95.5 37q56 0 93.5 -37t37.5 -96q0 -60 -37 -96.5t-94 -36.5q-58 0 -95.5 36.5t-37.5 96.5zM141 1077v387h236v-387l-58 -667h-120z" />
<glyph unicode="&#x22;" horiz-adv-x="706" d="M86 1464h203l-27 -475h-149zM420 1464h203l-27 -475h-150z" />
<glyph unicode="#" horiz-adv-x="1200" d="M59 385l33 176h187l59 297h-203l35 176h203l78 381h176l-78 -381h225l76 381h178l-78 -381h193l-35 -176h-193l-59 -297h211l-33 -176h-215l-78 -385h-176l78 385h-225l-78 -385h-176l78 385h-183zM455 561h223l59 297h-223z" />
<glyph unicode="$" horiz-adv-x="1216" d="M33 467h237q7 -124 80.5 -197t204.5 -92v447l-18 6q-75 21 -129.5 40t-119 53.5t-105 74.5t-68 101.5t-27.5 135.5q0 175 126.5 283.5t340.5 114.5v118h123v-125q190 -26 311 -144.5t131 -309.5h-237q-24 201 -205 248v-400q68 -21 111 -35t99 -37.5t90.5 -45.5t72 -56 t57.5 -72t33.5 -90t13.5 -114q0 -81 -30.5 -152t-89 -125.5t-150.5 -87.5t-207 -37v-155h-123v159q-228 23 -370.5 149.5t-151.5 344.5zM324 1059q0 -78 56.5 -121t174.5 -80v373q-106 -5 -168.5 -51t-62.5 -121zM678 172q120 6 180 57.5t60 137.5q0 85 -55.5 132.5 t-184.5 88.5v-416z" />
<glyph unicode="%" horiz-adv-x="1701" d="M47 1038q0 175 89 291.5t255 116.5q86 0 153.5 -32.5t108.5 -89.5t61.5 -129.5t20.5 -156.5q0 -114 -35.5 -204t-114.5 -146.5t-194 -56.5q-112 0 -191.5 56.5t-116 146.5t-36.5 204zM229 1038q0 -252 162 -252t162 252q0 250 -162 250t-162 -250zM397 0l744 1415h164 l-744 -1415h-164zM967 377q0 114 36.5 204t116 146.5t191.5 56.5q115 0 194 -56.5t114.5 -146.5t35.5 -204q0 -84 -20.5 -156.5t-61.5 -129.5t-108.5 -89.5t-153.5 -32.5q-166 0 -255 116.5t-89 291.5zM1149 377q0 -120 40.5 -185t121.5 -65q162 0 162 250q0 252 -162 252 q-81 0 -121.5 -66t-40.5 -186z" />
<glyph unicode="&#x26;" horiz-adv-x="1273" d="M70 360q0 78 25 144t72.5 117.5t95 87.5t114.5 75q-89 109 -130.5 188t-41.5 173q0 66 23.5 127t68.5 111.5t118.5 80.5t164.5 31q168 1 271 -105.5t103 -244.5q0 -118 -73.5 -203.5t-229.5 -175.5l269 -324q57 119 79 263h203q-31 -241 -147 -426l229 -279h-264l-98 119 q-167 -150 -394 -150q-218 0 -338 109t-120 282zM291 381q0 -88 66.5 -148.5t185.5 -60.5q143 0 254 100l-322 394q-184 -123 -184 -285zM414 1130q0 -56 33 -110t104 -135q100 61 147 115.5t47 129.5q0 70 -43.5 116t-121.5 46q-77 0 -121.5 -46.5t-44.5 -115.5z" />
<glyph unicode="'" horiz-adv-x="374" d="M86 1464h203l-27 -475h-149z" />
<glyph unicode="(" horiz-adv-x="647" d="M137 649q0 269 78.5 502.5t202.5 404.5h188q-110 -212 -171.5 -428t-61.5 -479q0 -257 61.5 -473.5t171.5 -427.5h-188q-123 170 -202 404.5t-79 496.5z" />
<glyph unicode=")" horiz-adv-x="647" d="M43 -252q110 211 171.5 427.5t61.5 473.5q0 263 -61.5 479t-171.5 428h188q124 -171 202.5 -404.5t78.5 -502.5q0 -262 -79 -496.5t-202 -404.5h-188z" />
<glyph unicode="*" horiz-adv-x="798" d="M82 1178l45 127l203 -72v231h141v-231l201 72l45 -127l-205 -64l129 -184l-113 -82l-129 194l-129 -194l-114 82l131 184z" />
<glyph unicode="+" horiz-adv-x="1150" d="M100 569v201h375v387h201v-387h375v-201h-375v-387h-201v387h-375z" />
<glyph unicode="," horiz-adv-x="436" d="M82 113q0 64 37.5 100.5t95.5 36.5q68 0 103.5 -50.5t35.5 -138.5q0 -79 -25 -150.5t-83.5 -127.5t-143.5 -70v117q57 10 91 53.5t34 102.5q-62 -11 -103.5 23t-41.5 104z" />
<glyph unicode="-" horiz-adv-x="698" d="M74 420v201h551v-201h-551z" />
<glyph unicode="." horiz-adv-x="430" d="M82 117q0 59 37.5 96t95.5 37q56 0 93.5 -37t37.5 -96q0 -60 -37 -96.5t-94 -36.5q-58 0 -95.5 36.5t-37.5 96.5z" />
<glyph unicode="/" horiz-adv-x="837" d="M29 0l567 1464h197l-568 -1464h-196z" />
<glyph unicode="0" horiz-adv-x="1292" d="M78 707q0 123 20.5 231t65 202t110.5 161.5t161 106t212 38.5q145 0 256.5 -58.5t179 -161.5t101 -234t33.5 -285t-33.5 -285t-101 -233.5t-179 -161t-256.5 -58.5q-117 0 -212 38.5t-161 105.5t-110.5 161t-65 202t-20.5 231zM313 707q0 -116 17.5 -209t55 -168 t104 -116.5t157.5 -41.5t157 41.5t103.5 116.5t54.5 168t17 209t-17 209t-54.5 168.5t-103.5 117t-157 41.5t-157.5 -41.5t-104 -117t-55 -168.5t-17.5 -209z" />
<glyph unicode="1" horiz-adv-x="747" d="M63 944v170q93 0 159.5 22t104.5 64t56.5 94t24.5 121h190v-1415h-238v1026q-30 -30 -62.5 -47.5t-75 -24.5t-72 -8.5t-87.5 -1.5z" />
<glyph unicode="2" horiz-adv-x="1155" d="M57 0q0 337 367 569q40 27 104 67.5t95.5 61t76 53t66 56.5t44.5 57t31.5 68.5t8.5 77.5q0 47 -12 86.5t-38 73.5t-74 53.5t-114 19.5q-290 0 -290 -330h-230q0 116 32 212t95 168.5t165 112.5t234 40q203 0 336 -114.5t133 -311.5q0 -92 -33.5 -171.5t-89 -137.5 t-123.5 -109.5t-141.5 -97t-138 -89.5t-118 -98t-76.5 -112v-4h706v-201h-1016z" />
<glyph unicode="3" horiz-adv-x="1183" d="M35 485h229q0 -313 330 -313q156 0 225.5 57.5t69.5 173.5q0 238 -287 238h-127v188h119q115 0 180.5 51t65.5 156q0 93 -60.5 150t-181.5 57q-156 0 -225.5 -70.5t-69.5 -185.5h-231q0 206 138.5 332.5t395.5 126.5q127 0 233 -44.5t172 -135t66 -211.5 q0 -213 -213 -303v-4q260 -74 260 -351q0 -205 -139.5 -316.5t-374.5 -111.5q-278 0 -426.5 135t-148.5 381z" />
<glyph unicode="4" horiz-adv-x="1196" d="M49 322v221l670 872h237v-893h199v-200h-199v-322h-237v322h-670zM260 522h459v598z" />
<glyph unicode="5" horiz-adv-x="1165" d="M41 418h235q12 -106 84.5 -176t210.5 -70q135 0 216 80.5t81 230.5q0 141 -75.5 212t-208.5 71q-179 0 -279 -123h-239l153 772h815v-201h-637l-75 -378v-5q120 123 325 123q203 0 331 -132.5t128 -340.5q0 -154 -69 -271.5t-187 -179t-266 -61.5q-249 0 -393 128.5 t-150 320.5z" />
<glyph unicode="6" horiz-adv-x="1216" d="M78 702q0 361 145.5 552.5t413.5 191.5q193 0 321.5 -111.5t151.5 -277.5h-238q-11 87 -70.5 140.5t-166.5 53.5q-74 0 -131 -27.5t-91.5 -72t-56.5 -110.5t-31 -134t-10 -151v-4q53 87 148 135.5t217 48.5q209 0 339 -131t130 -340q0 -214 -139.5 -355t-372.5 -141 q-151 0 -261 54t-174 154.5t-94 230t-30 294.5zM328 449q0 -128 81 -206.5t220 -78.5q135 0 212 79t77 210q0 133 -76.5 210.5t-200.5 77.5q-141 0 -227 -80.5t-86 -211.5z" />
<glyph unicode="7" horiz-adv-x="1071" d="M43 1214v201h999v-180q-239 -199 -388.5 -546t-149.5 -689h-250q0 169 47 353.5t123.5 344t170 293.5t189.5 219v4h-741z" />
<glyph unicode="8" horiz-adv-x="1212" d="M59 399q0 240 263 355v4q-195 103 -195 301q0 179 133.5 283t345.5 104t345.5 -104t133.5 -283q0 -196 -194 -301v-4q264 -117 264 -355q0 -105 -42 -188t-117 -135t-174 -79.5t-216 -27.5t-216 27.5t-173 79.5t-116 135t-42 188zM295 414q0 -116 84 -183t227 -67 q145 0 228.5 66.5t83.5 183.5q0 115 -83.5 181.5t-228.5 66.5q-143 0 -227 -66.5t-84 -181.5zM350 1049q0 -101 67.5 -154t188.5 -53t188.5 53t67.5 154q0 100 -68 154t-188 54t-188 -54t-68 -154z" />
<glyph unicode="9" horiz-adv-x="1210" d="M70 950q0 214 139.5 355t372.5 141q151 0 261 -54t174 -154.5t94 -230t30 -294.5q0 -361 -145.5 -552.5t-413.5 -191.5q-193 0 -321.5 111.5t-151.5 277.5h237q11 -87 71 -140.5t167 -53.5q74 0 130.5 27.5t91.5 72t56.5 110.5t30.5 134t10 151v5q-53 -87 -147.5 -136 t-216.5 -49q-209 0 -339 131t-130 340zM301 963q0 -133 77 -211t200 -78q141 0 227 81t86 212q0 128 -81 206t-220 78q-135 0 -212 -78.5t-77 -209.5z" />
<glyph unicode=":" horiz-adv-x="442" d="M88 117q0 59 37.5 96t95.5 37q56 0 93.5 -37t37.5 -96q0 -60 -37 -96.5t-94 -36.5q-58 0 -95.5 36.5t-37.5 96.5zM88 745q0 59 37.5 96.5t95.5 37.5q56 0 93.5 -37.5t37.5 -96.5q0 -60 -37 -96.5t-94 -36.5q-58 0 -95.5 36.5t-37.5 96.5z" />
<glyph unicode=";" horiz-adv-x="448" d="M88 113q0 64 37.5 100.5t95.5 36.5q68 0 103.5 -50.5t35.5 -138.5q0 -80 -24.5 -151t-83.5 -127t-143 -70v117q56 10 90 53.5t34 102.5q-62 -11 -103.5 23t-41.5 104zM92 745q0 59 37.5 96.5t95.5 37.5q56 0 93.5 -37.5t37.5 -96.5q0 -60 -37 -96.5t-94 -36.5 q-58 0 -95.5 36.5t-37.5 96.5z" />
<glyph unicode="&#x3c;" horiz-adv-x="1085" d="M98 524v224l836 350v-203l-658 -258v-4l658 -256v-201z" />
<glyph unicode="=" horiz-adv-x="1148" d="M106 358v201h941v-201h-941zM106 719v201h941v-201h-941z" />
<glyph unicode="&#x3e;" horiz-adv-x="1040" d="M106 176v201l658 256v4l-658 258v203l836 -350v-224z" />
<glyph unicode="?" horiz-adv-x="1064" d="M33 995q0 108 32.5 198.5t95 158t163 105.5t229.5 38q196 0 326.5 -107.5t130.5 -275.5q0 -64 -15.5 -114.5t-49.5 -92t-66.5 -70t-87.5 -67.5q-46 -33 -70 -52t-55.5 -52.5t-46.5 -65.5t-26 -81t-11 -109h-211q0 155 49.5 252.5t152.5 183.5q21 19 57 46t56.5 44.5 t43 43.5t32.5 58t10 72q0 79 -61 131.5t-164 52.5q-277 0 -277 -297h-237zM344 117q0 59 37.5 96t95.5 37q56 0 93.5 -37t37.5 -96q0 -60 -37 -96.5t-94 -36.5q-58 0 -95.5 36.5t-37.5 96.5z" />
<glyph unicode="@" horiz-adv-x="1820" d="M70 610q0 173 64.5 329t177 271.5t274 183.5t346.5 68q189 0 346 -63.5t260.5 -172.5t160 -254t56.5 -310q0 -93 -15 -170t-42.5 -130.5t-63 -93t-79 -62.5t-87 -33.5t-90.5 -10.5q-97 0 -169.5 43.5t-114.5 122.5h-4q-87 -158 -277 -158q-144 0 -240 114.5t-96 309.5 q0 207 109 336t268 129q97 0 168 -47t100 -119h4l21 151h186l-65 -514q-13 -104 19 -158.5t106 -54.5q93 0 146.5 86t53.5 252q0 185 -78 331.5t-230 232.5t-353 86q-152 0 -283.5 -58t-220.5 -154t-139.5 -222t-50.5 -261q0 -198 86 -351.5t239 -236.5t348 -83 q216 0 367 61l57 -131q-170 -88 -424 -88q-248 0 -438.5 101t-296.5 290t-106 438zM662 584q0 -111 48 -177.5t146 -66.5q114 0 174 85t60 220q0 119 -53.5 181.5t-141.5 62.5q-110 0 -171.5 -84t-61.5 -221z" />
<glyph unicode="A" horiz-adv-x="1357" d="M12 0l539 1464h256l536 -1464h-247l-119 346h-598l-119 -346h-248zM446 549h465l-229 692h-4z" />
<glyph unicode="B" horiz-adv-x="1337" d="M125 0v1464h629q226 0 350.5 -96t124.5 -272q0 -228 -228 -305v-5q125 -22 205 -112.5t80 -245.5q0 -208 -133 -318t-365 -110h-663zM369 207h401q148 0 210 61.5t62 173.5t-64.5 173t-180.5 61h-428v-469zM369 858h376q127 0 183.5 49.5t56.5 149.5q0 97 -58 148.5 t-167 51.5h-391v-399z" />
<glyph unicode="C" horiz-adv-x="1443" d="M70 733q0 164 48.5 304.5t136.5 241.5t218 158.5t285 57.5q136 0 249.5 -41.5t192.5 -114t127.5 -169t61.5 -209.5h-244q-9 48 -25 90.5t-47.5 87t-73 76t-105.5 51.5t-142 20q-92 0 -166.5 -30t-125 -81.5t-84.5 -122.5t-49.5 -150.5t-15.5 -168.5q0 -113 25.5 -210 t76.5 -176t138 -124t201 -45q98 0 173 30t120 82.5t69.5 113t30.5 131.5h244q-25 -254 -191.5 -410t-439.5 -156q-207 0 -364.5 99t-240.5 271.5t-83 393.5z" />
<glyph unicode="D" horiz-adv-x="1415" d="M125 0v1464h569q305 0 478.5 -191.5t173.5 -541.5q0 -351 -170 -541t-476 -190h-575zM369 207h313q420 0 420 524q0 526 -420 526h-313v-1050z" />
<glyph unicode="E" horiz-adv-x="1277" d="M125 0v1464h1059v-207h-815v-397h714v-209h-714v-444h835v-207h-1079z" />
<glyph unicode="F" horiz-adv-x="1200" d="M125 0v1464h1024v-207h-780v-426h632v-208h-632v-623h-244z" />
<glyph unicode="G" horiz-adv-x="1515" d="M70 733q0 164 48.5 304.5t136.5 241.5t218 158.5t285 57.5q275 0 435.5 -133t195.5 -354h-244q-28 132 -128 205t-261 73q-91 0 -166 -31.5t-125.5 -84.5t-85.5 -124.5t-50.5 -150t-15.5 -162.5q0 -87 13 -164t44.5 -150t80 -125.5t124.5 -84t173 -31.5q176 0 296 110.5 t121 282.5h-413v197h661v-768h-168l-53 201h-4q-76 -115 -186.5 -173.5t-270.5 -58.5q-156 0 -281.5 57t-208.5 159t-127 242t-44 306z" />
<glyph unicode="H" horiz-adv-x="1429" d="M125 0v1464h244v-606h692v606h244v-1464h-244v649h-692v-649h-244z" />
<glyph unicode="I" horiz-adv-x="499" d="M129 0v1464h244v-1464h-244z" />
<glyph unicode="J" horiz-adv-x="1048" d="M39 459v67h231v-67q0 -54 4.5 -93t17.5 -76.5t36 -60.5t62 -37t93 -14q66 0 110 20t66 60t30.5 87t8.5 114v1005h242v-1005q0 -80 -13 -148.5t-45 -132.5t-82 -109.5t-129.5 -72.5t-181.5 -27t-181.5 27t-129.5 72.5t-82 109.5t-44.5 132.5t-12.5 148.5z" />
<glyph unicode="K" horiz-adv-x="1353" d="M125 0v1464h244v-723l663 723h291l-569 -620l604 -844h-285l-479 674l-225 -242v-432h-244z" />
<glyph unicode="L" horiz-adv-x="1142" d="M125 0v1464h244v-1257h731v-207h-975z" />
<glyph unicode="M" horiz-adv-x="1701" d="M125 0v1464h315l410 -1126h4l408 1126h315v-1464h-238v1110h-4l-387 -1110h-194l-387 1110h-5v-1110h-237z" />
<glyph unicode="N" horiz-adv-x="1429" d="M125 0v1464h272l660 -1099h4v1099h244v-1464h-271l-661 1096h-4v-1096h-244z" />
<glyph unicode="O" horiz-adv-x="1576" d="M70 733q0 220 86.5 392t250.5 271t381 99q162 0 297.5 -57.5t227.5 -158.5t143 -241.5t51 -304.5t-51 -305t-143 -242.5t-227.5 -159t-297.5 -57.5q-217 0 -381 99.5t-250.5 272t-86.5 392.5zM311 733q0 -115 28 -213t84 -175.5t149.5 -122t215.5 -44.5t215.5 44.5 t149 122.5t83.5 175.5t28 212.5q0 114 -28 211.5t-83.5 175t-149 122t-215.5 44.5t-215.5 -44.5t-149.5 -122t-84 -175t-28 -211.5z" />
<glyph unicode="P" horiz-adv-x="1267" d="M125 0v1464h623q108 0 194.5 -25.5t152 -77.5t101 -137.5t35.5 -197.5q0 -149 -63 -249.5t-169 -145.5t-251 -45h-379v-586h-244zM369 795h368q125 0 187.5 55.5t62.5 175.5q0 118 -62.5 174.5t-187.5 56.5h-368v-462z" />
<glyph unicode="Q" horiz-adv-x="1576" d="M70 733q0 220 86.5 392t250.5 271t381 99q162 0 297.5 -57.5t227.5 -158.5t143 -241.5t51 -304.5q0 -306 -166 -514l164 -164l-143 -143l-170 170q-174 -113 -404 -113q-217 0 -381 99.5t-250.5 272t-86.5 392.5zM311 733q0 -115 28 -213t84 -175.5t149.5 -122 t215.5 -44.5q140 0 248 62l-198 198l143 144l197 -199q86 140 86 350q0 114 -28 211.5t-83.5 175t-149 122t-215.5 44.5t-215.5 -44.5t-149.5 -122t-84 -175t-28 -211.5z" />
<glyph unicode="R" horiz-adv-x="1320" d="M125 0v1464h625q228 0 361.5 -111t133.5 -309q0 -150 -71 -241t-201 -123v-4q80 -15 133.5 -69t75.5 -125t34.5 -151.5t14 -148t10 -115t25.5 -53.5v-14h-256q-14 12 -20 62q-5 42 -5 93v19v10q0 57 -10 121q-11 70 -33 127t-77 95t-138 38h-358v-565h-244zM369 774h329 q51 0 90 4.5t81.5 19.5t70.5 40.5t46 70.5t18 107q0 61 -18 106t-46 70.5t-70 40.5t-81.5 19.5t-90.5 4.5h-329v-483z" />
<glyph unicode="S" horiz-adv-x="1296" d="M41 487h244q9 -145 109.5 -227t291.5 -82q153 0 227 58.5t74 150.5q0 26 -3 48t-12.5 41t-18.5 34t-29 29.5t-35 25t-46 22.5t-52.5 19.5t-64 19.5t-71 19.5t-82.5 20.5q-56 14 -99 27t-95 33.5t-90.5 42.5t-76.5 54.5t-62 70t-39 88t-15 108.5q0 193 139 299t381 106 q247 0 404 -123.5t172 -345.5h-244q-35 260 -332 260q-131 0 -204.5 -46t-73.5 -128q0 -106 90 -158q56 -33 215 -73h1h1l1 -1h1h1h1l1 -1h1h1h1l1 -1h1h1h1l1 -1h1h1h2l1 -1h1h1h1l1 -1h1h1h1l1 -1h1h1h1l1 -1h1h1h1l1 -1h1q118 -30 180.5 -49t145.5 -57.5t124 -83t71 -117 t30 -168.5q0 -185 -140.5 -306.5t-404.5 -121.5q-180 0 -322.5 59.5t-229 178t-93.5 280.5z" />
<glyph unicode="T" horiz-adv-x="1261" d="M41 1257v207h1180v-207h-467v-1257h-244v1257h-469z" />
<glyph unicode="U" horiz-adv-x="1366" d="M104 500v964h244v-964q0 -165 83 -243.5t251 -78.5q170 0 253 78.5t83 243.5v964h242v-964q0 -250 -141.5 -390.5t-436.5 -140.5t-436.5 140.5t-141.5 390.5z" />
<glyph unicode="V" horiz-adv-x="1288" d="M12 1464h258l373 -1198h2l373 1198h258l-500 -1464h-264z" />
<glyph unicode="W" horiz-adv-x="1888" d="M20 1464h250l250 -1134h4l295 1134h250l297 -1134h4l250 1134h250l-367 -1464h-256l-301 1147h-4l-299 -1147h-258z" />
<glyph unicode="X" horiz-adv-x="1310" d="M20 0l496 748l-459 716h265l331 -540h2l334 540h264l-458 -712l495 -752h-284l-351 571h-2l-348 -571h-285z" />
<glyph unicode="Y" horiz-adv-x="1312" d="M14 1464h260l379 -680h4l381 680h260l-520 -882v-582h-243v582z" />
<glyph unicode="Z" horiz-adv-x="1265" d="M59 0v207l822 1046v4h-781v207h1078v-207l-822 -1046v-4h842v-207h-1139z" />
<glyph unicode="[" horiz-adv-x="673" d="M145 -252v1808h465v-200h-227v-1407h227v-201h-465z" />
<glyph unicode="\" horiz-adv-x="827" d="M31 1464h196l568 -1464h-197z" />
<glyph unicode="]" horiz-adv-x="673" d="M63 -51h228v1407h-228v200h465v-1808h-465v201z" />
<glyph unicode="^" horiz-adv-x="935" d="M18 901l357 563h188l355 -563h-224l-225 371h-2l-223 -371h-226z" />
<glyph unicode="_" horiz-adv-x="849" d="M-4 -156h858v-200h-858v200z" />
<glyph unicode="`" horiz-adv-x="1335" d="M248 1481h250l88 -269h-168z" />
<glyph unicode="a" horiz-adv-x="1073" d="M43 268q0 67 20.5 119.5t55.5 89t91.5 64.5t118 45.5t146.5 34.5q43 8 66 13t55.5 13t49.5 15t37 18t29.5 23.5t16 30t6.5 38.5q0 133 -188 133q-128 0 -183 -43t-63 -145h-209q8 162 119.5 260t335.5 98q403 0 403 -366v-498q0 -59 20 -85q17 -21 60 -21q10 0 21 1v-102 q-60 -22 -125 -22q-86 0 -130 33t-59 110h-4q-52 -72 -140.5 -114t-205.5 -42q-158 0 -251 82.5t-93 216.5zM260 289q0 -150 174 -150q137 0 219 67t82 191v158q-51 -36 -219 -70q-137 -30 -196.5 -74t-59.5 -122z" />
<glyph unicode="b" horiz-adv-x="1155" d="M102 0v1464h215v-544h5q47 68 126.5 111.5t192.5 43.5q205 0 336 -150t131 -403t-131 -403t-336 -150q-111 0 -190.5 44t-128.5 108h-5v-121h-215zM319 522q0 -174 76.5 -275.5t208.5 -101.5q136 0 212.5 108t76.5 269t-76.5 269t-212.5 108q-132 0 -208.5 -101 t-76.5 -276z" />
<glyph unicode="c" horiz-adv-x="1077" d="M47 522q0 247 141 400t373 153q200 0 328 -114.5t149 -296.5h-215q-3 43 -18.5 82t-45 74.5t-80 57t-116.5 21.5q-76 0 -135.5 -31t-94.5 -84t-53 -119.5t-18 -142.5t17.5 -142.5t52.5 -119.5t94.5 -84t136.5 -31q66 0 116.5 21t80 57.5t45 77.5t18.5 88h215 q-12 -184 -143 -302t-332 -118q-234 0 -375 153t-141 400z" />
<glyph unicode="d" horiz-adv-x="1155" d="M47 522q0 253 131 403t336 150q113 0 193.5 -43.5t126.5 -111.5h4v544h215v-1464h-215v121h-4q-49 -64 -129 -108t-191 -44q-205 0 -336 150t-131 403zM262 522q0 -161 76.5 -269t212.5 -108q132 0 208.5 101.5t76.5 275.5q0 175 -76.5 276t-208.5 101 q-136 0 -212.5 -108t-76.5 -269z" />
<glyph unicode="e" horiz-adv-x="1110" d="M47 522q0 255 139 404t375 149q248 0 376 -161t128 -447h-803q0 -137 82.5 -229.5t220.5 -92.5q63 0 114 18t81.5 46t48 55.5t25.5 54.5h215q-42 -158 -164 -254t-314 -96q-242 0 -383 152t-141 401zM262 627h588q0 122 -79.5 200t-209.5 78q-131 0 -215 -78.5 t-84 -199.5z" />
<glyph unicode="f" horiz-adv-x="636" d="M37 870v177h149v133q0 161 66.5 230t200.5 69q62 0 143 -15v-176q-19 1 -37 1q-81 0 -115 -19q-41 -24 -41 -109v-114h193v-177h-193v-870h-217v870h-149z" />
<glyph unicode="g" horiz-adv-x="1144" d="M47 563q0 242 128 377t331 135q205 0 317 -151h4v123h215v-975q0 -220 -125 -327t-354 -107q-215 0 -343 96t-146 237h217q35 -161 264 -161q137 0 204.5 65.5t67.5 204.5v127h-4q-110 -156 -317 -156q-203 0 -331 135.5t-128 376.5zM262 563q0 -150 74 -243t211 -93 q132 0 205 86t73 250t-73 250t-205 86q-137 0 -211 -92.5t-74 -243.5z" />
<glyph unicode="h" d="M102 0v1464h215v-553h5q47 70 132.5 117t207.5 47q157 0 254.5 -89.5t97.5 -262.5v-723h-217v662q0 231 -209 231q-120 0 -195.5 -70t-75.5 -186v-637h-215z" />
<glyph unicode="i" horiz-adv-x="448" d="M102 1337q0 52 36.5 82.5t84.5 30.5q51 0 87 -30t36 -83t-36.5 -83.5t-86.5 -30.5q-48 0 -84.5 31t-36.5 83zM117 0v1047h215v-1047h-215z" />
<glyph unicode="j" horiz-adv-x="438" d="M-70 -170q42 -2 72 -2h14q34 1 54 14t28 37.5t8 69.5v1098h216v-1096q0 -153 -50.5 -230t-173.5 -77q-75 0 -168 22v164zM92 1337q0 52 36.5 82.5t84.5 30.5q49 0 86 -30t37 -83t-37 -83.5t-86 -30.5q-48 0 -84.5 31t-36.5 83z" />
<glyph unicode="k" horiz-adv-x="1048" d="M102 0v1464h215v-837l426 420h265l-398 -390l441 -657h-258l-330 516l-146 -137v-379h-215z" />
<glyph unicode="l" horiz-adv-x="430" d="M106 0v1464h218v-1464h-218z" />
<glyph unicode="m" horiz-adv-x="1673" d="M102 0v1047h215v-136h5q110 164 311 164q210 0 278 -180h4q140 180 342 180q157 0 238.5 -91t81.5 -261v-723h-217v662q0 117 -36.5 174t-139.5 57q-109 0 -172.5 -69.5t-63.5 -186.5v-637h-217v662q0 117 -36.5 174t-141.5 57q-107 0 -171.5 -69.5t-64.5 -186.5v-637 h-215z" />
<glyph unicode="n" d="M102 0v1047h215v-136h5q47 70 133.5 117t210.5 47q154 0 251 -89.5t97 -262.5v-723h-217v662q0 231 -207 231q-125 0 -199 -69.5t-74 -186.5v-637h-215z" />
<glyph unicode="o" horiz-adv-x="1146" d="M47 522q0 -248 141.5 -400.5t384.5 -152.5t385 152.5t142 400.5t-142 400.5t-385 152.5t-384.5 -152.5t-141.5 -400.5zM262 522q0 169 81 273t230 104t230.5 -104.5t81.5 -272.5q0 -167 -81.5 -272t-230.5 -105t-230 105t-81 272z" />
<glyph unicode="p" horiz-adv-x="1163" d="M102 -356v1403h217v-123h5q48 65 128 108t193 43q204 0 334.5 -150t130.5 -403t-130.5 -403t-334.5 -150q-113 0 -193.5 44.5t-127.5 111.5h-5v-481h-217zM322 522q0 -174 76 -275.5t206 -101.5q136 0 212.5 108t76.5 269t-76.5 269t-212.5 108q-131 0 -206.5 -101 t-75.5 -276z" />
<glyph unicode="q" horiz-adv-x="1163" d="M53 522q0 253 130.5 403t334.5 150q113 0 193.5 -43t128.5 -108h4v123h217v-1403h-217v481h-4q-47 -67 -128 -111.5t-194 -44.5q-204 0 -334.5 150t-130.5 403zM270 522q0 -161 76.5 -269t212.5 -108q130 0 206.5 102t76.5 275q0 175 -76 276t-207 101 q-136 0 -212.5 -108t-76.5 -269z" />
<glyph unicode="r" horiz-adv-x="729" d="M102 0v1047h215v-197h5q93 221 278 221q48 0 94 -6v-203h-4q-34 6 -66 6q-114 0 -198 -74q-108 -94 -109 -259v-535h-215z" />
<glyph unicode="s" horiz-adv-x="997" d="M37 348h209q4 -47 17.5 -81t43 -65t84.5 -47t135 -16q104 0 158.5 42t54.5 98q0 21 -4 39t-14 32t-19.5 25t-28 20t-32.5 15.5t-40 13.5t-42.5 11t-48 11l-49.5 11q-143 34 -179 46q-157 53 -197 166q-15 43 -15 97q0 133 111 221t302 88q203 0 313.5 -95t121.5 -241 h-209q-6 35 -18 61.5t-36 52t-67 39t-103 13.5q-95 0 -146.5 -33t-51.5 -90q0 -22 6 -39.5t21.5 -30.5t29 -22t43.5 -17.5t47.5 -13t58.5 -13t62 -13.5q161 -38 225 -67q147 -67 171 -208q5 -30 5 -63q0 -150 -117 -238t-309 -88q-243 0 -364 102t-129 277z" />
<glyph unicode="t" horiz-adv-x="655" d="M37 870v177h151v329h218v-329h196v-177h-196v-567q0 -44 11 -70.5t35 -39.5t61 -15q17 -1 37 -2q24 0 52 2v-176q-69 -16 -147 -16q-134 0 -200.5 69t-66.5 230v585h-151z" />
<glyph unicode="u" d="M94 324v723h217v-662q0 -231 207 -231q125 0 199 69.5t74 186.5v637h215v-1047h-215v135h-5q-47 -70 -133.5 -117t-210.5 -47q-154 0 -251 90t-97 263z" />
<glyph unicode="v" horiz-adv-x="985" d="M0 1047h227l265 -820h2l264 820h227l-371 -1047h-243z" />
<glyph unicode="w" horiz-adv-x="1536" d="M4 1047h227l213 -795h5l213 795h210l215 -795h5l211 795h227l-320 -1047h-217l-225 799h-4l-223 -799h-215z" />
<glyph unicode="x" horiz-adv-x="1067" d="M12 0l391 535l-354 512h244l237 -369h5l237 369h244l-352 -512l391 -535h-250l-270 393h-5l-266 -393h-252z" />
<glyph unicode="y" horiz-adv-x="1028" d="M0 1047h231l293 -801h4l269 801h229l-418 -1113q-20 -55 -37.5 -93t-45 -78.5t-59.5 -64t-79 -39t-106 -15.5q-72 0 -154 20v164h18q33 -6 62.5 -6t56.5 6q53 11 86 51q21 26 34 58t13 77.5t-18 91.5z" />
<glyph unicode="z" horiz-adv-x="987" d="M63 0v160l564 706v4h-527v177h815v-144l-575 -723v-4h598v-176h-875z" />
<glyph unicode="{" horiz-adv-x="673" d="M49 553v201q73 -1 100 43t27 131v231q0 102 16.5 173t45 115t79 68t107.5 32.5t141 8.5h45v-200h-45q-75 0 -114 -42.5t-39 -148.5v-291q0 -84 -32.5 -135t-94.5 -84v-4q63 -33 95 -84.5t32 -136.5v-283q0 -112 39 -155t114 -43h45v-201h-45q-69 0 -119.5 6t-96.5 22.5 t-76.5 45.5t-53 74t-33 108.5t-10.5 149.5v223q0 86 -27 131t-100 45z" />
<glyph unicode="|" horiz-adv-x="458" d="M145 -268v1798h170v-1798h-170z" />
<glyph unicode="}" horiz-adv-x="673" d="M63 -51h46q75 0 114 43t39 155v283q0 85 32 136.5t95 84.5v4q-62 33 -94.5 84t-32.5 135v291q0 106 -39 148.5t-114 42.5h-46v200h46q84 0 141 -8.5t107.5 -32.5t79 -68t45 -115t16.5 -173v-231q0 -87 27 -131t100 -43v-201q-73 0 -100 -45t-27 -131v-223 q0 -86 -10.5 -149.5t-33 -108.5t-53 -74t-76.5 -45.5t-96.5 -22.5t-119.5 -6h-46v201z" />
<glyph unicode="~" horiz-adv-x="837" d="M66 532q0 131 52.5 205t149.5 74q54 0 104.5 -22t91.5 -44t71 -22q41 0 59 26.5t18 61.5h164q0 -130 -53 -204.5t-150 -74.5q-53 0 -103.5 23t-91 45.5t-69.5 22.5q-43 0 -61.5 -27.5t-18.5 -63.5h-163z" />
<glyph unicode="&#xa1;" horiz-adv-x="518" d="M127 1081q0 60 37.5 96.5t95.5 36.5q57 0 94 -36.5t37 -96.5q0 -59 -37.5 -96t-93.5 -37q-58 0 -95.5 37t-37.5 96zM141 121l58 667h120l58 -667v-387h-236v387z" />
<glyph unicode="&#xa2;" horiz-adv-x="1079" d="M47 522q0 228 120.5 377t324.5 172v141h122v-139q178 -15 291.5 -127.5t132.5 -281.5h-215q-7 84 -58 150.5t-151 80.5v-745q100 14 151 82t58 157h215q-11 -171 -127 -287t-297 -131v-159h-122v161q-204 25 -324.5 174t-120.5 375zM262 522q0 -139 57.5 -241t172.5 -127 v737q-77 -18 -130 -74t-76.5 -131t-23.5 -164z" />
<glyph unicode="&#xa3;" horiz-adv-x="1150" d="M57 172q85 55 138 157t53 222q0 21 -2 31h-170v170h108q-23 48 -35.5 77.5t-25 83t-12.5 107.5q0 55 14.5 110t51 110.5t90 98t139.5 69t192 26.5q129 0 226.5 -37t156.5 -104.5t88 -156.5t29 -198h-238q0 293 -260 293q-124 0 -189 -63.5t-65 -168.5q0 -18 2 -36.5 t4.5 -32.5t9 -33t9.5 -29t12.5 -31t12.5 -27.5t14.5 -29.5t13.5 -28h313v-170h-256q2 -12 2 -41q0 -206 -164 -324l3 -4q4 2 23.5 12t27.5 13.5t27 11t33 11t33.5 6.5t39.5 3q58 0 108 -22t96.5 -44t94.5 -22q65 0 114 22.5t111 69.5l92 -180q-45 -34 -67 -49t-62 -37 t-80 -30.5t-88 -8.5q-63 0 -121 20t-94.5 43.5t-84.5 43.5t-93 20q-62 0 -116.5 -21.5t-137.5 -74.5z" />
<glyph unicode="&#xa4;" horiz-adv-x="1011" d="M35 352l129 129q-62 97 -62 228q0 130 62 227l-129 129l115 115l129 -127q100 65 227 65q129 0 227 -67l127 127l119 -113l-131 -131q61 -95 61 -225q0 -131 -61 -226l131 -131l-117 -114l-127 127q-108 -68 -229 -68q-127 0 -230 68l-126 -127zM291 709q0 -122 58 -189 t157 -67t157 67t58 189t-58 188t-157 66t-157 -66t-58 -188z" />
<glyph unicode="&#xa5;" horiz-adv-x="1206" d="M14 1405h260l326 -637h4l326 637h258l-436 -780h319v-123h-350v-117h350v-123h-350v-262h-238v262h-352v123h352v117h-352v123h322z" />
<glyph unicode="&#xa6;" horiz-adv-x="458" d="M145 -268v770h170v-770h-170zM145 786v744h170v-744h-170z" />
<glyph unicode="&#xa7;" horiz-adv-x="1220" d="M57 653q0 107 59.5 179.5t172.5 103.5q-92 105 -92 240q0 141 105 230t304 89q108 0 191 -28.5t132 -78.5t75 -111t30 -134h-217q-3 31 -7.5 52.5t-18 48t-35 42.5t-60.5 27.5t-92 11.5q-192 0 -192 -131q0 -21 5.5 -41t20 -40t26 -35t37.5 -35t39 -31t47.5 -31t45 -26.5 t48.5 -27.5t44 -24q191 -104 229 -128q143 -90 186 -181q25 -52 25 -113q0 -109 -59.5 -181.5t-169.5 -102.5q90 -107 90 -238q0 -140 -105.5 -229.5t-304.5 -89.5q-108 0 -191 28.5t-132 78.5t-75 111t-30 134h218q3 -31 7.5 -52.5t18 -48t34.5 -42.5t60 -27.5t92 -11.5 q193 0 193 131q0 23 -9 47t-21 42.5t-36.5 40.5t-41.5 35.5t-51.5 35.5t-50 31t-54.5 30.5t-49 27.5q-32 18 -87 47t-84 45t-73 43t-67.5 46t-52.5 47.5t-43 56t-24 62.5t-10 75zM274 692q0 -22 7.5 -42.5t17.5 -36.5t32 -34.5t39.5 -31t52.5 -32t57.5 -31.5t68 -35 t72.5 -37q14 -8 52.5 -29t68 -38t55.5 -34q80 15 115.5 47t35.5 84q0 36 -22.5 69.5t-70 67t-99.5 62.5t-134 72q-13 6 -20 10q-17 9 -53.5 29t-66.5 37t-56 34q-80 -15 -116 -47t-36 -84z" />
<glyph unicode="&#xa8;" horiz-adv-x="1028" d="M219 1364q0 52 32.5 86.5t88.5 34.5q55 0 87 -34.5t32 -86.5q0 -48 -32 -82.5t-87 -34.5q-56 0 -88.5 34.5t-32.5 82.5zM569 1364q0 52 32.5 86.5t88.5 34.5q55 0 87 -34.5t32 -86.5q0 -48 -32 -82.5t-87 -34.5q-56 0 -88.5 34.5t-32.5 82.5z" />
<glyph unicode="&#xa9;" horiz-adv-x="1673" d="M82 731q0 163 53.5 304t150.5 243.5t239.5 160.5t312.5 58t312.5 -58t238.5 -160.5t149 -243t53 -304.5q0 -163 -53 -304t-149.5 -242.5t-238.5 -159.5t-312 -58t-312.5 58t-239.5 159.5t-150.5 242.5t-53.5 304zM190 731q0 -140 45.5 -263.5t127.5 -215t205 -144.5 t270 -53q194 0 342.5 91t225.5 244t77 341q0 189 -77 342.5t-225.5 244.5t-342.5 91q-147 0 -270 -53t-205 -144.5t-127.5 -215.5t-45.5 -265zM418 731q0 206 110.5 333.5t305.5 127.5q172 0 278.5 -95t122.5 -241h-182q-3 20 -9.5 39.5t-22 45t-37.5 44.5t-59.5 32 t-84.5 13q-238 0 -238 -299q0 -297 238 -297q58 0 100.5 18t65 47.5t33 56t14.5 54.5h182q-9 -146 -112.5 -242t-272.5 -96q-202 0 -317 126.5t-115 332.5z" />
<glyph unicode="&#xab;" horiz-adv-x="800" d="M61 403v226l291 252v-209l-182 -154v-4l182 -154v-208zM446 403v226l293 252v-209l-184 -154v-4l184 -154v-208z" />
<glyph unicode="&#xac;" horiz-adv-x="1122" d="M115 635v201h856v-547h-230v346h-626z" />
<glyph unicode="&#xad;" horiz-adv-x="698" d="M74 420v201h551v-201h-551z" />
<glyph unicode="&#xae;" horiz-adv-x="1083" d="M53 1001q0 212 141.5 354t348.5 142q205 0 346 -142t141 -354t-141 -353.5t-346 -141.5q-207 0 -348.5 141.5t-141.5 353.5zM147 1001q0 -179 112.5 -293t283.5 -114q170 0 281.5 114t111.5 293q0 178 -112 293t-281 115q-171 0 -283.5 -115t-112.5 -293zM350 741v529 h225q83 0 132 -40.5t49 -111.5q0 -54 -26.5 -87t-72.5 -44v-2q41 -8 63 -45.5t24.5 -78.5t5.5 -76t12 -37v-7h-109q-6 6 -7.5 41t-4 70.5t-27.5 64.5t-73 29h-88v-205h-103zM453 1034h90q25 0 40.5 2t33 8.5t26 22.5t8.5 41q0 20 -6.5 34t-15 21.5t-25 11.5t-28.5 4.5 t-33 0.5h-90v-146z" />
<glyph unicode="&#xaf;" horiz-adv-x="1024" d="M240 1270v164h546v-164h-546z" />
<glyph unicode="&#xb0;" horiz-adv-x="757" d="M68 1186q0 56 20 110t57.5 99t98.5 72.5t135 27.5t134.5 -27.5t98 -72.5t58 -99t20.5 -110q0 -77 -34 -146.5t-107 -117.5t-170 -48q-98 0 -171 48t-106.5 117.5t-33.5 146.5zM211 1186q0 -79 47 -126.5t121 -47.5q75 0 122.5 48t47.5 126q0 77 -48 124.5t-122 47.5 t-121 -47.5t-47 -124.5z" />
<glyph unicode="&#xb1;" horiz-adv-x="1150" d="M100 0v176h951v-176h-951zM100 627v200h375v330h201v-330h375v-200h-375v-330h-201v330h-375z" />
<glyph unicode="&#xb4;" horiz-adv-x="991" d="M406 1212l88 269h249l-170 -269h-167z" />
<glyph unicode="&#xb6;" horiz-adv-x="1159" d="M57 1069q0 187 104 291t300 104h569v-1464h-162v1294h-153v-1294h-162v686h-92q-197 0 -300.5 99t-103.5 284z" />
<glyph unicode="&#xb7;" horiz-adv-x="430" d="M86 635q0 59 37.5 96t95.5 37q56 0 93.5 -37t37.5 -96q0 -60 -37 -96.5t-94 -36.5q-58 0 -95.5 36.5t-37.5 96.5z" />
<glyph unicode="&#xb8;" horiz-adv-x="1028" d="M244 -344l49 59q75 -30 149 -30q45 0 76 19t31 50q0 56 -96 56q-41 0 -78 -15l-29 43l117 146h108l-82 -101v-4q77 10 142 -22t65 -105q0 -71 -67.5 -113t-161.5 -42q-137 0 -223 59z" />
<glyph unicode="&#xbb;" horiz-adv-x="800" d="M61 152v208l185 154v4l-185 154v209l293 -252v-226zM449 152v208l182 154v4l-182 154v209l290 -252v-226z" />
<glyph unicode="&#xbf;" horiz-adv-x="1060" d="M51 86q0 64 15.5 114.5t49.5 92t66.5 70t87.5 67.5q46 33 70 52.5t55.5 52.5t46.5 65.5t26 81.5t11 109h211q0 -155 -49.5 -253t-153.5 -184q-21 -19 -57 -46t-56 -44.5t-42.5 -43.5t-32.5 -58t-10 -72q0 -79 61 -131.5t164 -52.5q277 0 277 297h237q0 -108 -32.5 -198.5 t-95 -158t-163 -105.5t-229.5 -38q-196 0 -326.5 107.5t-130.5 275.5zM453 1081q0 60 37 96.5t94 36.5q58 0 95.5 -36.5t37.5 -96.5q0 -59 -37.5 -96t-95.5 -37q-56 0 -93.5 37t-37.5 96z" />
<glyph unicode="&#xc0;" horiz-adv-x="1357" d="M12 0l539 1464h256l536 -1464h-247l-119 346h-598l-119 -346h-248zM428 1898h250l88 -268h-168zM446 549h465l-229 692h-4z" />
<glyph unicode="&#xc1;" horiz-adv-x="1357" d="M12 0l539 1464h256l536 -1464h-247l-119 346h-598l-119 -346h-248zM446 549h465l-229 692h-4zM592 1630l88 268h250l-170 -268h-168z" />
<glyph unicode="&#xc2;" horiz-adv-x="1357" d="M12 0l539 1464h256l536 -1464h-247l-119 346h-598l-119 -346h-248zM369 1632l198 269h228l198 -269h-202l-109 162h-4l-107 -162h-202zM446 549h465l-229 692h-4z" />
<glyph unicode="&#xc3;" horiz-adv-x="1357" d="M12 0l539 1464h256l536 -1464h-247l-119 346h-598l-119 -346h-248zM377 1675q0 111 44.5 165t115.5 54q54 0 141 -37.5t113 -37.5q53 0 53 71h139q0 -113 -44.5 -166t-117.5 -53q-53 0 -140 38t-114 38q-53 0 -53 -72h-137zM446 549h465l-229 692h-4z" />
<glyph unicode="&#xc4;" horiz-adv-x="1357" d="M12 0l539 1464h256l536 -1464h-247l-119 346h-598l-119 -346h-248zM385 1782q0 52 32.5 86.5t88.5 34.5q55 0 87 -34.5t32 -86.5q0 -48 -32 -82.5t-87 -34.5q-56 0 -88.5 34.5t-32.5 82.5zM446 549h465l-229 692h-4zM735 1782q0 52 32.5 86.5t88.5 34.5q55 0 87 -34.5 t32 -86.5q0 -48 -32 -82.5t-87 -34.5q-56 0 -88.5 34.5t-32.5 82.5z" />
<glyph unicode="&#xc5;" horiz-adv-x="1357" d="M12 0l539 1464h256l536 -1464h-247l-119 346h-598l-119 -346h-248zM446 549h465l-229 692h-4zM492 1790q0 82 55 135t133 53t133 -53t55 -135q0 -84 -54.5 -135t-133.5 -51t-133.5 51t-54.5 135zM588 1790q0 -43 27 -69.5t65 -26.5t65 26.5t27 69.5t-27 69.5t-65 26.5 t-65 -26.5t-27 -69.5z" />
<glyph unicode="&#xc6;" horiz-adv-x="2035" d="M18 -2l545 1466h1379v-207h-816v-397h715v-209h-715v-444h836v-207h-1079v346h-498l-119 -348h-248zM453 547h430v706h-193z" />
<glyph unicode="&#xc7;" horiz-adv-x="1443" d="M70 733q0 164 48.5 304.5t136.5 241.5t218 158.5t285 57.5q136 0 249.5 -41.5t192.5 -114t127.5 -169t61.5 -209.5h-244q-9 48 -25 90.5t-47.5 87t-73 76t-105.5 51.5t-142 20q-92 0 -166.5 -30t-125 -81.5t-84.5 -122.5t-49.5 -150.5t-15.5 -168.5q0 -113 25.5 -210 t76.5 -176t138 -124t201 -45q98 0 173 30t120 82.5t69.5 113t30.5 131.5h244q-24 -242 -177 -396.5t-405 -167.5l-72 -88v-4q77 10 142 -22t65 -105q0 -71 -67.5 -113t-161.5 -42q-138 0 -224 59l50 59q75 -30 149 -30q45 0 76 19t31 50q0 56 -97 56q-40 0 -77 -15l-29 43 l106 133q-287 22 -457.5 232t-170.5 530z" />
<glyph unicode="&#xc8;" horiz-adv-x="1277" d="M125 0v1464h1059v-207h-815v-397h714v-209h-714v-444h835v-207h-1079zM414 1898h250l88 -268h-168z" />
<glyph unicode="&#xc9;" horiz-adv-x="1277" d="M125 0v1464h1059v-207h-815v-397h714v-209h-714v-444h835v-207h-1079zM578 1630l88 268h249l-170 -268h-167z" />
<glyph unicode="&#xca;" horiz-adv-x="1277" d="M125 0v1464h1059v-207h-815v-397h714v-209h-714v-444h835v-207h-1079zM354 1632l199 269h227l199 -269h-203l-108 162h-4l-107 -162h-203z" />
<glyph unicode="&#xcb;" horiz-adv-x="1277" d="M125 0v1464h1059v-207h-815v-397h714v-209h-714v-444h835v-207h-1079zM371 1782q0 52 32.5 86.5t88.5 34.5q55 0 86.5 -34.5t31.5 -86.5q0 -48 -31.5 -82.5t-86.5 -34.5q-56 0 -88.5 34.5t-32.5 82.5zM721 1782q0 52 32.5 86.5t88.5 34.5q55 0 87 -34.5t32 -86.5 q0 -48 -32 -82.5t-87 -34.5q-56 0 -88.5 34.5t-32.5 82.5z" />
<glyph unicode="&#xcc;" horiz-adv-x="499" d="M-2 1898h250l88 -268h-168zM129 0v1464h244v-1464h-244z" />
<glyph unicode="&#xcd;" horiz-adv-x="499" d="M129 0v1464h244v-1464h-244zM162 1630l88 268h250l-170 -268h-168z" />
<glyph unicode="&#xce;" horiz-adv-x="499" d="M-61 1632l198 269h228l198 -269h-203l-108 162h-4l-107 -162h-202zM129 0v1464h244v-1464h-244z" />
<glyph unicode="&#xcf;" horiz-adv-x="499" d="M-45 1782q0 52 32.5 86.5t88.5 34.5q55 0 87 -34.5t32 -86.5q0 -48 -32 -82.5t-87 -34.5q-56 0 -88.5 34.5t-32.5 82.5zM129 0v1464h244v-1464h-244zM305 1782q0 52 32.5 86.5t88.5 34.5q55 0 87 -34.5t32 -86.5q0 -48 -32 -82.5t-87 -34.5q-56 0 -88.5 34.5t-32.5 82.5z " />
<glyph unicode="&#xd0;" horiz-adv-x="1431" d="M8 680v156h133v628h570q305 0 478 -191.5t173 -541.5q0 -351 -169.5 -541t-475.5 -190h-576v680h-133zM385 207h313q420 0 420 524q0 526 -420 526h-313v-421h412v-156h-412v-473z" />
<glyph unicode="&#xd1;" horiz-adv-x="1429" d="M125 0v1464h272l660 -1099h4v1099h244v-1464h-271l-661 1096h-4v-1096h-244zM414 1675q0 111 44 165t115 54q54 0 141 -37.5t113 -37.5q54 0 54 71h139q0 -113 -44.5 -166t-117.5 -53q-53 0 -140 38t-114 38q-53 0 -53 -72h-137z" />
<glyph unicode="&#xd2;" horiz-adv-x="1576" d="M70 733q0 220 86.5 392t250.5 271t381 99q162 0 297.5 -57.5t227.5 -158.5t143 -241.5t51 -304.5t-51 -305t-143 -242.5t-227.5 -159t-297.5 -57.5q-217 0 -381 99.5t-250.5 272t-86.5 392.5zM311 733q0 -115 28 -213t84 -175.5t149.5 -122t215.5 -44.5t215.5 44.5 t149 122.5t83.5 175.5t28 212.5q0 114 -28 211.5t-83.5 175t-149 122t-215.5 44.5t-215.5 -44.5t-149.5 -122t-84 -175t-28 -211.5zM537 1898h249l88 -268h-167z" />
<glyph unicode="&#xd3;" horiz-adv-x="1576" d="M70 733q0 220 86.5 392t250.5 271t381 99q162 0 297.5 -57.5t227.5 -158.5t143 -241.5t51 -304.5t-51 -305t-143 -242.5t-227.5 -159t-297.5 -57.5q-217 0 -381 99.5t-250.5 272t-86.5 392.5zM311 733q0 -115 28 -213t84 -175.5t149.5 -122t215.5 -44.5t215.5 44.5 t149 122.5t83.5 175.5t28 212.5q0 114 -28 211.5t-83.5 175t-149 122t-215.5 44.5t-215.5 -44.5t-149.5 -122t-84 -175t-28 -211.5zM700 1630l88 268h250l-170 -268h-168z" />
<glyph unicode="&#xd4;" horiz-adv-x="1576" d="M70 733q0 220 86.5 392t250.5 271t381 99q162 0 297.5 -57.5t227.5 -158.5t143 -241.5t51 -304.5t-51 -305t-143 -242.5t-227.5 -159t-297.5 -57.5q-217 0 -381 99.5t-250.5 272t-86.5 392.5zM311 733q0 -115 28 -213t84 -175.5t149.5 -122t215.5 -44.5t215.5 44.5 t149 122.5t83.5 175.5t28 212.5q0 114 -28 211.5t-83.5 175t-149 122t-215.5 44.5t-215.5 -44.5t-149.5 -122t-84 -175t-28 -211.5zM477 1632l199 269h227l199 -269h-203l-108 162h-5l-106 -162h-203z" />
<glyph unicode="&#xd5;" horiz-adv-x="1576" d="M70 733q0 220 86.5 392t250.5 271t381 99q162 0 297.5 -57.5t227.5 -158.5t143 -241.5t51 -304.5t-51 -305t-143 -242.5t-227.5 -159t-297.5 -57.5q-217 0 -381 99.5t-250.5 272t-86.5 392.5zM311 733q0 -115 28 -213t84 -175.5t149.5 -122t215.5 -44.5t215.5 44.5 t149 122.5t83.5 175.5t28 212.5q0 114 -28 211.5t-83.5 175t-149 122t-215.5 44.5t-215.5 -44.5t-149.5 -122t-84 -175t-28 -211.5zM485 1675q0 111 44.5 165t115.5 54q54 0 141 -37.5t113 -37.5q53 0 53 71h140q0 -113 -44.5 -166t-117.5 -53q-53 0 -140 38t-114 38 q-53 0 -53 -72h-138z" />
<glyph unicode="&#xd6;" horiz-adv-x="1576" d="M70 733q0 220 86.5 392t250.5 271t381 99q162 0 297.5 -57.5t227.5 -158.5t143 -241.5t51 -304.5t-51 -305t-143 -242.5t-227.5 -159t-297.5 -57.5q-217 0 -381 99.5t-250.5 272t-86.5 392.5zM311 733q0 -115 28 -213t84 -175.5t149.5 -122t215.5 -44.5t215.5 44.5 t149 122.5t83.5 175.5t28 212.5q0 114 -28 211.5t-83.5 175t-149 122t-215.5 44.5t-215.5 -44.5t-149.5 -122t-84 -175t-28 -211.5zM494 1782q0 52 32 86.5t88 34.5q55 0 87 -34.5t32 -86.5q0 -48 -32 -82.5t-87 -34.5q-56 0 -88 34t-32 83zM844 1782q0 52 32.5 86.5 t88.5 34.5q55 0 86.5 -34.5t31.5 -86.5q0 -48 -31.5 -82.5t-86.5 -34.5q-56 0 -88.5 34.5t-32.5 82.5z" />
<glyph unicode="&#xd7;" horiz-adv-x="1150" d="M125 365l305 305l-305 303l143 143l305 -305l306 305l143 -143l-305 -303l305 -305l-143 -144l-306 305l-305 -305z" />
<glyph unicode="&#xd8;" horiz-adv-x="1581" d="M66 84l155 156q-149 203 -149 493q0 220 86.5 392t251 271t381.5 99q299 0 495 -186l150 149l77 -74l-155 -155q151 -202 151 -496q0 -220 -87 -393t-251 -272t-380 -99q-306 0 -500 191l-150 -152zM313 733q0 -191 74 -327l741 745q-122 135 -337 135 q-98 0 -178.5 -29.5t-135.5 -80.5t-92.5 -121.5t-54.5 -151t-17 -170.5zM449 317q126 -139 342 -139q122 0 215.5 44.5t149 122.5t83 175.5t27.5 212.5q0 195 -74 328z" />
<glyph unicode="&#xd9;" horiz-adv-x="1366" d="M104 500v964h244v-964q0 -165 83 -243.5t251 -78.5q170 0 253 78.5t83 243.5v964h242v-964q0 -250 -141.5 -390.5t-436.5 -140.5t-436.5 140.5t-141.5 390.5zM430 1898h250l88 -268h-168z" />
<glyph unicode="&#xda;" horiz-adv-x="1366" d="M104 500v964h244v-964q0 -165 83 -243.5t251 -78.5q170 0 253 78.5t83 243.5v964h242v-964q0 -250 -141.5 -390.5t-436.5 -140.5t-436.5 140.5t-141.5 390.5zM594 1630l88 268h250l-170 -268h-168z" />
<glyph unicode="&#xdb;" horiz-adv-x="1366" d="M104 500v964h244v-964q0 -165 83 -243.5t251 -78.5q170 0 253 78.5t83 243.5v964h242v-964q0 -250 -141.5 -390.5t-436.5 -140.5t-436.5 140.5t-141.5 390.5zM371 1632l198 269h228l198 -269h-202l-109 162h-4l-107 -162h-202z" />
<glyph unicode="&#xdc;" horiz-adv-x="1366" d="M104 500v964h244v-964q0 -165 83 -243.5t251 -78.5q170 0 253 78.5t83 243.5v964h242v-964q0 -250 -141.5 -390.5t-436.5 -140.5t-436.5 140.5t-141.5 390.5zM387 1782q0 52 32.5 86.5t88.5 34.5q55 0 87 -34.5t32 -86.5q0 -48 -32 -82.5t-87 -34.5q-56 0 -88.5 34.5 t-32.5 82.5zM737 1782q0 52 32.5 86.5t88.5 34.5q55 0 87 -34.5t32 -86.5q0 -48 -32 -82.5t-87 -34.5q-56 0 -88.5 34.5t-32.5 82.5z" />
<glyph unicode="&#xdd;" horiz-adv-x="1312" d="M14 1464h260l379 -680h4l381 680h260l-520 -882v-582h-243v582zM569 1630l88 268h250l-170 -268h-168z" />
<glyph unicode="&#xde;" horiz-adv-x="1261" d="M125 0v1464h244v-233h379q108 0 194.5 -25.5t152 -77.5t101 -137.5t35.5 -197.5q0 -113 -35.5 -198t-101 -137t-152.5 -78t-194 -26h-379v-354h-244zM369 561h368q250 0 250 232q0 118 -62.5 174.5t-187.5 56.5h-368v-463z" />
<glyph unicode="&#xdf;" horiz-adv-x="1087" d="M102 0v1036q0 212 116 335.5t337 123.5q173 0 295.5 -104.5t122.5 -266.5q0 -64 -17.5 -115t-50 -88t-70 -62t-85.5 -46v-4q135 -42 212.5 -148t77.5 -264q0 -187 -113.5 -307.5t-283.5 -120.5q-97 0 -209 31v176q132 -42 219 -15t129.5 94.5t42.5 162.5q0 68 -19 122.5 t-50 88.5t-73 57t-86 32t-91 9h-57v164h51q106 0 182 55.5t76 157.5q0 90 -53.5 152.5t-149.5 62.5q-130 0 -184 -73t-54 -210v-1036h-215z" />
<glyph unicode="&#xe0;" horiz-adv-x="1073" d="M43 268q0 67 20.5 119.5t55.5 89t91.5 64.5t118 45.5t146.5 34.5q43 8 66 13t55.5 13t49.5 15t37 18t29.5 23.5t16 30t6.5 38.5q0 133 -188 133q-128 0 -183 -43t-63 -145h-209q8 162 119.5 260t335.5 98q403 0 403 -366v-498q0 -60 20.5 -85.5t80.5 -19.5v-102 q-60 -22 -125 -22q-86 0 -130 33t-59 110h-4q-52 -72 -140.5 -114t-205.5 -42q-158 0 -251 82.5t-93 216.5zM260 289q0 -150 174 -150q137 0 219 67t82 191v158q-51 -36 -219 -70q-137 -30 -196.5 -74t-59.5 -122zM295 1481h250l88 -269h-168z" />
<glyph unicode="&#xe1;" horiz-adv-x="1073" d="M43 268q0 67 20.5 119.5t55.5 89t91.5 64.5t118 45.5t146.5 34.5q43 8 66 13t55.5 13t49.5 15t37 18t29.5 23.5t16 30t6.5 38.5q0 133 -188 133q-128 0 -183 -43t-63 -145h-209q8 162 119.5 260t335.5 98q403 0 403 -366v-498q0 -60 20.5 -85.5t80.5 -19.5v-102 q-60 -22 -125 -22q-86 0 -130 33t-59 110h-4q-52 -72 -140.5 -114t-205.5 -42q-158 0 -251 82.5t-93 216.5zM260 289q0 -150 174 -150q137 0 219 67t82 191v158q-51 -36 -219 -70q-137 -30 -196.5 -74t-59.5 -122zM459 1212l88 269h250l-170 -269h-168z" />
<glyph unicode="&#xe2;" horiz-adv-x="1073" d="M43 268q0 67 20.5 119.5t55.5 89t91.5 64.5t118 45.5t146.5 34.5q43 8 66 13t55.5 13t49.5 15t37 18t29.5 23.5t16 30t6.5 38.5q0 133 -188 133q-128 0 -183 -43t-63 -145h-209q8 162 119.5 260t335.5 98q403 0 403 -366v-498q0 -60 20.5 -85.5t80.5 -19.5v-102 q-60 -22 -125 -22q-86 0 -130 33t-59 110h-4q-52 -72 -140.5 -114t-205.5 -42q-158 0 -251 82.5t-93 216.5zM236 1214l198 269h228l198 -269h-203l-108 162h-4l-107 -162h-202zM260 289q0 -150 174 -150q137 0 219 67t82 191v158q-51 -36 -219 -70q-137 -30 -196.5 -74 t-59.5 -122z" />
<glyph unicode="&#xe3;" horiz-adv-x="1073" d="M43 268q0 67 20.5 119.5t55.5 89t91.5 64.5t118 45.5t146.5 34.5q43 8 66 13t55.5 13t49.5 15t37 18t29.5 23.5t16 30t6.5 38.5q0 133 -188 133q-128 0 -183 -43t-63 -145h-209q8 162 119.5 260t335.5 98q403 0 403 -366v-498q0 -60 20.5 -85.5t80.5 -19.5v-102 q-60 -22 -125 -22q-86 0 -130 33t-59 110h-4q-52 -72 -140.5 -114t-205.5 -42q-158 0 -251 82.5t-93 216.5zM244 1257q0 111 44 165.5t115 54.5q54 0 141 -38t113 -38q54 0 54 72h139q0 -113 -44.5 -166.5t-117.5 -53.5q-53 0 -140 38t-114 38q-53 0 -53 -72h-137zM260 289 q0 -150 174 -150q137 0 219 67t82 191v158q-51 -36 -219 -70q-137 -30 -196.5 -74t-59.5 -122z" />
<glyph unicode="&#xe4;" horiz-adv-x="1073" d="M43 268q0 67 20.5 119.5t55.5 89t91.5 64.5t118 45.5t146.5 34.5q43 8 66 13t55.5 13t49.5 15t37 18t29.5 23.5t16 30t6.5 38.5q0 133 -188 133q-128 0 -183 -43t-63 -145h-209q8 162 119.5 260t335.5 98q403 0 403 -366v-498q0 -60 20.5 -85.5t80.5 -19.5v-102 q-60 -22 -125 -22q-86 0 -130 33t-59 110h-4q-52 -72 -140.5 -114t-205.5 -42q-158 0 -251 82.5t-93 216.5zM252 1364q0 52 32.5 86.5t88.5 34.5q55 0 87 -34.5t32 -86.5q0 -48 -32 -82.5t-87 -34.5q-56 0 -88.5 34.5t-32.5 82.5zM260 289q0 -150 174 -150q137 0 219 67 t82 191v158q-51 -36 -219 -70q-137 -30 -196.5 -74t-59.5 -122zM602 1364q0 52 32.5 86.5t88.5 34.5q55 0 87 -34.5t32 -86.5q0 -48 -32 -82.5t-87 -34.5q-56 0 -88.5 34.5t-32.5 82.5z" />
<glyph unicode="&#xe5;" horiz-adv-x="1073" d="M43 268q0 67 20.5 119.5t55.5 89t91.5 64.5t118 45.5t146.5 34.5q43 8 66 13t55.5 13t49.5 15t37 18t29.5 23.5t16 30t6.5 38.5q0 133 -188 133q-128 0 -183 -43t-63 -145h-209q8 162 119.5 260t335.5 98q403 0 403 -366v-498q0 -60 20.5 -85.5t80.5 -19.5v-102 q-60 -22 -125 -22q-86 0 -130 33t-59 110h-4q-52 -72 -140.5 -114t-205.5 -42q-158 0 -251 82.5t-93 216.5zM260 289q0 -150 174 -150q137 0 219 67t82 191v158q-51 -36 -219 -70q-137 -30 -196.5 -74t-59.5 -122zM358 1372q0 82 55.5 135.5t133.5 53.5t133 -53.5t55 -135.5 q0 -84 -54.5 -135t-133.5 -51t-134 51t-55 135zM455 1372q0 -43 27 -69.5t65 -26.5t65 26.5t27 69.5t-27 69.5t-65 26.5t-65 -26.5t-27 -69.5z" />
<glyph unicode="&#xe6;" horiz-adv-x="1798" d="M43 268q0 67 20.5 119.5t55.5 89t91.5 64.5t118 45.5t146.5 34.5q43 8 66 13t55.5 13t49.5 15t37 18t29.5 23.5t16 30t6.5 38.5q0 133 -188 133q-128 0 -183 -43t-63 -145h-209q8 162 119.5 260t335.5 98q246 0 342 -135q135 135 360 135q248 0 376 -161t128 -447h-803 q0 -137 82.5 -229.5t220.5 -92.5q63 0 114 18t81.5 46t48 55.5t25.5 54.5h215q-42 -158 -163.5 -254t-313.5 -96q-133 0 -239.5 49.5t-174.5 139.5q-79 -101 -199 -145t-246 -44q-167 0 -262.5 82.5t-95.5 216.5zM260 289q0 -150 174 -150q147 0 224 74.5t77 183.5v158 q-51 -36 -219 -70q-137 -30 -196.5 -74t-59.5 -122zM950 627h588q0 122 -79.5 200t-209.5 78q-131 0 -215 -78.5t-84 -199.5z" />
<glyph unicode="&#xe7;" horiz-adv-x="1077" d="M47 522q0 247 141 400t373 153q200 0 328 -114.5t149 -296.5h-215q-3 43 -18.5 82t-45 74.5t-80 57t-116.5 21.5q-76 0 -135.5 -31t-94.5 -84t-53 -119.5t-18 -142.5t17.5 -142.5t52.5 -119.5t94.5 -84t136.5 -31q66 0 116.5 21t80 57.5t45 77.5t18.5 88h215 q-12 -175 -133 -292.5t-309 -127.5l-72 -86v-4q77 10 142 -22t65 -105q0 -71 -67.5 -113t-161.5 -42q-137 0 -223 59l49 59q75 -30 149 -30q45 0 76 19t31 50q0 56 -97 56q-39 0 -77 -15l-29 43l108 135q-202 25 -322 174t-120 375z" />
<glyph unicode="&#xe8;" horiz-adv-x="1110" d="M47 522q0 255 139 404t375 149q248 0 376 -161t128 -447h-803q0 -137 82.5 -229.5t220.5 -92.5q63 0 114 18t81.5 46t48 55.5t25.5 54.5h215q-42 -158 -164 -254t-314 -96q-242 0 -383 152t-141 401zM262 627h588q0 122 -79.5 200t-209.5 78q-131 0 -215 -78.5 t-84 -199.5zM305 1481h250l88 -269h-168z" />
<glyph unicode="&#xe9;" horiz-adv-x="1110" d="M47 522q0 255 139 404t375 149q248 0 376 -161t128 -447h-803q0 -137 82.5 -229.5t220.5 -92.5q63 0 114 18t81.5 46t48 55.5t25.5 54.5h215q-42 -158 -164 -254t-314 -96q-242 0 -383 152t-141 401zM262 627h588q0 122 -79.5 200t-209.5 78q-131 0 -215 -78.5 t-84 -199.5zM469 1212l88 269h250l-170 -269h-168z" />
<glyph unicode="&#xea;" horiz-adv-x="1110" d="M47 522q0 255 139 404t375 149q248 0 376 -161t128 -447h-803q0 -137 82.5 -229.5t220.5 -92.5q63 0 114 18t81.5 46t48 55.5t25.5 54.5h215q-42 -158 -164 -254t-314 -96q-242 0 -383 152t-141 401zM246 1214l198 269h228l198 -269h-202l-109 162h-4l-106 -162h-203z M262 627h588q0 122 -79.5 200t-209.5 78q-131 0 -215 -78.5t-84 -199.5z" />
<glyph unicode="&#xeb;" horiz-adv-x="1110" d="M47 522q0 255 139 404t375 149q248 0 376 -161t128 -447h-803q0 -137 82.5 -229.5t220.5 -92.5q63 0 114 18t81.5 46t48 55.5t25.5 54.5h215q-42 -158 -164 -254t-314 -96q-242 0 -383 152t-141 401zM262 627h588q0 122 -79.5 200t-209.5 78q-131 0 -215 -78.5 t-84 -199.5zM262 1364q0 52 32.5 86.5t88.5 34.5q55 0 87 -34.5t32 -86.5q0 -48 -32 -82.5t-87 -34.5q-56 0 -88.5 34.5t-32.5 82.5zM612 1364q0 52 32.5 86.5t88.5 34.5q55 0 87 -34.5t32 -86.5q0 -48 -32 -82.5t-87 -34.5q-56 0 -88.5 34.5t-32.5 82.5z" />
<glyph unicode="&#xec;" horiz-adv-x="339" d="M-82 1481h250l88 -269h-168zM61 0v1047h218v-1047h-218z" />
<glyph unicode="&#xed;" horiz-adv-x="339" d="M61 0v1047h218v-1047h-218zM82 1212l88 269h250l-170 -269h-168z" />
<glyph unicode="&#xee;" horiz-adv-x="339" d="M-141 1214l198 269h228l198 -269h-202l-109 162h-4l-107 -162h-202zM61 0v1047h218v-1047h-218z" />
<glyph unicode="&#xef;" horiz-adv-x="339" d="M-125 1364q0 52 32.5 86.5t88.5 34.5q55 0 87 -34.5t32 -86.5q0 -48 -32 -82.5t-87 -34.5q-56 0 -88.5 34.5t-32.5 82.5zM61 0v1047h218v-1047h-218zM225 1364q0 52 32.5 86.5t88.5 34.5q55 0 87 -34.5t32 -86.5q0 -48 -32 -82.5t-87 -34.5q-56 0 -88.5 34.5t-32.5 82.5z " />
<glyph unicode="&#xf0;" horiz-adv-x="1146" d="M47 526q0 128 39.5 234t107 175.5t156.5 107.5t189 38q93 0 172.5 -43t130.5 -114v4q-50 150 -197 297l-307 -82l-27 106l236 64q-105 86 -219 151h282q62 -32 140 -98l251 68l29 -107l-188 -51q256 -283 256 -658q0 -99 -17.5 -190.5t-58.5 -176.5t-101.5 -147 t-152.5 -99.5t-205 -37.5q-245 0 -380.5 151.5t-135.5 407.5zM262 526q0 -165 79 -270.5t222 -105.5t227 106.5t84 269.5t-84 269t-227 106t-222 -105t-79 -270z" />
<glyph unicode="&#xf1;" d="M102 0v1047h215v-136h5q47 70 133.5 117t210.5 47q154 0 251 -89.5t97 -262.5v-723h-217v662q0 231 -207 231q-125 0 -199 -69.5t-74 -186.5v-637h-215zM274 1257q0 111 44.5 165.5t115.5 54.5q54 0 141 -38t113 -38q53 0 53 72h140q0 -113 -44.5 -166.5t-117.5 -53.5 q-53 0 -140 38t-114 38q-53 0 -53 -72h-138z" />
<glyph unicode="&#xf2;" horiz-adv-x="1146" d="M47 522q0 -248 141.5 -400.5t384.5 -152.5t385 152.5t142 400.5t-142 400.5t-385 152.5t-384.5 -152.5t-141.5 -400.5zM262 522q0 169 81 273t230 104t230.5 -104.5t81.5 -272.5q0 -167 -81.5 -272t-230.5 -105t-230 105t-81 272zM322 1481h249l88 -269h-167z" />
<glyph unicode="&#xf3;" horiz-adv-x="1146" d="M47 522q0 -248 141.5 -400.5t384.5 -152.5t385 152.5t142 400.5t-142 400.5t-385 152.5t-384.5 -152.5t-141.5 -400.5zM262 522q0 169 81 273t230 104t230.5 -104.5t81.5 -272.5q0 -167 -81.5 -272t-230.5 -105t-230 105t-81 272zM485 1212l88 269h250l-170 -269h-168z " />
<glyph unicode="&#xf4;" horiz-adv-x="1146" d="M47 522q0 -248 141.5 -400.5t384.5 -152.5t385 152.5t142 400.5t-142 400.5t-385 152.5t-384.5 -152.5t-141.5 -400.5zM262 522q0 169 81 273t230 104t230.5 -104.5t81.5 -272.5q0 -167 -81.5 -272t-230.5 -105t-230 105t-81 272zM262 1214l199 269h227l199 -269h-203 l-109 162h-4l-106 -162h-203z" />
<glyph unicode="&#xf5;" horiz-adv-x="1146" d="M47 522q0 -248 141.5 -400.5t384.5 -152.5t385 152.5t142 400.5t-142 400.5t-385 152.5t-384.5 -152.5t-141.5 -400.5zM262 522q0 169 81 273t230 104t230.5 -104.5t81.5 -272.5q0 -167 -81.5 -272t-230.5 -105t-230 105t-81 272zM270 1257q0 111 44.5 165.5t115.5 54.5 q54 0 141 -38t113 -38q53 0 53 72h140q0 -113 -44.5 -166.5t-117.5 -53.5q-53 0 -140 38t-114 38q-53 0 -53 -72h-138z" />
<glyph unicode="&#xf6;" horiz-adv-x="1146" d="M47 522q0 -248 141.5 -400.5t384.5 -152.5t385 152.5t142 400.5t-142 400.5t-385 152.5t-384.5 -152.5t-141.5 -400.5zM262 522q0 169 81 273t230 104t230.5 -104.5t81.5 -272.5q0 -167 -81.5 -272t-230.5 -105t-230 105t-81 272zM279 1364q0 52 32 86.5t88 34.5 q55 0 87 -34.5t32 -86.5q0 -48 -32 -82.5t-87 -34.5q-56 0 -88 34t-32 83zM629 1364q0 52 32.5 86.5t88.5 34.5q55 0 86.5 -34.5t31.5 -86.5q0 -48 -31.5 -82.5t-86.5 -34.5q-56 0 -88.5 34.5t-32.5 82.5z" />
<glyph unicode="&#xf7;" horiz-adv-x="1150" d="M100 569v201h951v-201h-951zM440 307q0 60 35 92.5t100 32.5q64 0 100 -33t36 -92t-36 -92t-100 -33q-65 0 -100 32.5t-35 92.5zM440 1028q0 60 35 92.5t100 32.5q64 0 100 -33t36 -92t-36 -92t-100 -33q-65 0 -100 32.5t-35 92.5z" />
<glyph unicode="&#xf8;" horiz-adv-x="1183" d="M47 522q0 248 141.5 400.5t384.5 152.5q223 0 365 -133l123 125l76 -76l-129 -131q92 -140 92 -338q0 -248 -142 -400.5t-385 -152.5q-207 0 -344 115l-104 -107l-74 76l107 107q-111 145 -111 362zM262 522q0 -127 45 -213l490 494q-80 96 -224 96q-149 0 -230 -104 t-81 -273zM369 221q82 -76 204 -76q149 0 230.5 105t81.5 272q0 103 -31 185z" />
<glyph unicode="&#xf9;" d="M94 324v723h217v-662q0 -231 207 -231q125 0 199 69.5t74 186.5v637h215v-1047h-215v135h-5q-47 -70 -133.5 -117t-210.5 -47q-154 0 -251 90t-97 263zM299 1481h250l88 -269h-168z" />
<glyph unicode="&#xfa;" d="M94 324v723h217v-662q0 -231 207 -231q125 0 199 69.5t74 186.5v637h215v-1047h-215v135h-5q-47 -70 -133.5 -117t-210.5 -47q-154 0 -251 90t-97 263zM463 1212l88 269h250l-170 -269h-168z" />
<glyph unicode="&#xfb;" d="M94 324v723h217v-662q0 -231 207 -231q125 0 199 69.5t74 186.5v637h215v-1047h-215v135h-5q-47 -70 -133.5 -117t-210.5 -47q-154 0 -251 90t-97 263zM240 1214l198 269h228l198 -269h-202l-109 162h-4l-107 -162h-202z" />
<glyph unicode="&#xfc;" d="M94 324v723h217v-662q0 -231 207 -231q125 0 199 69.5t74 186.5v637h215v-1047h-215v135h-5q-47 -70 -133.5 -117t-210.5 -47q-154 0 -251 90t-97 263zM256 1364q0 52 32.5 86.5t88.5 34.5q55 0 87 -34.5t32 -86.5q0 -48 -32 -82.5t-87 -34.5q-56 0 -88.5 34.5 t-32.5 82.5zM606 1364q0 52 32.5 86.5t88.5 34.5q55 0 87 -34.5t32 -86.5q0 -48 -32 -82.5t-87 -34.5q-56 0 -88.5 34.5t-32.5 82.5z" />
<glyph unicode="&#xfd;" horiz-adv-x="1028" d="M0 1047h231l293 -801h4l269 801h229l-418 -1113q-20 -55 -37.5 -93t-45 -78.5t-59.5 -64t-79 -39t-106 -15.5q-72 0 -154 20v164h18q65 -11 118.5 0t86.5 51q21 26 34 58t13 77.5t-18 91.5zM426 1212l88 269h250l-170 -269h-168z" />
<glyph unicode="&#xfe;" horiz-adv-x="1155" d="M102 -356v1820h215v-553h5q105 164 323 164q202 0 332.5 -150t130.5 -403t-130.5 -403t-332.5 -150q-221 0 -323 170h-5v-495h-215zM319 522q0 -174 76.5 -275.5t208.5 -101.5q136 0 212.5 108t76.5 269t-76.5 269t-212.5 108q-132 0 -208.5 -101t-76.5 -276z" />
<glyph unicode="&#xff;" horiz-adv-x="1028" d="M0 1047h231l293 -801h4l269 801h229l-418 -1113q-20 -55 -37.5 -93t-45 -78.5t-59.5 -64t-79 -39t-106 -15.5q-72 0 -154 20v164h18q65 -11 118.5 0t86.5 51q21 26 34 58t13 77.5t-18 91.5zM219 1364q0 52 32.5 86.5t88.5 34.5q55 0 87 -34.5t32 -86.5q0 -48 -32 -82.5 t-87 -34.5q-56 0 -88.5 34.5t-32.5 82.5zM569 1364q0 52 32.5 86.5t88.5 34.5q55 0 87 -34.5t32 -86.5q0 -48 -32 -82.5t-87 -34.5q-56 0 -88.5 34.5t-32.5 82.5z" />
<glyph unicode="&#x152;" horiz-adv-x="2211" d="M70 733q0 218 76.5 391.5t217 272t320.5 98.5q233 0 375 -154v123h1059v-207h-815v-397h714v-209h-714v-444h835v-207h-1079v123q-145 -154 -375 -154q-180 0 -320.5 99t-217 273t-76.5 392zM311 733q0 -92 14 -172t46 -151.5t80 -122t120 -80t162 -29.5q103 0 188 51.5 t138 151.5v702q-53 100 -138 151.5t-188 51.5q-90 0 -162 -29.5t-120 -80t-80 -121.5t-46 -151t-14 -171z" />
<glyph unicode="&#x153;" horiz-adv-x="1986" d="M47 522q0 248 141.5 400.5t384.5 152.5q146 0 258 -57.5t179 -161.5q65 105 175 162t253 57q248 0 376 -161t128 -447h-803q0 -137 82.5 -229.5t220.5 -92.5q63 0 113.5 17.5t81 46t48 56t25.5 54.5h215q-42 -158 -163.5 -254t-313.5 -96q-146 0 -257 58t-179 163 q-67 -105 -179.5 -163t-259.5 -58q-243 0 -384.5 152.5t-141.5 400.5zM262 522q0 -167 81 -272t230 -105t230.5 105t81.5 272q0 168 -81.5 272.5t-230.5 104.5t-230 -104t-81 -273zM1139 627h587q0 123 -79.5 200.5t-208.5 77.5q-131 0 -215 -78.5t-84 -199.5z" />
<glyph unicode="&#x178;" horiz-adv-x="1312" d="M14 1464h260l379 -680h4l381 680h260l-520 -882v-582h-243v582zM362 1782q0 52 32.5 86.5t88.5 34.5q55 0 87 -34.5t32 -86.5q0 -48 -32 -82.5t-87 -34.5q-56 0 -88.5 34.5t-32.5 82.5zM713 1782q0 52 32.5 86.5t88.5 34.5q55 0 86.5 -34.5t31.5 -86.5q0 -48 -31.5 -82.5 t-86.5 -34.5q-56 0 -88.5 34.5t-32.5 82.5z" />
<glyph unicode="&#x2c6;" horiz-adv-x="884" d="M201 1214l198 269h228l198 -269h-202l-109 162h-4l-107 -162h-202z" />
<glyph unicode="&#x2dc;" horiz-adv-x="1024" d="M209 1257q0 111 44.5 165.5t115.5 54.5q54 0 141 -38t113 -38q53 0 53 72h139q0 -113 -44.5 -166.5t-117.5 -53.5q-53 0 -140 38t-114 38q-53 0 -53 -72h-137z" />
<glyph unicode="&#x2000;" horiz-adv-x="989" />
<glyph unicode="&#x2001;" horiz-adv-x="1978" />
<glyph unicode="&#x2002;" horiz-adv-x="989" />
<glyph unicode="&#x2003;" horiz-adv-x="1978" />
<glyph unicode="&#x2004;" horiz-adv-x="659" />
<glyph unicode="&#x2005;" horiz-adv-x="494" />
<glyph unicode="&#x2006;" horiz-adv-x="329" />
<glyph unicode="&#x2007;" horiz-adv-x="329" />
<glyph unicode="&#x2008;" horiz-adv-x="247" />
<glyph unicode="&#x2009;" horiz-adv-x="395" />
<glyph unicode="&#x200a;" horiz-adv-x="109" />
<glyph unicode="&#x2010;" horiz-adv-x="698" d="M74 420v201h551v-201h-551z" />
<glyph unicode="&#x2011;" horiz-adv-x="698" d="M74 420v201h551v-201h-551z" />
<glyph unicode="&#x2012;" horiz-adv-x="698" d="M74 420v201h551v-201h-551z" />
<glyph unicode="&#x2013;" horiz-adv-x="1024" d="M0 420v201h1024v-201h-1024z" />
<glyph unicode="&#x2014;" horiz-adv-x="2048" d="M205 418v203h1638v-203h-1638z" />
<glyph unicode="&#x2018;" horiz-adv-x="432" d="M80 1122q0 79 25 150.5t83.5 127.5t143.5 70v-116q-57 -10 -91 -53.5t-34 -102.5q62 11 103.5 -23t41.5 -104q0 -64 -37.5 -100.5t-95.5 -36.5q-68 0 -103.5 50t-35.5 138z" />
<glyph unicode="&#x2019;" horiz-adv-x="432" d="M80 1343q0 64 38 101t95 37q68 0 103.5 -50.5t35.5 -138.5q0 -79 -25 -150.5t-83.5 -127.5t-143.5 -70v117q57 10 91 53.5t34 102.5q-62 -11 -103.5 22.5t-41.5 103.5z" />
<glyph unicode="&#x201a;" horiz-adv-x="432" d="M80 113q0 64 37.5 100.5t95.5 36.5q68 0 103.5 -50.5t35.5 -138.5q0 -79 -25 -150.5t-83.5 -127.5t-143.5 -70v117q57 10 91 53.5t34 102.5q-62 -11 -103.5 23t-41.5 104z" />
<glyph unicode="&#x201c;" horiz-adv-x="780" d="M80 1122q0 79 25 150.5t83.5 127.5t143.5 70v-116q-57 -10 -91 -53.5t-34 -102.5q62 11 103.5 -23t41.5 -104q0 -64 -37.5 -100.5t-95.5 -36.5q-68 0 -103.5 50t-35.5 138zM428 1122q0 79 25 150.5t83.5 127.5t143.5 70v-116q-57 -10 -91 -53.5t-34 -102.5 q62 11 103.5 -23t41.5 -104q0 -64 -37.5 -100.5t-95.5 -36.5q-68 0 -103.5 50t-35.5 138z" />
<glyph unicode="&#x201d;" horiz-adv-x="780" d="M80 1343q0 64 38 101t95 37q68 0 103.5 -50.5t35.5 -138.5q0 -79 -25 -150.5t-83.5 -127.5t-143.5 -70v117q57 10 91 53.5t34 102.5q-62 -11 -103.5 22.5t-41.5 103.5zM428 1343q0 64 38 101t95 37q68 0 103.5 -50.5t35.5 -138.5q0 -80 -24.5 -151t-83.5 -127t-143 -70 v117q56 10 90 53.5t34 102.5q-62 -11 -103.5 22.5t-41.5 103.5z" />
<glyph unicode="&#x201e;" horiz-adv-x="782" d="M80 113q0 64 37.5 100.5t95.5 36.5q68 0 103.5 -50.5t35.5 -138.5q0 -79 -25 -150.5t-83.5 -127.5t-143.5 -70v117q57 10 91 53.5t34 102.5q-62 -11 -103.5 23t-41.5 104zM430 113q0 64 37.5 100.5t95.5 36.5q68 0 103.5 -50.5t35.5 -138.5q0 -80 -24.5 -151t-83.5 -127 t-143 -70v117q56 10 90 53.5t34 102.5q-62 -11 -103.5 23t-41.5 104z" />
<glyph unicode="&#x2022;" horiz-adv-x="673" d="M72 655q0 112 73 189.5t193 77.5q118 0 191 -77.5t73 -189.5t-73 -189t-191 -77q-120 0 -193 77t-73 189z" />
<glyph unicode="&#x2026;" horiz-adv-x="1150" d="M82 117q0 59 37.5 96t95.5 37q56 0 93.5 -37t37.5 -96q0 -60 -37 -96.5t-94 -36.5q-58 0 -95.5 36.5t-37.5 96.5zM442 117q0 59 37.5 96t95.5 37q56 0 94 -37t38 -96q0 -60 -37.5 -96.5t-94.5 -36.5q-58 0 -95.5 36.5t-37.5 96.5zM803 117q0 59 37.5 96t95.5 37 q56 0 93.5 -37t37.5 -96q0 -60 -37 -96.5t-94 -36.5q-58 0 -95.5 36.5t-37.5 96.5z" />
<glyph unicode="&#x202f;" horiz-adv-x="395" />
<glyph unicode="&#x2039;" horiz-adv-x="516" d="M98 403v226l316 252v-209l-201 -154v-4l201 -154v-208z" />
<glyph unicode="&#x203a;" horiz-adv-x="516" d="M102 152v208l201 154v4l-201 154v209l316 -252v-226z" />
<glyph unicode="&#x205f;" horiz-adv-x="494" />
<glyph unicode="&#x20ac;" horiz-adv-x="1320" d="M61 516v123h111q-2 20 -2 63v54h-109v123h121q36 257 179.5 406t379.5 149q119 0 213.5 -38.5t157 -108t100 -161t48.5 -202.5h-238q-10 62 -26.5 110.5t-47 96t-83.5 74t-126 26.5q-139 0 -216.5 -94t-102.5 -258h381v-123h-393v-54q0 -43 2 -63h391v-123h-379 q25 -161 102 -252.5t215 -91.5q74 0 129 27.5t86.5 76.5t47.5 107t20 129h238q-21 -252 -151.5 -397.5t-367.5 -145.5q-235 0 -377.5 146t-181.5 401h-121z" />
<glyph unicode="&#x2122;" horiz-adv-x="1347" d="M66 1339v125h489v-125h-176v-440h-137v440h-176zM631 899v565h190l121 -385h2l125 385h193v-565h-134v406h-4l-123 -406h-116l-121 406h-4v-406h-129z" />
<glyph unicode="&#x25fc;" horiz-adv-x="1044" d="M0 0v1044h1044v-1044h-1044z" />
<hkern u1="&#x23;" u2="&#x39;" k="-6" />
<hkern u1="&#x23;" u2="&#x38;" k="6" />
<hkern u1="&#x23;" u2="&#x37;" k="-10" />
<hkern u1="&#x23;" u2="&#x35;" k="10" />
<hkern u1="&#x23;" u2="&#x34;" k="35" />
<hkern u1="&#x23;" u2="&#x31;" k="-6" />
<hkern u1="&#x24;" u2="&#x37;" k="16" />
<hkern u1="&#x26;" u2="x" k="-16" />
<hkern u1="&#x26;" u2="v" k="68" />
<hkern u1="&#x26;" u2="V" k="104" />
<hkern u1="&#x26;" u2="&#x39;" k="16" />
<hkern u1="&#x26;" u2="&#x38;" k="10" />
<hkern u1="&#x26;" u2="&#x37;" k="51" />
<hkern u1="&#x26;" u2="&#x36;" k="6" />
<hkern u1="&#x26;" u2="&#x35;" k="10" />
<hkern u1="&#x26;" u2="&#x34;" k="10" />
<hkern u1="&#x26;" u2="&#x33;" k="12" />
<hkern u1="&#x26;" u2="&#x32;" k="-16" />
<hkern u1="&#x26;" u2="&#x31;" k="82" />
<hkern u1="&#x26;" u2="&#x30;" k="6" />
<hkern u1="&#x28;" u2="x" k="16" />
<hkern u1="&#x28;" u2="v" k="31" />
<hkern u1="&#x28;" u2="V" k="-16" />
<hkern u1="&#x28;" u2="&#x34;" k="45" />
<hkern u1="&#x28;" u2="&#x31;" k="51" />
<hkern u1="&#x28;" u2="&#x30;" k="16" />
<hkern u1="&#x2a;" u2="x" k="16" />
<hkern u1="&#x2a;" u2="v" k="-31" />
<hkern u1="&#x2a;" u2="X" k="43" />
<hkern u1="&#x2a;" u2="&#x39;" k="16" />
<hkern u1="&#x2a;" u2="&#x38;" k="37" />
<hkern u1="&#x2a;" u2="&#x37;" k="-6" />
<hkern u1="&#x2a;" u2="&#x36;" k="41" />
<hkern u1="&#x2a;" u2="&#x35;" k="45" />
<hkern u1="&#x2a;" u2="&#x34;" k="178" />
<hkern u1="&#x2a;" u2="&#x33;" k="16" />
<hkern u1="&#x2a;" u2="&#x32;" k="16" />
<hkern u1="&#x2a;" u2="&#x30;" k="31" />
<hkern u1="&#x2c;" u2="j" k="-78" />
<hkern u1="&#x2c;" u2="g" k="14" />
<hkern u1="&#x2f;" u2="&#xdf;" k="55" />
<hkern u1="&#x2f;" u2="x" k="109" />
<hkern u1="&#x2f;" u2="v" k="68" />
<hkern u1="&#x2f;" u2="p" k="96" />
<hkern u1="&#x2f;" u2="V" k="-25" />
<hkern u1="&#x2f;" u2="&#x39;" k="66" />
<hkern u1="&#x2f;" u2="&#x38;" k="57" />
<hkern u1="&#x2f;" u2="&#x37;" k="-6" />
<hkern u1="&#x2f;" u2="&#x36;" k="88" />
<hkern u1="&#x2f;" u2="&#x35;" k="72" />
<hkern u1="&#x2f;" u2="&#x34;" k="164" />
<hkern u1="&#x2f;" u2="&#x33;" k="68" />
<hkern u1="&#x2f;" u2="&#x32;" k="68" />
<hkern u1="&#x2f;" u2="&#x31;" k="41" />
<hkern u1="&#x2f;" u2="&#x30;" k="82" />
<hkern u1="&#x2f;" u2="&#x2f;" k="266" />
<hkern u1="&#x30;" u2="&#x2122;" k="68" />
<hkern u1="&#x30;" u2="&#xb7;" k="-6" />
<hkern u1="&#x30;" u2="&#xb0;" k="41" />
<hkern u1="&#x30;" u2="^" k="-4" />
<hkern u1="&#x30;" u2="\" k="82" />
<hkern u1="&#x30;" u2="&#x39;" k="10" />
<hkern u1="&#x30;" u2="&#x38;" k="16" />
<hkern u1="&#x30;" u2="&#x37;" k="51" />
<hkern u1="&#x30;" u2="&#x35;" k="6" />
<hkern u1="&#x30;" u2="&#x32;" k="14" />
<hkern u1="&#x30;" u2="&#x31;" k="25" />
<hkern u1="&#x30;" u2="&#x2f;" k="61" />
<hkern u1="&#x30;" u2="&#x2a;" k="31" />
<hkern u1="&#x30;" u2="&#x29;" k="16" />
<hkern u1="&#x30;" u2="&#x26;" k="16" />
<hkern u1="&#x31;" u2="&#x40;" k="-16" />
<hkern u1="&#x32;" u2="&#x2122;" k="31" />
<hkern u1="&#x32;" u2="&#xa2;" k="16" />
<hkern u1="&#x32;" u2="^" k="4" />
<hkern u1="&#x32;" u2="\" k="55" />
<hkern u1="&#x32;" u2="&#x40;" k="-6" />
<hkern u1="&#x32;" u2="&#x3c;" k="16" />
<hkern u1="&#x32;" u2="&#x39;" k="-6" />
<hkern u1="&#x32;" u2="&#x34;" k="49" />
<hkern u1="&#x32;" u2="&#x32;" k="-6" />
<hkern u1="&#x32;" u2="&#x31;" k="-6" />
<hkern u1="&#x32;" u2="&#x30;" k="-4" />
<hkern u1="&#x32;" u2="&#x2f;" k="16" />
<hkern u1="&#x32;" u2="&#x26;" k="16" />
<hkern u1="&#x32;" u2="&#x23;" k="16" />
<hkern u1="&#x33;" u2="&#x2122;" k="41" />
<hkern u1="&#x33;" u2="&#xb7;" k="-6" />
<hkern u1="&#x33;" u2="&#xb0;" k="20" />
<hkern u1="&#x33;" u2="\" k="66" />
<hkern u1="&#x33;" u2="&#x40;" k="-6" />
<hkern u1="&#x33;" u2="&#x37;" k="31" />
<hkern u1="&#x33;" u2="&#x2f;" k="6" />
<hkern u1="&#x33;" u2="&#x2a;" k="25" />
<hkern u1="&#x34;" u2="&#x2122;" k="78" />
<hkern u1="&#x34;" u2="&#xb7;" k="-6" />
<hkern u1="&#x34;" u2="&#xb0;" k="78" />
<hkern u1="&#x34;" u2="&#xa2;" k="-6" />
<hkern u1="&#x34;" u2="^" k="-4" />
<hkern u1="&#x34;" u2="\" k="82" />
<hkern u1="&#x34;" u2="&#x40;" k="-6" />
<hkern u1="&#x34;" u2="&#x3f;" k="27" />
<hkern u1="&#x34;" u2="&#x3c;" k="16" />
<hkern u1="&#x34;" u2="&#x38;" k="-6" />
<hkern u1="&#x34;" u2="&#x37;" k="27" />
<hkern u1="&#x34;" u2="&#x34;" k="-6" />
<hkern u1="&#x34;" u2="&#x32;" k="6" />
<hkern u1="&#x34;" u2="&#x31;" k="57" />
<hkern u1="&#x34;" u2="&#x2a;" k="90" />
<hkern u1="&#x34;" u2="&#x26;" k="-6" />
<hkern u1="&#x34;" u2="&#x23;" k="-6" />
<hkern u1="&#x35;" u2="&#x2122;" k="16" />
<hkern u1="&#x35;" u2="&#xb7;" k="-6" />
<hkern u1="&#x35;" u2="&#xb0;" k="51" />
<hkern u1="&#x35;" u2="\" k="45" />
<hkern u1="&#x35;" u2="&#x3f;" k="10" />
<hkern u1="&#x35;" u2="&#x39;" k="16" />
<hkern u1="&#x35;" u2="&#x37;" k="20" />
<hkern u1="&#x35;" u2="&#x32;" k="6" />
<hkern u1="&#x35;" u2="&#x31;" k="51" />
<hkern u1="&#x35;" u2="&#x2f;" k="25" />
<hkern u1="&#x35;" u2="&#x2a;" k="31" />
<hkern u1="&#x36;" u2="&#x2122;" k="41" />
<hkern u1="&#x36;" u2="&#xb7;" k="-6" />
<hkern u1="&#x36;" u2="&#xb0;" k="25" />
<hkern u1="&#x36;" u2="\" k="66" />
<hkern u1="&#x36;" u2="&#x40;" k="-6" />
<hkern u1="&#x36;" u2="&#x3e;" k="25" />
<hkern u1="&#x36;" u2="&#x39;" k="16" />
<hkern u1="&#x36;" u2="&#x37;" k="25" />
<hkern u1="&#x36;" u2="&#x32;" k="20" />
<hkern u1="&#x36;" u2="&#x2f;" k="35" />
<hkern u1="&#x36;" u2="&#x2a;" k="25" />
<hkern u1="&#x37;" u2="&#x2122;" k="-31" />
<hkern u1="&#x37;" u2="&#xb7;" k="57" />
<hkern u1="&#x37;" u2="&#xb0;" k="-37" />
<hkern u1="&#x37;" u2="&#xae;" k="37" />
<hkern u1="&#x37;" u2="&#xa7;" k="41" />
<hkern u1="&#x37;" u2="&#xa2;" k="123" />
<hkern u1="&#x37;" u2="^" k="20" />
<hkern u1="&#x37;" u2="\" k="-20" />
<hkern u1="&#x37;" u2="&#x40;" k="47" />
<hkern u1="&#x37;" u2="&#x3f;" k="-20" />
<hkern u1="&#x37;" u2="&#x3d;" k="31" />
<hkern u1="&#x37;" u2="&#x3c;" k="82" />
<hkern u1="&#x37;" u2="&#x39;" k="10" />
<hkern u1="&#x37;" u2="&#x38;" k="16" />
<hkern u1="&#x37;" u2="&#x37;" k="-35" />
<hkern u1="&#x37;" u2="&#x36;" k="35" />
<hkern u1="&#x37;" u2="&#x35;" k="37" />
<hkern u1="&#x37;" u2="&#x34;" k="133" />
<hkern u1="&#x37;" u2="&#x33;" k="10" />
<hkern u1="&#x37;" u2="&#x32;" k="6" />
<hkern u1="&#x37;" u2="&#x30;" k="35" />
<hkern u1="&#x37;" u2="&#x2f;" k="164" />
<hkern u1="&#x37;" u2="&#x2a;" k="-20" />
<hkern u1="&#x37;" u2="&#x26;" k="78" />
<hkern u1="&#x37;" u2="&#x23;" k="66" />
<hkern u1="&#x37;" u2="&#x21;" k="-6" />
<hkern u1="&#x38;" u2="&#x2122;" k="61" />
<hkern u1="&#x38;" u2="&#xb7;" k="-20" />
<hkern u1="&#x38;" u2="&#xb0;" k="41" />
<hkern u1="&#x38;" u2="&#xae;" k="16" />
<hkern u1="&#x38;" u2="^" k="-4" />
<hkern u1="&#x38;" u2="\" k="57" />
<hkern u1="&#x38;" u2="&#x40;" k="-6" />
<hkern u1="&#x38;" u2="&#x3f;" k="6" />
<hkern u1="&#x38;" u2="&#x39;" k="16" />
<hkern u1="&#x38;" u2="&#x38;" k="-6" />
<hkern u1="&#x38;" u2="&#x37;" k="31" />
<hkern u1="&#x38;" u2="&#x36;" k="16" />
<hkern u1="&#x38;" u2="&#x34;" k="-6" />
<hkern u1="&#x38;" u2="&#x32;" k="6" />
<hkern u1="&#x38;" u2="&#x31;" k="25" />
<hkern u1="&#x38;" u2="&#x30;" k="16" />
<hkern u1="&#x38;" u2="&#x2f;" k="6" />
<hkern u1="&#x38;" u2="&#x2a;" k="37" />
<hkern u1="&#x38;" u2="&#x23;" k="-6" />
<hkern u1="&#x39;" u2="&#x2122;" k="41" />
<hkern u1="&#x39;" u2="&#xb7;" k="-6" />
<hkern u1="&#x39;" u2="&#xb0;" k="31" />
<hkern u1="&#x39;" u2="^" k="-4" />
<hkern u1="&#x39;" u2="\" k="78" />
<hkern u1="&#x39;" u2="&#x37;" k="31" />
<hkern u1="&#x39;" u2="&#x32;" k="20" />
<hkern u1="&#x39;" u2="&#x2f;" k="66" />
<hkern u1="&#x39;" u2="&#x2a;" k="16" />
<hkern u1="&#x3a;" u2="j" k="-27" />
<hkern u1="&#x3b;" u2="j" k="-78" />
<hkern u1="&#x3d;" u2="&#x37;" k="49" />
<hkern u1="&#x3d;" u2="&#x31;" k="16" />
<hkern u1="&#x3e;" u2="&#x39;" k="16" />
<hkern u1="&#x3e;" u2="&#x37;" k="68" />
<hkern u1="&#x3e;" u2="&#x32;" k="37" />
<hkern u1="&#x3e;" u2="&#x31;" k="51" />
<hkern u1="&#x40;" u2="x" k="35" />
<hkern u1="&#x40;" u2="v" k="-16" />
<hkern u1="&#x40;" u2="X" k="92" />
<hkern u1="&#x40;" u2="V" k="68" />
<hkern u1="&#x40;" u2="&#x38;" k="10" />
<hkern u1="&#x40;" u2="&#x37;" k="41" />
<hkern u1="&#x40;" u2="&#x36;" k="-6" />
<hkern u1="&#x40;" u2="&#x34;" k="-6" />
<hkern u1="&#x40;" u2="&#x32;" k="16" />
<hkern u1="&#x40;" u2="&#x31;" k="16" />
<hkern u1="B" u2="&#x2122;" k="16" />
<hkern u1="B" u2="x" k="27" />
<hkern u1="B" u2="v" k="6" />
<hkern u1="B" u2="^" k="-6" />
<hkern u1="B" u2="\" k="51" />
<hkern u1="B" u2="X" k="41" />
<hkern u1="B" u2="V" k="35" />
<hkern u1="B" u2="&#x40;" k="-20" />
<hkern u1="B" u2="&#x2f;" k="6" />
<hkern u1="B" u2="&#x29;" k="16" />
<hkern u1="F" u2="&#xdf;" k="31" />
<hkern u1="F" u2="&#xb7;" k="31" />
<hkern u1="F" u2="&#xae;" k="6" />
<hkern u1="F" u2="x" k="92" />
<hkern u1="F" u2="v" k="51" />
<hkern u1="F" u2="p" k="66" />
<hkern u1="F" u2="^" k="10" />
<hkern u1="F" u2="\" k="-6" />
<hkern u1="F" u2="X" k="6" />
<hkern u1="F" u2="V" k="-6" />
<hkern u1="F" u2="&#x40;" k="47" />
<hkern u1="F" u2="&#x3f;" k="-6" />
<hkern u1="F" u2="&#x2f;" k="137" />
<hkern u1="F" u2="&#x2a;" k="-16" />
<hkern u1="F" u2="&#x29;" k="-27" />
<hkern u1="F" u2="&#x26;" k="82" />
<hkern u1="P" u2="&#x2122;" k="-16" />
<hkern u1="P" u2="&#xae;" k="-16" />
<hkern u1="P" u2="x" k="6" />
<hkern u1="P" u2="v" k="-31" />
<hkern u1="P" u2="^" k="6" />
<hkern u1="P" u2="\" k="6" />
<hkern u1="P" u2="X" k="68" />
<hkern u1="P" u2="V" k="6" />
<hkern u1="P" u2="&#x3f;" k="-47" />
<hkern u1="P" u2="&#x2f;" k="143" />
<hkern u1="P" u2="&#x2a;" k="-16" />
<hkern u1="P" u2="&#x29;" k="6" />
<hkern u1="P" u2="&#x26;" k="66" />
<hkern u1="Q" u2="&#x2122;" k="66" />
<hkern u1="Q" u2="x" k="31" />
<hkern u1="Q" u2="\" k="88" />
<hkern u1="Q" u2="X" k="66" />
<hkern u1="Q" u2="V" k="72" />
<hkern u1="Q" u2="&#x2f;" k="25" />
<hkern u1="Q" u2="&#x2c;" k="23" />
<hkern u1="Q" u2="&#x2a;" k="31" />
<hkern u1="V" u2="&#x2122;" k="-25" />
<hkern u1="V" u2="&#xdf;" k="35" />
<hkern u1="V" u2="&#xb7;" k="82" />
<hkern u1="V" u2="&#xae;" k="57" />
<hkern u1="V" u2="x" k="72" />
<hkern u1="V" u2="v" k="45" />
<hkern u1="V" u2="p" k="57" />
<hkern u1="V" u2="^" k="23" />
<hkern u1="V" u2="\" k="-25" />
<hkern u1="V" u2="V" k="-35" />
<hkern u1="V" u2="&#x40;" k="88" />
<hkern u1="V" u2="&#x2f;" k="164" />
<hkern u1="V" u2="&#x29;" k="-16" />
<hkern u1="V" u2="&#x26;" k="88" />
<hkern u1="X" u2="&#xdf;" k="16" />
<hkern u1="X" u2="&#xb7;" k="102" />
<hkern u1="X" u2="&#xae;" k="92" />
<hkern u1="X" u2="v" k="84" />
<hkern u1="X" u2="^" k="16" />
<hkern u1="X" u2="X" k="-6" />
<hkern u1="X" u2="&#x40;" k="51" />
<hkern u1="X" u2="&#x3f;" k="37" />
<hkern u1="X" u2="&#x2f;" k="-6" />
<hkern u1="X" u2="&#x2a;" k="43" />
<hkern u1="X" u2="&#x26;" k="47" />
<hkern u1="\" u2="v" k="68" />
<hkern u1="\" u2="X" k="-6" />
<hkern u1="\" u2="V" k="164" />
<hkern u1="\" u2="\" k="266" />
<hkern u1="\" u2="&#x39;" k="47" />
<hkern u1="\" u2="&#x38;" k="6" />
<hkern u1="\" u2="&#x37;" k="98" />
<hkern u1="\" u2="&#x36;" k="57" />
<hkern u1="\" u2="&#x35;" k="6" />
<hkern u1="\" u2="&#x34;" k="25" />
<hkern u1="\" u2="&#x33;" k="6" />
<hkern u1="\" u2="&#x31;" k="119" />
<hkern u1="\" u2="&#x30;" k="61" />
<hkern u1="^" u2="x" k="12" />
<hkern u1="^" u2="v" k="6" />
<hkern u1="^" u2="X" k="16" />
<hkern u1="^" u2="V" k="23" />
<hkern u1="^" u2="&#x39;" k="4" />
<hkern u1="^" u2="&#x38;" k="-4" />
<hkern u1="^" u2="&#x37;" k="12" />
<hkern u1="^" u2="&#x34;" k="-4" />
<hkern u1="^" u2="&#x32;" k="4" />
<hkern u1="^" u2="&#x31;" k="23" />
<hkern u1="^" u2="&#x30;" k="-4" />
<hkern u1="q" u2="&#x2122;" k="57" />
<hkern u1="q" u2="\" k="119" />
<hkern u1="v" u2="&#xb7;" k="16" />
<hkern u1="v" u2="v" k="-35" />
<hkern u1="v" u2="^" k="6" />
<hkern u1="v" u2="\" k="68" />
<hkern u1="v" u2="&#x3f;" k="-66" />
<hkern u1="v" u2="&#x2f;" k="68" />
<hkern u1="v" u2="&#x2a;" k="-31" />
<hkern u1="v" u2="&#x29;" k="31" />
<hkern u1="v" u2="&#x26;" k="61" />
<hkern u1="x" u2="&#x2122;" k="41" />
<hkern u1="x" u2="&#xb7;" k="57" />
<hkern u1="x" u2="&#xae;" k="37" />
<hkern u1="x" u2="^" k="12" />
<hkern u1="x" u2="\" k="109" />
<hkern u1="x" u2="&#x40;" k="47" />
<hkern u1="x" u2="&#x2a;" k="16" />
<hkern u1="x" u2="&#x29;" k="16" />
<hkern u1="x" u2="&#x26;" k="61" />
<hkern u1="&#xa1;" u2="V" k="37" />
<hkern u1="&#xa1;" u2="&#x37;" k="16" />
<hkern u1="&#xa3;" u2="&#x37;" k="16" />
<hkern u1="&#xa3;" u2="&#x35;" k="6" />
<hkern u1="&#xa3;" u2="&#x34;" k="61" />
<hkern u1="&#xa5;" u2="&#x39;" k="37" />
<hkern u1="&#xa5;" u2="&#x38;" k="31" />
<hkern u1="&#xa5;" u2="&#x36;" k="51" />
<hkern u1="&#xa5;" u2="&#x35;" k="20" />
<hkern u1="&#xa5;" u2="&#x34;" k="20" />
<hkern u1="&#xa5;" u2="&#x33;" k="16" />
<hkern u1="&#xa5;" u2="&#x32;" k="37" />
<hkern u1="&#xa5;" u2="&#x31;" k="47" />
<hkern u1="&#xa5;" u2="&#x30;" k="37" />
<hkern u1="&#xa7;" u2="&#x37;" k="37" />
<hkern u1="&#xa7;" u2="&#x33;" k="-16" />
<hkern u1="&#xa7;" u2="&#x31;" k="37" />
<hkern u1="&#xae;" u2="x" k="6" />
<hkern u1="&#xae;" u2="X" k="16" />
<hkern u1="&#xae;" u2="V" k="10" />
<hkern u1="&#xae;" u2="&#x39;" k="4" />
<hkern u1="&#xae;" u2="&#x38;" k="4" />
<hkern u1="&#xae;" u2="&#x37;" k="6" />
<hkern u1="&#xae;" u2="&#x32;" k="4" />
<hkern u1="&#xae;" u2="&#x31;" k="4" />
<hkern u1="&#xb0;" u2="&#x39;" k="16" />
<hkern u1="&#xb0;" u2="&#x38;" k="41" />
<hkern u1="&#xb0;" u2="&#x37;" k="-6" />
<hkern u1="&#xb0;" u2="&#x36;" k="35" />
<hkern u1="&#xb0;" u2="&#x35;" k="51" />
<hkern u1="&#xb0;" u2="&#x34;" k="195" />
<hkern u1="&#xb0;" u2="&#x33;" k="6" />
<hkern u1="&#xb0;" u2="&#x32;" k="31" />
<hkern u1="&#xb0;" u2="&#x31;" k="-6" />
<hkern u1="&#xb0;" u2="&#x30;" k="41" />
<hkern u1="&#xb1;" u2="&#x3f;" k="-4" />
<hkern u1="&#xb7;" u2="x" k="57" />
<hkern u1="&#xb7;" u2="v" k="16" />
<hkern u1="&#xb7;" u2="X" k="102" />
<hkern u1="&#xb7;" u2="V" k="82" />
<hkern u1="&#xb7;" u2="&#x39;" k="-6" />
<hkern u1="&#xb7;" u2="&#x38;" k="-20" />
<hkern u1="&#xb7;" u2="&#x37;" k="47" />
<hkern u1="&#xb7;" u2="&#x36;" k="-6" />
<hkern u1="&#xb7;" u2="&#x34;" k="-6" />
<hkern u1="&#xb7;" u2="&#x33;" k="-6" />
<hkern u1="&#xb7;" u2="&#x31;" k="61" />
<hkern u1="&#xb7;" u2="&#x30;" k="-6" />
<hkern u1="&#xbf;" u2="v" k="109" />
<hkern u1="&#xbf;" u2="V" k="190" />
<hkern u1="&#xbf;" u2="&#x39;" k="12" />
<hkern u1="&#xbf;" u2="&#x37;" k="68" />
<hkern u1="&#xbf;" u2="&#x36;" k="37" />
<hkern u1="&#xbf;" u2="&#x35;" k="6" />
<hkern u1="&#xbf;" u2="&#x34;" k="16" />
<hkern u1="&#xbf;" u2="&#x33;" k="6" />
<hkern u1="&#xbf;" u2="&#x32;" k="-25" />
<hkern u1="&#xbf;" u2="&#x31;" k="106" />
<hkern u1="&#xbf;" u2="&#x30;" k="45" />
<hkern u1="&#xdf;" u2="&#x2122;" k="41" />
<hkern u1="&#xdf;" u2="x" k="27" />
<hkern u1="&#xdf;" u2="v" k="16" />
<hkern u1="&#xdf;" u2="^" k="-4" />
<hkern u1="&#xdf;" u2="\" k="76" />
<hkern u1="&#xdf;" u2="&#x2f;" k="20" />
<hkern u1="&#xdf;" u2="&#x2a;" k="6" />
<hkern g1="bracketleft,braceleft" 	g2="five" 	k="12" />
<hkern g1="bracketleft,braceleft" 	g2="four" 	k="37" />
<hkern g1="bracketleft,braceleft" 	g2="nine" 	k="6" />
<hkern g1="bracketleft,braceleft" 	g2="one" 	k="25" />
<hkern g1="bracketleft,braceleft" 	g2="three" 	k="6" />
<hkern g1="bracketleft,braceleft" 	g2="zero" 	k="10" />
<hkern g1="colon,semicolon" 	g2="four" 	k="-6" />
<hkern g1="colon,semicolon" 	g2="one" 	k="49" />
<hkern g1="colon,semicolon" 	g2="zero" 	k="-6" />
<hkern g1="colon,semicolon" 	g2="seven" 	k="16" />
<hkern g1="colon,semicolon" 	g2="two" 	k="-6" />
<hkern g1="comma,period,ellipsis" 	g2="four" 	k="49" />
<hkern g1="comma,period,ellipsis" 	g2="nine" 	k="31" />
<hkern g1="comma,period,ellipsis" 	g2="one" 	k="195" />
<hkern g1="comma,period,ellipsis" 	g2="zero" 	k="45" />
<hkern g1="comma,period,ellipsis" 	g2="seven" 	k="57" />
<hkern g1="comma,period,ellipsis" 	g2="six" 	k="57" />
<hkern g1="guilsinglleft" 	g2="one" 	k="61" />
<hkern g1="guilsinglleft" 	g2="seven" 	k="45" />
<hkern g1="guilsinglleft" 	g2="two" 	k="16" />
<hkern g1="guilsinglright" 	g2="four" 	k="-6" />
<hkern g1="guilsinglright" 	g2="nine" 	k="16" />
<hkern g1="guilsinglright" 	g2="one" 	k="121" />
<hkern g1="guilsinglright" 	g2="seven" 	k="78" />
<hkern g1="guilsinglright" 	g2="two" 	k="37" />
<hkern g1="guilsinglright" 	g2="eight" 	k="16" />
<hkern g1="hyphen,endash,emdash" 	g2="four" 	k="-6" />
<hkern g1="hyphen,endash,emdash" 	g2="nine" 	k="16" />
<hkern g1="hyphen,endash,emdash" 	g2="one" 	k="162" />
<hkern g1="hyphen,endash,emdash" 	g2="zero" 	k="-6" />
<hkern g1="hyphen,endash,emdash" 	g2="seven" 	k="78" />
<hkern g1="hyphen,endash,emdash" 	g2="two" 	k="20" />
<hkern g1="hyphen,endash,emdash" 	g2="eight" 	k="-16" />
<hkern g1="quotedbl,quotesingle" 	g2="five" 	k="16" />
<hkern g1="quotedbl,quotesingle" 	g2="four" 	k="143" />
<hkern g1="quotedbl,quotesingle" 	g2="nine" 	k="4" />
<hkern g1="quotedbl,quotesingle" 	g2="one" 	k="-14" />
<hkern g1="quotedbl,quotesingle" 	g2="three" 	k="-8" />
<hkern g1="quotedbl,quotesingle" 	g2="zero" 	k="16" />
<hkern g1="quotedbl,quotesingle" 	g2="seven" 	k="-16" />
<hkern g1="quotedbl,quotesingle" 	g2="two" 	k="6" />
<hkern g1="quotedbl,quotesingle" 	g2="six" 	k="16" />
<hkern g1="quotedbl,quotesingle" 	g2="eight" 	k="25" />
<hkern g1="quoteleft,quotedblleft" 	g2="five" 	k="25" />
<hkern g1="quoteleft,quotedblleft" 	g2="four" 	k="137" />
<hkern g1="quoteleft,quotedblleft" 	g2="nine" 	k="-6" />
<hkern g1="quoteleft,quotedblleft" 	g2="one" 	k="-35" />
<hkern g1="quoteleft,quotedblleft" 	g2="zero" 	k="31" />
<hkern g1="quoteleft,quotedblleft" 	g2="seven" 	k="-16" />
<hkern g1="quoteleft,quotedblleft" 	g2="six" 	k="16" />
<hkern g1="quoteleft,quotedblleft" 	g2="eight" 	k="25" />
<hkern g1="quoteright,quotedblright" 	g2="five" 	k="37" />
<hkern g1="quoteright,quotedblright" 	g2="four" 	k="193" />
<hkern g1="quoteright,quotedblright" 	g2="nine" 	k="16" />
<hkern g1="quoteright,quotedblright" 	g2="three" 	k="20" />
<hkern g1="quoteright,quotedblright" 	g2="zero" 	k="51" />
<hkern g1="quoteright,quotedblright" 	g2="seven" 	k="-25" />
<hkern g1="quoteright,quotedblright" 	g2="two" 	k="20" />
<hkern g1="quoteright,quotedblright" 	g2="six" 	k="51" />
<hkern g1="quoteright,quotedblright" 	g2="eight" 	k="37" />
<hkern g1="quotesinglbase,quotedblbase" 	g2="four" 	k="49" />
<hkern g1="quotesinglbase,quotedblbase" 	g2="nine" 	k="31" />
<hkern g1="quotesinglbase,quotedblbase" 	g2="one" 	k="195" />
<hkern g1="quotesinglbase,quotedblbase" 	g2="zero" 	k="45" />
<hkern g1="quotesinglbase,quotedblbase" 	g2="seven" 	k="57" />
<hkern g1="quotesinglbase,quotedblbase" 	g2="six" 	k="57" />
<hkern g1="copyright" 	g2="eight" 	k="16" />
<hkern g1="copyright" 	g2="nine" 	k="16" />
<hkern g1="copyright" 	g2="one" 	k="16" />
<hkern g1="copyright" 	g2="seven" 	k="51" />
<hkern g1="copyright" 	g2="two" 	k="20" />
<hkern g1="plus,divide" 	g2="eight" 	k="6" />
<hkern g1="plus,divide" 	g2="nine" 	k="16" />
<hkern g1="plus,divide" 	g2="one" 	k="49" />
<hkern g1="plus,divide" 	g2="seven" 	k="45" />
<hkern g1="plus,divide" 	g2="two" 	k="20" />
<hkern g1="eight" 	g2="copyright" 	k="16" />
<hkern g1="eight" 	g2="guilsinglleft" 	k="16" />
<hkern g1="eight" 	g2="hyphen,endash,emdash" 	k="-16" />
<hkern g1="eight" 	g2="plus,divide" 	k="6" />
<hkern g1="eight" 	g2="quotedbl,quotesingle" 	k="31" />
<hkern g1="eight" 	g2="quoteleft,quotedblleft" 	k="31" />
<hkern g1="eight" 	g2="quoteright,quotedblright" 	k="31" />
<hkern g1="five" 	g2="quotedbl,quotesingle" 	k="20" />
<hkern g1="five" 	g2="quoteleft,quotedblleft" 	k="41" />
<hkern g1="five" 	g2="quoteright,quotedblright" 	k="20" />
<hkern g1="five" 	g2="percent" 	k="25" />
<hkern g1="four" 	g2="guilsinglleft" 	k="-6" />
<hkern g1="four" 	g2="hyphen,endash,emdash" 	k="-6" />
<hkern g1="four" 	g2="plus,divide" 	k="16" />
<hkern g1="four" 	g2="quotedbl,quotesingle" 	k="41" />
<hkern g1="four" 	g2="quoteleft,quotedblleft" 	k="61" />
<hkern g1="four" 	g2="quoteright,quotedblright" 	k="41" />
<hkern g1="four" 	g2="percent" 	k="57" />
<hkern g1="four" 	g2="bracketright,braceright" 	k="-4" />
<hkern g1="four" 	g2="colon,semicolon" 	k="-6" />
<hkern g1="nine" 	g2="hyphen,endash,emdash" 	k="-6" />
<hkern g1="nine" 	g2="quotedbl,quotesingle" 	k="16" />
<hkern g1="nine" 	g2="quoteleft,quotedblleft" 	k="25" />
<hkern g1="nine" 	g2="quoteright,quotedblright" 	k="16" />
<hkern g1="nine" 	g2="bracketright,braceright" 	k="10" />
<hkern g1="nine" 	g2="comma,period,ellipsis" 	k="31" />
<hkern g1="nine" 	g2="quotesinglbase,quotedblbase" 	k="31" />
<hkern g1="seven" 	g2="copyright" 	k="37" />
<hkern g1="seven" 	g2="guilsinglleft" 	k="143" />
<hkern g1="seven" 	g2="hyphen,endash,emdash" 	k="123" />
<hkern g1="seven" 	g2="plus,divide" 	k="37" />
<hkern g1="seven" 	g2="quotedbl,quotesingle" 	k="-41" />
<hkern g1="seven" 	g2="quoteleft,quotedblleft" 	k="-31" />
<hkern g1="seven" 	g2="quoteright,quotedblright" 	k="-41" />
<hkern g1="seven" 	g2="percent" 	k="-41" />
<hkern g1="seven" 	g2="colon,semicolon" 	k="55" />
<hkern g1="seven" 	g2="comma,period,ellipsis" 	k="129" />
<hkern g1="seven" 	g2="quotesinglbase,quotedblbase" 	k="96" />
<hkern g1="seven" 	g2="guilsinglright" 	k="61" />
<hkern g1="six" 	g2="plus,divide" 	k="6" />
<hkern g1="six" 	g2="comma,period,ellipsis" 	k="16" />
<hkern g1="six" 	g2="quotesinglbase,quotedblbase" 	k="16" />
<hkern g1="three" 	g2="quoteleft,quotedblleft" 	k="16" />
<hkern g1="three" 	g2="percent" 	k="-6" />
<hkern g1="two" 	g2="guilsinglleft" 	k="49" />
<hkern g1="two" 	g2="hyphen,endash,emdash" 	k="31" />
<hkern g1="two" 	g2="percent" 	k="-57" />
<hkern g1="two" 	g2="colon,semicolon" 	k="-6" />
<hkern g1="zero" 	g2="hyphen,endash,emdash" 	k="-6" />
<hkern g1="zero" 	g2="quotedbl,quotesingle" 	k="16" />
<hkern g1="zero" 	g2="quoteleft,quotedblleft" 	k="31" />
<hkern g1="zero" 	g2="quoteright,quotedblright" 	k="16" />
<hkern g1="zero" 	g2="bracketright,braceright" 	k="10" />
<hkern g1="zero" 	g2="colon,semicolon" 	k="-6" />
<hkern g1="zero" 	g2="comma,period,ellipsis" 	k="45" />
<hkern g1="zero" 	g2="quotesinglbase,quotedblbase" 	k="41" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="-10" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="AE" 	k="-10" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="78" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="J" 	k="6" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="S" 	k="25" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="T" 	k="184" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	k="41" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="V" 	k="150" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="W" 	k="117" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="X" 	k="-6" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="Y,Yacute,Ydieresis" 	k="170" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="4" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="ampersand" 	k="27" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="asciicircum" 	k="6" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="asterisk" 	k="190" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="at" 	k="27" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="backslash" 	k="174" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="45" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="colon,semicolon" 	k="-20" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="comma,period,ellipsis" 	k="-35" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="copyright" 	k="72" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="d,q" 	k="37" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="f" 	k="31" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="g" 	k="37" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="guilsinglleft" 	k="51" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="guilsinglright" 	k="16" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="hyphen,endash,emdash" 	k="41" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="periodcentered" 	k="68" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="question" 	k="113" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="quotedbl,quotesingle" 	k="154" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="quoteleft,quotedblleft" 	k="160" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="quoteright,quotedblright" 	k="154" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="quotesinglbase,quotedblbase" 	k="-25" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="registered" 	k="72" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="s" 	k="14" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="slash" 	k="-27" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="t" 	k="57" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="trademark" 	k="170" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="u,ugrave,uacute,ucircumflex,udieresis" 	k="35" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="v" 	k="94" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="w" 	k="94" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="y,yacute,ydieresis" 	k="88" />
<hkern g1="C,Ccedilla" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="37" />
<hkern g1="C,Ccedilla" 	g2="AE" 	k="31" />
<hkern g1="C,Ccedilla" 	g2="J" 	k="-6" />
<hkern g1="C,Ccedilla" 	g2="T" 	k="45" />
<hkern g1="C,Ccedilla" 	g2="V" 	k="37" />
<hkern g1="C,Ccedilla" 	g2="W" 	k="25" />
<hkern g1="C,Ccedilla" 	g2="X" 	k="82" />
<hkern g1="C,Ccedilla" 	g2="Y,Yacute,Ydieresis" 	k="78" />
<hkern g1="C,Ccedilla" 	g2="asciicircum" 	k="-4" />
<hkern g1="C,Ccedilla" 	g2="backslash" 	k="37" />
<hkern g1="C,Ccedilla" 	g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="-6" />
<hkern g1="C,Ccedilla" 	g2="comma,period,ellipsis" 	k="41" />
<hkern g1="C,Ccedilla" 	g2="d,q" 	k="-6" />
<hkern g1="C,Ccedilla" 	g2="f" 	k="-6" />
<hkern g1="C,Ccedilla" 	g2="g" 	k="-6" />
<hkern g1="C,Ccedilla" 	g2="hyphen,endash,emdash" 	k="-20" />
<hkern g1="C,Ccedilla" 	g2="question" 	k="-25" />
<hkern g1="C,Ccedilla" 	g2="quotedbl,quotesingle" 	k="-16" />
<hkern g1="C,Ccedilla" 	g2="quoteleft,quotedblleft" 	k="-16" />
<hkern g1="C,Ccedilla" 	g2="quoteright,quotedblright" 	k="-16" />
<hkern g1="C,Ccedilla" 	g2="quotesinglbase,quotedblbase" 	k="41" />
<hkern g1="C,Ccedilla" 	g2="s" 	k="-4" />
<hkern g1="C,Ccedilla" 	g2="slash" 	k="45" />
<hkern g1="C,Ccedilla" 	g2="t" 	k="-6" />
<hkern g1="C,Ccedilla" 	g2="v" 	k="-6" />
<hkern g1="C,Ccedilla" 	g2="w" 	k="-6" />
<hkern g1="C,Ccedilla" 	g2="Z" 	k="16" />
<hkern g1="C,Ccedilla" 	g2="parenright" 	k="-10" />
<hkern g1="C,Ccedilla" 	g2="x" 	k="35" />
<hkern g1="C,Ccedilla" 	g2="z" 	k="31" />
<hkern g1="D,Eth" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="78" />
<hkern g1="D,Eth" 	g2="AE" 	k="92" />
<hkern g1="D,Eth" 	g2="J" 	k="16" />
<hkern g1="D,Eth" 	g2="T" 	k="82" />
<hkern g1="D,Eth" 	g2="V" 	k="72" />
<hkern g1="D,Eth" 	g2="W" 	k="61" />
<hkern g1="D,Eth" 	g2="X" 	k="123" />
<hkern g1="D,Eth" 	g2="Y,Yacute,Ydieresis" 	k="127" />
<hkern g1="D,Eth" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="16" />
<hkern g1="D,Eth" 	g2="asterisk" 	k="31" />
<hkern g1="D,Eth" 	g2="backslash" 	k="88" />
<hkern g1="D,Eth" 	g2="comma,period,ellipsis" 	k="92" />
<hkern g1="D,Eth" 	g2="quotedbl,quotesingle" 	k="25" />
<hkern g1="D,Eth" 	g2="quoteleft,quotedblleft" 	k="25" />
<hkern g1="D,Eth" 	g2="quoteright,quotedblright" 	k="25" />
<hkern g1="D,Eth" 	g2="quotesinglbase,quotedblbase" 	k="80" />
<hkern g1="D,Eth" 	g2="slash" 	k="88" />
<hkern g1="D,Eth" 	g2="t" 	k="-6" />
<hkern g1="D,Eth" 	g2="trademark" 	k="66" />
<hkern g1="D,Eth" 	g2="Z" 	k="57" />
<hkern g1="D,Eth" 	g2="x" 	k="35" />
<hkern g1="D,Eth" 	g2="z" 	k="27" />
<hkern g1="D,Eth" 	g2="j" 	k="-6" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="-16" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE" 	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="31" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE" 	g2="S" 	k="16" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE" 	g2="ampersand" 	k="16" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE" 	g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="37" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE" 	g2="colon,semicolon" 	k="-6" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE" 	g2="comma,period,ellipsis" 	k="-41" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE" 	g2="d,q" 	k="27" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE" 	g2="f" 	k="31" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE" 	g2="g" 	k="27" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE" 	g2="guilsinglleft" 	k="31" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE" 	g2="quoteleft,quotedblleft" 	k="6" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE" 	g2="quotesinglbase,quotedblbase" 	k="-31" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE" 	g2="s" 	k="16" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE" 	g2="t" 	k="31" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE" 	g2="u,ugrave,uacute,ucircumflex,udieresis" 	k="20" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE" 	g2="v" 	k="47" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE" 	g2="w" 	k="47" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE" 	g2="y,yacute,ydieresis" 	k="45" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE" 	g2="z" 	k="10" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE" 	g2="j" 	k="-6" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE" 	g2="germandbls" 	k="16" />
<hkern g1="F" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="133" />
<hkern g1="F" 	g2="AE" 	k="158" />
<hkern g1="F" 	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="41" />
<hkern g1="F" 	g2="J" 	k="131" />
<hkern g1="F" 	g2="S" 	k="27" />
<hkern g1="F" 	g2="T" 	k="-6" />
<hkern g1="F" 	g2="W" 	k="-10" />
<hkern g1="F" 	g2="Y,Yacute,Ydieresis" 	k="-6" />
<hkern g1="F" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="96" />
<hkern g1="F" 	g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="92" />
<hkern g1="F" 	g2="colon,semicolon" 	k="45" />
<hkern g1="F" 	g2="comma,period,ellipsis" 	k="164" />
<hkern g1="F" 	g2="copyright" 	k="6" />
<hkern g1="F" 	g2="d,q" 	k="82" />
<hkern g1="F" 	g2="f" 	k="20" />
<hkern g1="F" 	g2="g" 	k="82" />
<hkern g1="F" 	g2="guilsinglleft" 	k="82" />
<hkern g1="F" 	g2="guilsinglright" 	k="25" />
<hkern g1="F" 	g2="hyphen,endash,emdash" 	k="51" />
<hkern g1="F" 	g2="quotedbl,quotesingle" 	k="-27" />
<hkern g1="F" 	g2="quoteleft,quotedblleft" 	k="-20" />
<hkern g1="F" 	g2="quoteright,quotedblright" 	k="-27" />
<hkern g1="F" 	g2="quotesinglbase,quotedblbase" 	k="127" />
<hkern g1="F" 	g2="s" 	k="78" />
<hkern g1="F" 	g2="t" 	k="27" />
<hkern g1="F" 	g2="u,ugrave,uacute,ucircumflex,udieresis" 	k="66" />
<hkern g1="F" 	g2="w" 	k="51" />
<hkern g1="F" 	g2="y,yacute,ydieresis" 	k="51" />
<hkern g1="F" 	g2="z" 	k="72" />
<hkern g1="F" 	g2="bracketright,braceright" 	k="-37" />
<hkern g1="F" 	g2="m,n,r,ntilde" 	k="66" />
<hkern g1="G" 	g2="T" 	k="76" />
<hkern g1="G" 	g2="V" 	k="57" />
<hkern g1="G" 	g2="W" 	k="45" />
<hkern g1="G" 	g2="X" 	k="16" />
<hkern g1="G" 	g2="Y,Yacute,Ydieresis" 	k="94" />
<hkern g1="G" 	g2="asterisk" 	k="25" />
<hkern g1="G" 	g2="backslash" 	k="88" />
<hkern g1="G" 	g2="comma,period,ellipsis" 	k="-6" />
<hkern g1="G" 	g2="quotedbl,quotesingle" 	k="16" />
<hkern g1="G" 	g2="quoteleft,quotedblleft" 	k="16" />
<hkern g1="G" 	g2="quoteright,quotedblright" 	k="16" />
<hkern g1="G" 	g2="quotesinglbase,quotedblbase" 	k="-4" />
<hkern g1="G" 	g2="s" 	k="-4" />
<hkern g1="G" 	g2="trademark" 	k="45" />
<hkern g1="G" 	g2="m,n,r,ntilde" 	k="-4" />
<hkern g1="G" 	g2="b,h,i,k,l,igrave,iacute,icircumflex,idieresis" 	k="-4" />
<hkern g1="G" 	g2="p" 	k="-4" />
<hkern g1="J" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="16" />
<hkern g1="J" 	g2="AE" 	k="27" />
<hkern g1="J" 	g2="X" 	k="6" />
<hkern g1="J" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="4" />
<hkern g1="J" 	g2="comma,period,ellipsis" 	k="31" />
<hkern g1="J" 	g2="question" 	k="-16" />
<hkern g1="J" 	g2="quotesinglbase,quotedblbase" 	k="25" />
<hkern g1="J" 	g2="slash" 	k="37" />
<hkern g1="J" 	g2="x" 	k="6" />
<hkern g1="J" 	g2="z" 	k="20" />
<hkern g1="K" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="-6" />
<hkern g1="K" 	g2="AE" 	k="-6" />
<hkern g1="K" 	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="119" />
<hkern g1="K" 	g2="J" 	k="43" />
<hkern g1="K" 	g2="S" 	k="68" />
<hkern g1="K" 	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	k="10" />
<hkern g1="K" 	g2="V" 	k="-6" />
<hkern g1="K" 	g2="W" 	k="-6" />
<hkern g1="K" 	g2="X" 	k="-6" />
<hkern g1="K" 	g2="Y,Yacute,Ydieresis" 	k="-4" />
<hkern g1="K" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="27" />
<hkern g1="K" 	g2="ampersand" 	k="47" />
<hkern g1="K" 	g2="asciicircum" 	k="23" />
<hkern g1="K" 	g2="asterisk" 	k="57" />
<hkern g1="K" 	g2="at" 	k="47" />
<hkern g1="K" 	g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="88" />
<hkern g1="K" 	g2="comma,period,ellipsis" 	k="-25" />
<hkern g1="K" 	g2="copyright" 	k="72" />
<hkern g1="K" 	g2="d,q" 	k="78" />
<hkern g1="K" 	g2="f" 	k="47" />
<hkern g1="K" 	g2="g" 	k="78" />
<hkern g1="K" 	g2="guilsinglleft" 	k="143" />
<hkern g1="K" 	g2="guilsinglright" 	k="41" />
<hkern g1="K" 	g2="hyphen,endash,emdash" 	k="143" />
<hkern g1="K" 	g2="periodcentered" 	k="111" />
<hkern g1="K" 	g2="question" 	k="63" />
<hkern g1="K" 	g2="quotedbl,quotesingle" 	k="6" />
<hkern g1="K" 	g2="quoteleft,quotedblleft" 	k="43" />
<hkern g1="K" 	g2="quoteright,quotedblright" 	k="6" />
<hkern g1="K" 	g2="quotesinglbase,quotedblbase" 	k="-25" />
<hkern g1="K" 	g2="registered" 	k="72" />
<hkern g1="K" 	g2="s" 	k="41" />
<hkern g1="K" 	g2="slash" 	k="-20" />
<hkern g1="K" 	g2="t" 	k="72" />
<hkern g1="K" 	g2="u,ugrave,uacute,ucircumflex,udieresis" 	k="47" />
<hkern g1="K" 	g2="v" 	k="94" />
<hkern g1="K" 	g2="w" 	k="94" />
<hkern g1="K" 	g2="y,yacute,ydieresis" 	k="88" />
<hkern g1="K" 	g2="bracketright,braceright" 	k="-16" />
<hkern g1="L" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="-6" />
<hkern g1="L" 	g2="AE" 	k="-27" />
<hkern g1="L" 	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="80" />
<hkern g1="L" 	g2="J" 	k="-6" />
<hkern g1="L" 	g2="S" 	k="25" />
<hkern g1="L" 	g2="T" 	k="205" />
<hkern g1="L" 	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	k="37" />
<hkern g1="L" 	g2="V" 	k="174" />
<hkern g1="L" 	g2="W" 	k="139" />
<hkern g1="L" 	g2="Y,Yacute,Ydieresis" 	k="215" />
<hkern g1="L" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="-6" />
<hkern g1="L" 	g2="ampersand" 	k="-6" />
<hkern g1="L" 	g2="asciicircum" 	k="6" />
<hkern g1="L" 	g2="asterisk" 	k="205" />
<hkern g1="L" 	g2="at" 	k="25" />
<hkern g1="L" 	g2="backslash" 	k="201" />
<hkern g1="L" 	g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="31" />
<hkern g1="L" 	g2="colon,semicolon" 	k="-27" />
<hkern g1="L" 	g2="comma,period,ellipsis" 	k="-47" />
<hkern g1="L" 	g2="copyright" 	k="41" />
<hkern g1="L" 	g2="d,q" 	k="25" />
<hkern g1="L" 	g2="f" 	k="51" />
<hkern g1="L" 	g2="g" 	k="31" />
<hkern g1="L" 	g2="guilsinglleft" 	k="49" />
<hkern g1="L" 	g2="guilsinglright" 	k="-6" />
<hkern g1="L" 	g2="hyphen,endash,emdash" 	k="49" />
<hkern g1="L" 	g2="periodcentered" 	k="49" />
<hkern g1="L" 	g2="question" 	k="113" />
<hkern g1="L" 	g2="quotedbl,quotesingle" 	k="158" />
<hkern g1="L" 	g2="quoteleft,quotedblleft" 	k="164" />
<hkern g1="L" 	g2="quoteright,quotedblright" 	k="158" />
<hkern g1="L" 	g2="quotesinglbase,quotedblbase" 	k="-31" />
<hkern g1="L" 	g2="registered" 	k="41" />
<hkern g1="L" 	g2="s" 	k="10" />
<hkern g1="L" 	g2="slash" 	k="-20" />
<hkern g1="L" 	g2="t" 	k="51" />
<hkern g1="L" 	g2="trademark" 	k="180" />
<hkern g1="L" 	g2="u,ugrave,uacute,ucircumflex,udieresis" 	k="20" />
<hkern g1="L" 	g2="v" 	k="109" />
<hkern g1="L" 	g2="w" 	k="109" />
<hkern g1="L" 	g2="y,yacute,ydieresis" 	k="102" />
<hkern g1="L" 	g2="Z" 	k="-31" />
<hkern g1="L" 	g2="z" 	k="-6" />
<hkern g1="L" 	g2="j" 	k="-6" />
<hkern g1="L" 	g2="m,n,r,ntilde" 	k="-6" />
<hkern g1="L" 	g2="b,h,i,k,l,igrave,iacute,icircumflex,idieresis" 	k="-6" />
<hkern g1="L" 	g2="p" 	k="-6" />
<hkern g1="O,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="78" />
<hkern g1="O,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="AE" 	k="92" />
<hkern g1="O,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="J" 	k="16" />
<hkern g1="O,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="T" 	k="82" />
<hkern g1="O,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="V" 	k="72" />
<hkern g1="O,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="W" 	k="61" />
<hkern g1="O,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="X" 	k="123" />
<hkern g1="O,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="Y,Yacute,Ydieresis" 	k="127" />
<hkern g1="O,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="16" />
<hkern g1="O,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="asterisk" 	k="31" />
<hkern g1="O,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="backslash" 	k="88" />
<hkern g1="O,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="comma,period,ellipsis" 	k="92" />
<hkern g1="O,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="quotedbl,quotesingle" 	k="25" />
<hkern g1="O,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="quoteleft,quotedblleft" 	k="25" />
<hkern g1="O,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="quoteright,quotedblright" 	k="25" />
<hkern g1="O,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="quotesinglbase,quotedblbase" 	k="80" />
<hkern g1="O,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="slash" 	k="88" />
<hkern g1="O,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="t" 	k="-6" />
<hkern g1="O,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="trademark" 	k="66" />
<hkern g1="O,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="Z" 	k="57" />
<hkern g1="O,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="x" 	k="35" />
<hkern g1="O,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="z" 	k="27" />
<hkern g1="O,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="j" 	k="-6" />
<hkern g1="P" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="123" />
<hkern g1="P" 	g2="AE" 	k="133" />
<hkern g1="P" 	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="-20" />
<hkern g1="P" 	g2="J" 	k="113" />
<hkern g1="P" 	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	k="-20" />
<hkern g1="P" 	g2="W" 	k="-10" />
<hkern g1="P" 	g2="Y,Yacute,Ydieresis" 	k="16" />
<hkern g1="P" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="45" />
<hkern g1="P" 	g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="35" />
<hkern g1="P" 	g2="comma,period,ellipsis" 	k="195" />
<hkern g1="P" 	g2="copyright" 	k="-16" />
<hkern g1="P" 	g2="d,q" 	k="25" />
<hkern g1="P" 	g2="f" 	k="-31" />
<hkern g1="P" 	g2="g" 	k="25" />
<hkern g1="P" 	g2="guilsinglleft" 	k="57" />
<hkern g1="P" 	g2="hyphen,endash,emdash" 	k="51" />
<hkern g1="P" 	g2="quotedbl,quotesingle" 	k="-41" />
<hkern g1="P" 	g2="quoteleft,quotedblleft" 	k="-37" />
<hkern g1="P" 	g2="quoteright,quotedblright" 	k="-41" />
<hkern g1="P" 	g2="quotesinglbase,quotedblbase" 	k="160" />
<hkern g1="P" 	g2="s" 	k="16" />
<hkern g1="P" 	g2="t" 	k="-20" />
<hkern g1="P" 	g2="w" 	k="-31" />
<hkern g1="P" 	g2="y,yacute,ydieresis" 	k="-31" />
<hkern g1="P" 	g2="Z" 	k="31" />
<hkern g1="P" 	g2="z" 	k="4" />
<hkern g1="P" 	g2="bracketright,braceright" 	k="6" />
<hkern g1="Q" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="31" />
<hkern g1="Q" 	g2="AE" 	k="31" />
<hkern g1="Q" 	g2="J" 	k="16" />
<hkern g1="Q" 	g2="T" 	k="82" />
<hkern g1="Q" 	g2="W" 	k="61" />
<hkern g1="Q" 	g2="Y,Yacute,Ydieresis" 	k="127" />
<hkern g1="Q" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="16" />
<hkern g1="Q" 	g2="comma,period,ellipsis" 	k="82" />
<hkern g1="Q" 	g2="quotedbl,quotesingle" 	k="25" />
<hkern g1="Q" 	g2="quoteleft,quotedblleft" 	k="25" />
<hkern g1="Q" 	g2="quoteright,quotedblright" 	k="25" />
<hkern g1="Q" 	g2="quotesinglbase,quotedblbase" 	k="82" />
<hkern g1="Q" 	g2="t" 	k="-6" />
<hkern g1="Q" 	g2="Z" 	k="31" />
<hkern g1="Q" 	g2="z" 	k="25" />
<hkern g1="Q" 	g2="j" 	k="-6" />
<hkern g1="R" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="-6" />
<hkern g1="R" 	g2="AE" 	k="-6" />
<hkern g1="R" 	g2="T" 	k="25" />
<hkern g1="R" 	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	k="-16" />
<hkern g1="R" 	g2="V" 	k="6" />
<hkern g1="R" 	g2="W" 	k="6" />
<hkern g1="R" 	g2="Y,Yacute,Ydieresis" 	k="37" />
<hkern g1="R" 	g2="asciicircum" 	k="4" />
<hkern g1="R" 	g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="10" />
<hkern g1="R" 	g2="comma,period,ellipsis" 	k="-25" />
<hkern g1="R" 	g2="d,q" 	k="10" />
<hkern g1="R" 	g2="f" 	k="-6" />
<hkern g1="R" 	g2="g" 	k="10" />
<hkern g1="R" 	g2="guilsinglleft" 	k="25" />
<hkern g1="R" 	g2="hyphen,endash,emdash" 	k="16" />
<hkern g1="R" 	g2="question" 	k="-25" />
<hkern g1="R" 	g2="quotedbl,quotesingle" 	k="-6" />
<hkern g1="R" 	g2="quoteleft,quotedblleft" 	k="-6" />
<hkern g1="R" 	g2="quoteright,quotedblright" 	k="-6" />
<hkern g1="R" 	g2="quotesinglbase,quotedblbase" 	k="-16" />
<hkern g1="R" 	g2="slash" 	k="-6" />
<hkern g1="R" 	g2="t" 	k="-4" />
<hkern g1="R" 	g2="Z" 	k="-16" />
<hkern g1="R" 	g2="z" 	k="-6" />
<hkern g1="R" 	g2="j" 	k="-4" />
<hkern g1="R" 	g2="m,n,r,ntilde" 	k="-4" />
<hkern g1="R" 	g2="b,h,i,k,l,igrave,iacute,icircumflex,idieresis" 	k="-4" />
<hkern g1="R" 	g2="p" 	k="-4" />
<hkern g1="S" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="16" />
<hkern g1="S" 	g2="AE" 	k="6" />
<hkern g1="S" 	g2="J" 	k="-6" />
<hkern g1="S" 	g2="S" 	k="-6" />
<hkern g1="S" 	g2="T" 	k="66" />
<hkern g1="S" 	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	k="-16" />
<hkern g1="S" 	g2="V" 	k="45" />
<hkern g1="S" 	g2="W" 	k="35" />
<hkern g1="S" 	g2="X" 	k="47" />
<hkern g1="S" 	g2="Y,Yacute,Ydieresis" 	k="88" />
<hkern g1="S" 	g2="asciicircum" 	k="-4" />
<hkern g1="S" 	g2="asterisk" 	k="16" />
<hkern g1="S" 	g2="backslash" 	k="57" />
<hkern g1="S" 	g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="-4" />
<hkern g1="S" 	g2="comma,period,ellipsis" 	k="16" />
<hkern g1="S" 	g2="d,q" 	k="-4" />
<hkern g1="S" 	g2="f" 	k="6" />
<hkern g1="S" 	g2="g" 	k="-4" />
<hkern g1="S" 	g2="hyphen,endash,emdash" 	k="-20" />
<hkern g1="S" 	g2="question" 	k="6" />
<hkern g1="S" 	g2="quotedbl,quotesingle" 	k="35" />
<hkern g1="S" 	g2="quoteleft,quotedblleft" 	k="31" />
<hkern g1="S" 	g2="quoteright,quotedblright" 	k="35" />
<hkern g1="S" 	g2="quotesinglbase,quotedblbase" 	k="16" />
<hkern g1="S" 	g2="slash" 	k="6" />
<hkern g1="S" 	g2="t" 	k="6" />
<hkern g1="S" 	g2="trademark" 	k="41" />
<hkern g1="S" 	g2="v" 	k="27" />
<hkern g1="S" 	g2="w" 	k="27" />
<hkern g1="S" 	g2="y,yacute,ydieresis" 	k="10" />
<hkern g1="S" 	g2="Z" 	k="16" />
<hkern g1="S" 	g2="x" 	k="47" />
<hkern g1="S" 	g2="z" 	k="31" />
<hkern g1="S" 	g2="bracketright,braceright" 	k="6" />
<hkern g1="T" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="184" />
<hkern g1="T" 	g2="AE" 	k="184" />
<hkern g1="T" 	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="82" />
<hkern g1="T" 	g2="J" 	k="195" />
<hkern g1="T" 	g2="S" 	k="27" />
<hkern g1="T" 	g2="T" 	k="-31" />
<hkern g1="T" 	g2="V" 	k="-27" />
<hkern g1="T" 	g2="W" 	k="-27" />
<hkern g1="T" 	g2="X" 	k="6" />
<hkern g1="T" 	g2="Y,Yacute,Ydieresis" 	k="-25" />
<hkern g1="T" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="236" />
<hkern g1="T" 	g2="ampersand" 	k="106" />
<hkern g1="T" 	g2="asciicircum" 	k="27" />
<hkern g1="T" 	g2="at" 	k="111" />
<hkern g1="T" 	g2="backslash" 	k="-20" />
<hkern g1="T" 	g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="231" />
<hkern g1="T" 	g2="colon,semicolon" 	k="127" />
<hkern g1="T" 	g2="comma,period,ellipsis" 	k="190" />
<hkern g1="T" 	g2="copyright" 	k="78" />
<hkern g1="T" 	g2="d,q" 	k="195" />
<hkern g1="T" 	g2="f" 	k="57" />
<hkern g1="T" 	g2="g" 	k="195" />
<hkern g1="T" 	g2="guilsinglleft" 	k="205" />
<hkern g1="T" 	g2="guilsinglright" 	k="111" />
<hkern g1="T" 	g2="hyphen,endash,emdash" 	k="164" />
<hkern g1="T" 	g2="periodcentered" 	k="129" />
<hkern g1="T" 	g2="question" 	k="-6" />
<hkern g1="T" 	g2="quotedbl,quotesingle" 	k="-20" />
<hkern g1="T" 	g2="quoteleft,quotedblleft" 	k="-20" />
<hkern g1="T" 	g2="quoteright,quotedblright" 	k="-20" />
<hkern g1="T" 	g2="quotesinglbase,quotedblbase" 	k="141" />
<hkern g1="T" 	g2="registered" 	k="78" />
<hkern g1="T" 	g2="s" 	k="211" />
<hkern g1="T" 	g2="slash" 	k="174" />
<hkern g1="T" 	g2="t" 	k="51" />
<hkern g1="T" 	g2="u,ugrave,uacute,ucircumflex,udieresis" 	k="154" />
<hkern g1="T" 	g2="v" 	k="143" />
<hkern g1="T" 	g2="w" 	k="143" />
<hkern g1="T" 	g2="y,yacute,ydieresis" 	k="143" />
<hkern g1="T" 	g2="Z" 	k="4" />
<hkern g1="T" 	g2="parenright" 	k="-16" />
<hkern g1="T" 	g2="x" 	k="143" />
<hkern g1="T" 	g2="z" 	k="152" />
<hkern g1="T" 	g2="germandbls" 	k="37" />
<hkern g1="T" 	g2="bracketright,braceright" 	k="-25" />
<hkern g1="T" 	g2="m,n,r,ntilde" 	k="147" />
<hkern g1="T" 	g2="p" 	k="147" />
<hkern g1="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="41" />
<hkern g1="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	g2="AE" 	k="37" />
<hkern g1="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	g2="S" 	k="-16" />
<hkern g1="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	g2="V" 	k="-20" />
<hkern g1="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	g2="X" 	k="6" />
<hkern g1="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="6" />
<hkern g1="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	g2="comma,period,ellipsis" 	k="45" />
<hkern g1="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	g2="question" 	k="-16" />
<hkern g1="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	g2="quotesinglbase,quotedblbase" 	k="41" />
<hkern g1="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	g2="slash" 	k="41" />
<hkern g1="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	g2="Z" 	k="20" />
<hkern g1="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	g2="x" 	k="6" />
<hkern g1="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	g2="z" 	k="20" />
<hkern g1="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	g2="bracketright,braceright" 	k="-16" />
<hkern g1="V" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="150" />
<hkern g1="V" 	g2="AE" 	k="164" />
<hkern g1="V" 	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="72" />
<hkern g1="V" 	g2="J" 	k="143" />
<hkern g1="V" 	g2="S" 	k="25" />
<hkern g1="V" 	g2="T" 	k="-27" />
<hkern g1="V" 	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	k="-20" />
<hkern g1="V" 	g2="W" 	k="-35" />
<hkern g1="V" 	g2="Y,Yacute,Ydieresis" 	k="-27" />
<hkern g1="V" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="129" />
<hkern g1="V" 	g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="139" />
<hkern g1="V" 	g2="colon,semicolon" 	k="72" />
<hkern g1="V" 	g2="comma,period,ellipsis" 	k="180" />
<hkern g1="V" 	g2="copyright" 	k="57" />
<hkern g1="V" 	g2="d,q" 	k="113" />
<hkern g1="V" 	g2="f" 	k="37" />
<hkern g1="V" 	g2="g" 	k="113" />
<hkern g1="V" 	g2="guilsinglleft" 	k="133" />
<hkern g1="V" 	g2="guilsinglright" 	k="78" />
<hkern g1="V" 	g2="hyphen,endash,emdash" 	k="133" />
<hkern g1="V" 	g2="quotedbl,quotesingle" 	k="-20" />
<hkern g1="V" 	g2="quoteright,quotedblright" 	k="-20" />
<hkern g1="V" 	g2="quotesinglbase,quotedblbase" 	k="141" />
<hkern g1="V" 	g2="s" 	k="88" />
<hkern g1="V" 	g2="t" 	k="31" />
<hkern g1="V" 	g2="u,ugrave,uacute,ucircumflex,udieresis" 	k="66" />
<hkern g1="V" 	g2="w" 	k="45" />
<hkern g1="V" 	g2="y,yacute,ydieresis" 	k="12" />
<hkern g1="V" 	g2="Z" 	k="20" />
<hkern g1="V" 	g2="z" 	k="86" />
<hkern g1="V" 	g2="bracketright,braceright" 	k="-20" />
<hkern g1="V" 	g2="m,n,r,ntilde" 	k="57" />
<hkern g1="W" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="117" />
<hkern g1="W" 	g2="AE" 	k="139" />
<hkern g1="W" 	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="61" />
<hkern g1="W" 	g2="J" 	k="123" />
<hkern g1="W" 	g2="S" 	k="20" />
<hkern g1="W" 	g2="T" 	k="-27" />
<hkern g1="W" 	g2="V" 	k="-35" />
<hkern g1="W" 	g2="W" 	k="-27" />
<hkern g1="W" 	g2="Y,Yacute,Ydieresis" 	k="-27" />
<hkern g1="W" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="119" />
<hkern g1="W" 	g2="ampersand" 	k="98" />
<hkern g1="W" 	g2="asciicircum" 	k="16" />
<hkern g1="W" 	g2="at" 	k="72" />
<hkern g1="W" 	g2="backslash" 	k="-25" />
<hkern g1="W" 	g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="109" />
<hkern g1="W" 	g2="colon,semicolon" 	k="51" />
<hkern g1="W" 	g2="comma,period,ellipsis" 	k="143" />
<hkern g1="W" 	g2="copyright" 	k="55" />
<hkern g1="W" 	g2="d,q" 	k="106" />
<hkern g1="W" 	g2="f" 	k="20" />
<hkern g1="W" 	g2="g" 	k="106" />
<hkern g1="W" 	g2="guilsinglleft" 	k="113" />
<hkern g1="W" 	g2="guilsinglright" 	k="57" />
<hkern g1="W" 	g2="hyphen,endash,emdash" 	k="88" />
<hkern g1="W" 	g2="periodcentered" 	k="66" />
<hkern g1="W" 	g2="question" 	k="-16" />
<hkern g1="W" 	g2="quotedbl,quotesingle" 	k="-20" />
<hkern g1="W" 	g2="quoteright,quotedblright" 	k="-20" />
<hkern g1="W" 	g2="quotesinglbase,quotedblbase" 	k="111" />
<hkern g1="W" 	g2="registered" 	k="55" />
<hkern g1="W" 	g2="s" 	k="96" />
<hkern g1="W" 	g2="slash" 	k="143" />
<hkern g1="W" 	g2="t" 	k="25" />
<hkern g1="W" 	g2="u,ugrave,uacute,ucircumflex,udieresis" 	k="66" />
<hkern g1="W" 	g2="v" 	k="25" />
<hkern g1="W" 	g2="w" 	k="25" />
<hkern g1="W" 	g2="y,yacute,ydieresis" 	k="6" />
<hkern g1="W" 	g2="Z" 	k="6" />
<hkern g1="W" 	g2="parenright" 	k="-20" />
<hkern g1="W" 	g2="x" 	k="68" />
<hkern g1="W" 	g2="z" 	k="76" />
<hkern g1="W" 	g2="germandbls" 	k="27" />
<hkern g1="W" 	g2="bracketright,braceright" 	k="-20" />
<hkern g1="W" 	g2="m,n,r,ntilde" 	k="55" />
<hkern g1="W" 	g2="p" 	k="55" />
<hkern g1="X" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="-6" />
<hkern g1="X" 	g2="AE" 	k="-10" />
<hkern g1="X" 	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="123" />
<hkern g1="X" 	g2="J" 	k="31" />
<hkern g1="X" 	g2="S" 	k="47" />
<hkern g1="X" 	g2="T" 	k="6" />
<hkern g1="X" 	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	k="6" />
<hkern g1="X" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="27" />
<hkern g1="X" 	g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="82" />
<hkern g1="X" 	g2="comma,period,ellipsis" 	k="-6" />
<hkern g1="X" 	g2="copyright" 	k="92" />
<hkern g1="X" 	g2="d,q" 	k="68" />
<hkern g1="X" 	g2="f" 	k="27" />
<hkern g1="X" 	g2="g" 	k="51" />
<hkern g1="X" 	g2="guilsinglleft" 	k="102" />
<hkern g1="X" 	g2="guilsinglright" 	k="41" />
<hkern g1="X" 	g2="hyphen,endash,emdash" 	k="102" />
<hkern g1="X" 	g2="quotedbl,quotesingle" 	k="14" />
<hkern g1="X" 	g2="quoteleft,quotedblleft" 	k="43" />
<hkern g1="X" 	g2="quoteright,quotedblright" 	k="14" />
<hkern g1="X" 	g2="quotesinglbase,quotedblbase" 	k="-4" />
<hkern g1="X" 	g2="s" 	k="45" />
<hkern g1="X" 	g2="t" 	k="51" />
<hkern g1="X" 	g2="u,ugrave,uacute,ucircumflex,udieresis" 	k="47" />
<hkern g1="X" 	g2="w" 	k="84" />
<hkern g1="X" 	g2="y,yacute,ydieresis" 	k="29" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="170" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="AE" 	k="184" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="127" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="J" 	k="211" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="S" 	k="66" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="T" 	k="-25" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="V" 	k="-27" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="W" 	k="-27" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="Y,Yacute,Ydieresis" 	k="-27" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="211" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="ampersand" 	k="133" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="asciicircum" 	k="37" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="asterisk" 	k="27" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="at" 	k="150" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="backslash" 	k="-20" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="236" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="colon,semicolon" 	k="133" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="comma,period,ellipsis" 	k="215" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="copyright" 	k="119" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="d,q" 	k="197" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="f" 	k="78" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="g" 	k="197" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="guilsinglleft" 	k="215" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="guilsinglright" 	k="139" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="hyphen,endash,emdash" 	k="211" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="periodcentered" 	k="150" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="question" 	k="25" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="quotedbl,quotesingle" 	k="-20" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="quoteleft,quotedblleft" 	k="16" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="quoteright,quotedblright" 	k="-20" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="quotesinglbase,quotedblbase" 	k="160" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="registered" 	k="119" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="s" 	k="190" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="slash" 	k="201" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="t" 	k="76" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="trademark" 	k="-16" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="u,ugrave,uacute,ucircumflex,udieresis" 	k="133" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="v" 	k="102" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="w" 	k="102" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="y,yacute,ydieresis" 	k="102" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="Z" 	k="27" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="parenright" 	k="-20" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="x" 	k="133" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="z" 	k="150" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="j" 	k="16" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="germandbls" 	k="78" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="bracketright,braceright" 	k="-20" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="m,n,r,ntilde" 	k="119" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="b,h,i,k,l,igrave,iacute,icircumflex,idieresis" 	k="16" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="p" 	k="119" />
<hkern g1="Z" 	g2="AE" 	k="-6" />
<hkern g1="Z" 	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="82" />
<hkern g1="Z" 	g2="S" 	k="25" />
<hkern g1="Z" 	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	k="20" />
<hkern g1="Z" 	g2="V" 	k="16" />
<hkern g1="Z" 	g2="Y,Yacute,Ydieresis" 	k="20" />
<hkern g1="Z" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="16" />
<hkern g1="Z" 	g2="ampersand" 	k="41" />
<hkern g1="Z" 	g2="asciicircum" 	k="6" />
<hkern g1="Z" 	g2="asterisk" 	k="16" />
<hkern g1="Z" 	g2="at" 	k="31" />
<hkern g1="Z" 	g2="backslash" 	k="16" />
<hkern g1="Z" 	g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="51" />
<hkern g1="Z" 	g2="colon,semicolon" 	k="-16" />
<hkern g1="Z" 	g2="comma,period,ellipsis" 	k="-20" />
<hkern g1="Z" 	g2="copyright" 	k="61" />
<hkern g1="Z" 	g2="d,q" 	k="41" />
<hkern g1="Z" 	g2="f" 	k="37" />
<hkern g1="Z" 	g2="g" 	k="41" />
<hkern g1="Z" 	g2="guilsinglleft" 	k="61" />
<hkern g1="Z" 	g2="hyphen,endash,emdash" 	k="61" />
<hkern g1="Z" 	g2="periodcentered" 	k="49" />
<hkern g1="Z" 	g2="question" 	k="16" />
<hkern g1="Z" 	g2="quoteleft,quotedblleft" 	k="6" />
<hkern g1="Z" 	g2="quotesinglbase,quotedblbase" 	k="-16" />
<hkern g1="Z" 	g2="registered" 	k="61" />
<hkern g1="Z" 	g2="s" 	k="16" />
<hkern g1="Z" 	g2="t" 	k="45" />
<hkern g1="Z" 	g2="u,ugrave,uacute,ucircumflex,udieresis" 	k="31" />
<hkern g1="Z" 	g2="v" 	k="61" />
<hkern g1="Z" 	g2="w" 	k="61" />
<hkern g1="Z" 	g2="y,yacute,ydieresis" 	k="68" />
<hkern g1="Z" 	g2="z" 	k="16" />
<hkern g1="Z" 	g2="germandbls" 	k="16" />
<hkern g1="numbersign,dollar,percent,parenright,less,equal,greater,question,H,I,M,N,bracketright,asciicircum,underscore,grave,bar,braceright,asciitilde,cent,sterling,currency,yen,brokenbar,section,dieresis,logicalnot,registered,macron,degree,plusminus,acute,paragraph,cedilla,Igrave,Iacute,Icircumflex,Idieresis,Ntilde,multiply,Thorn,eth,circumflex,tilde,bullet,trademark" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="6" />
<hkern g1="numbersign,dollar,percent,parenright,less,equal,greater,question,H,I,M,N,bracketright,asciicircum,underscore,grave,bar,braceright,asciitilde,cent,sterling,currency,yen,brokenbar,section,dieresis,logicalnot,registered,macron,degree,plusminus,acute,paragraph,cedilla,Igrave,Iacute,Icircumflex,Idieresis,Ntilde,multiply,Thorn,eth,circumflex,tilde,bullet,trademark" 	g2="AE" 	k="20" />
<hkern g1="numbersign,dollar,percent,parenright,less,equal,greater,question,H,I,M,N,bracketright,asciicircum,underscore,grave,bar,braceright,asciitilde,cent,sterling,currency,yen,brokenbar,section,dieresis,logicalnot,registered,macron,degree,plusminus,acute,paragraph,cedilla,Igrave,Iacute,Icircumflex,Idieresis,Ntilde,multiply,Thorn,eth,circumflex,tilde,bullet,trademark" 	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="-10" />
<hkern g1="numbersign,dollar,percent,parenright,less,equal,greater,question,H,I,M,N,bracketright,asciicircum,underscore,grave,bar,braceright,asciitilde,cent,sterling,currency,yen,brokenbar,section,dieresis,logicalnot,registered,macron,degree,plusminus,acute,paragraph,cedilla,Igrave,Iacute,Icircumflex,Idieresis,Ntilde,multiply,Thorn,eth,circumflex,tilde,bullet,trademark" 	g2="J" 	k="-31" />
<hkern g1="numbersign,dollar,percent,parenright,less,equal,greater,question,H,I,M,N,bracketright,asciicircum,underscore,grave,bar,braceright,asciitilde,cent,sterling,currency,yen,brokenbar,section,dieresis,logicalnot,registered,macron,degree,plusminus,acute,paragraph,cedilla,Igrave,Iacute,Icircumflex,Idieresis,Ntilde,multiply,Thorn,eth,circumflex,tilde,bullet,trademark" 	g2="S" 	k="-6" />
<hkern g1="numbersign,dollar,percent,parenright,less,equal,greater,question,H,I,M,N,bracketright,asciicircum,underscore,grave,bar,braceright,asciitilde,cent,sterling,currency,yen,brokenbar,section,dieresis,logicalnot,registered,macron,degree,plusminus,acute,paragraph,cedilla,Igrave,Iacute,Icircumflex,Idieresis,Ntilde,multiply,Thorn,eth,circumflex,tilde,bullet,trademark" 	g2="T" 	k="45" />
<hkern g1="numbersign,dollar,percent,parenright,less,equal,greater,question,H,I,M,N,bracketright,asciicircum,underscore,grave,bar,braceright,asciitilde,cent,sterling,currency,yen,brokenbar,section,dieresis,logicalnot,registered,macron,degree,plusminus,acute,paragraph,cedilla,Igrave,Iacute,Icircumflex,Idieresis,Ntilde,multiply,Thorn,eth,circumflex,tilde,bullet,trademark" 	g2="W" 	k="27" />
<hkern g1="numbersign,dollar,percent,parenright,less,equal,greater,question,H,I,M,N,bracketright,asciicircum,underscore,grave,bar,braceright,asciitilde,cent,sterling,currency,yen,brokenbar,section,dieresis,logicalnot,registered,macron,degree,plusminus,acute,paragraph,cedilla,Igrave,Iacute,Icircumflex,Idieresis,Ntilde,multiply,Thorn,eth,circumflex,tilde,bullet,trademark" 	g2="Y,Yacute,Ydieresis" 	k="47" />
<hkern g1="numbersign,dollar,percent,parenright,less,equal,greater,question,H,I,M,N,bracketright,asciicircum,underscore,grave,bar,braceright,asciitilde,cent,sterling,currency,yen,brokenbar,section,dieresis,logicalnot,registered,macron,degree,plusminus,acute,paragraph,cedilla,Igrave,Iacute,Icircumflex,Idieresis,Ntilde,multiply,Thorn,eth,circumflex,tilde,bullet,trademark" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="-4" />
<hkern g1="numbersign,dollar,percent,parenright,less,equal,greater,question,H,I,M,N,bracketright,asciicircum,underscore,grave,bar,braceright,asciitilde,cent,sterling,currency,yen,brokenbar,section,dieresis,logicalnot,registered,macron,degree,plusminus,acute,paragraph,cedilla,Igrave,Iacute,Icircumflex,Idieresis,Ntilde,multiply,Thorn,eth,circumflex,tilde,bullet,trademark" 	g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="-20" />
<hkern g1="numbersign,dollar,percent,parenright,less,equal,greater,question,H,I,M,N,bracketright,asciicircum,underscore,grave,bar,braceright,asciitilde,cent,sterling,currency,yen,brokenbar,section,dieresis,logicalnot,registered,macron,degree,plusminus,acute,paragraph,cedilla,Igrave,Iacute,Icircumflex,Idieresis,Ntilde,multiply,Thorn,eth,circumflex,tilde,bullet,trademark" 	g2="colon,semicolon" 	k="-16" />
<hkern g1="numbersign,dollar,percent,parenright,less,equal,greater,question,H,I,M,N,bracketright,asciicircum,underscore,grave,bar,braceright,asciitilde,cent,sterling,currency,yen,brokenbar,section,dieresis,logicalnot,registered,macron,degree,plusminus,acute,paragraph,cedilla,Igrave,Iacute,Icircumflex,Idieresis,Ntilde,multiply,Thorn,eth,circumflex,tilde,bullet,trademark" 	g2="comma,period,ellipsis" 	k="-20" />
<hkern g1="numbersign,dollar,percent,parenright,less,equal,greater,question,H,I,M,N,bracketright,asciicircum,underscore,grave,bar,braceright,asciitilde,cent,sterling,currency,yen,brokenbar,section,dieresis,logicalnot,registered,macron,degree,plusminus,acute,paragraph,cedilla,Igrave,Iacute,Icircumflex,Idieresis,Ntilde,multiply,Thorn,eth,circumflex,tilde,bullet,trademark" 	g2="d,q" 	k="-20" />
<hkern g1="numbersign,dollar,percent,parenright,less,equal,greater,question,H,I,M,N,bracketright,asciicircum,underscore,grave,bar,braceright,asciitilde,cent,sterling,currency,yen,brokenbar,section,dieresis,logicalnot,registered,macron,degree,plusminus,acute,paragraph,cedilla,Igrave,Iacute,Icircumflex,Idieresis,Ntilde,multiply,Thorn,eth,circumflex,tilde,bullet,trademark" 	g2="g" 	k="-20" />
<hkern g1="numbersign,dollar,percent,parenright,less,equal,greater,question,H,I,M,N,bracketright,asciicircum,underscore,grave,bar,braceright,asciitilde,cent,sterling,currency,yen,brokenbar,section,dieresis,logicalnot,registered,macron,degree,plusminus,acute,paragraph,cedilla,Igrave,Iacute,Icircumflex,Idieresis,Ntilde,multiply,Thorn,eth,circumflex,tilde,bullet,trademark" 	g2="guilsinglleft" 	k="-6" />
<hkern g1="numbersign,dollar,percent,parenright,less,equal,greater,question,H,I,M,N,bracketright,asciicircum,underscore,grave,bar,braceright,asciitilde,cent,sterling,currency,yen,brokenbar,section,dieresis,logicalnot,registered,macron,degree,plusminus,acute,paragraph,cedilla,Igrave,Iacute,Icircumflex,Idieresis,Ntilde,multiply,Thorn,eth,circumflex,tilde,bullet,trademark" 	g2="guilsinglright" 	k="-16" />
<hkern g1="numbersign,dollar,percent,parenright,less,equal,greater,question,H,I,M,N,bracketright,asciicircum,underscore,grave,bar,braceright,asciitilde,cent,sterling,currency,yen,brokenbar,section,dieresis,logicalnot,registered,macron,degree,plusminus,acute,paragraph,cedilla,Igrave,Iacute,Icircumflex,Idieresis,Ntilde,multiply,Thorn,eth,circumflex,tilde,bullet,trademark" 	g2="hyphen,endash,emdash" 	k="-31" />
<hkern g1="numbersign,dollar,percent,parenright,less,equal,greater,question,H,I,M,N,bracketright,asciicircum,underscore,grave,bar,braceright,asciitilde,cent,sterling,currency,yen,brokenbar,section,dieresis,logicalnot,registered,macron,degree,plusminus,acute,paragraph,cedilla,Igrave,Iacute,Icircumflex,Idieresis,Ntilde,multiply,Thorn,eth,circumflex,tilde,bullet,trademark" 	g2="quotedbl,quotesingle" 	k="16" />
<hkern g1="numbersign,dollar,percent,parenright,less,equal,greater,question,H,I,M,N,bracketright,asciicircum,underscore,grave,bar,braceright,asciitilde,cent,sterling,currency,yen,brokenbar,section,dieresis,logicalnot,registered,macron,degree,plusminus,acute,paragraph,cedilla,Igrave,Iacute,Icircumflex,Idieresis,Ntilde,multiply,Thorn,eth,circumflex,tilde,bullet,trademark" 	g2="quoteleft,quotedblleft" 	k="20" />
<hkern g1="numbersign,dollar,percent,parenright,less,equal,greater,question,H,I,M,N,bracketright,asciicircum,underscore,grave,bar,braceright,asciitilde,cent,sterling,currency,yen,brokenbar,section,dieresis,logicalnot,registered,macron,degree,plusminus,acute,paragraph,cedilla,Igrave,Iacute,Icircumflex,Idieresis,Ntilde,multiply,Thorn,eth,circumflex,tilde,bullet,trademark" 	g2="quoteright,quotedblright" 	k="16" />
<hkern g1="numbersign,dollar,percent,parenright,less,equal,greater,question,H,I,M,N,bracketright,asciicircum,underscore,grave,bar,braceright,asciitilde,cent,sterling,currency,yen,brokenbar,section,dieresis,logicalnot,registered,macron,degree,plusminus,acute,paragraph,cedilla,Igrave,Iacute,Icircumflex,Idieresis,Ntilde,multiply,Thorn,eth,circumflex,tilde,bullet,trademark" 	g2="quotesinglbase,quotedblbase" 	k="-16" />
<hkern g1="numbersign,dollar,percent,parenright,less,equal,greater,question,H,I,M,N,bracketright,asciicircum,underscore,grave,bar,braceright,asciitilde,cent,sterling,currency,yen,brokenbar,section,dieresis,logicalnot,registered,macron,degree,plusminus,acute,paragraph,cedilla,Igrave,Iacute,Icircumflex,Idieresis,Ntilde,multiply,Thorn,eth,circumflex,tilde,bullet,trademark" 	g2="s" 	k="-4" />
<hkern g1="numbersign,dollar,percent,parenright,less,equal,greater,question,H,I,M,N,bracketright,asciicircum,underscore,grave,bar,braceright,asciitilde,cent,sterling,currency,yen,brokenbar,section,dieresis,logicalnot,registered,macron,degree,plusminus,acute,paragraph,cedilla,Igrave,Iacute,Icircumflex,Idieresis,Ntilde,multiply,Thorn,eth,circumflex,tilde,bullet,trademark" 	g2="t" 	k="4" />
<hkern g1="numbersign,dollar,percent,parenright,less,equal,greater,question,H,I,M,N,bracketright,asciicircum,underscore,grave,bar,braceright,asciitilde,cent,sterling,currency,yen,brokenbar,section,dieresis,logicalnot,registered,macron,degree,plusminus,acute,paragraph,cedilla,Igrave,Iacute,Icircumflex,Idieresis,Ntilde,multiply,Thorn,eth,circumflex,tilde,bullet,trademark" 	g2="w" 	k="6" />
<hkern g1="numbersign,dollar,percent,parenright,less,equal,greater,question,H,I,M,N,bracketright,asciicircum,underscore,grave,bar,braceright,asciitilde,cent,sterling,currency,yen,brokenbar,section,dieresis,logicalnot,registered,macron,degree,plusminus,acute,paragraph,cedilla,Igrave,Iacute,Icircumflex,Idieresis,Ntilde,multiply,Thorn,eth,circumflex,tilde,bullet,trademark" 	g2="y,yacute,ydieresis" 	k="6" />
<hkern g1="numbersign,dollar,percent,parenright,less,equal,greater,question,H,I,M,N,bracketright,asciicircum,underscore,grave,bar,braceright,asciitilde,cent,sterling,currency,yen,brokenbar,section,dieresis,logicalnot,registered,macron,degree,plusminus,acute,paragraph,cedilla,Igrave,Iacute,Icircumflex,Idieresis,Ntilde,multiply,Thorn,eth,circumflex,tilde,bullet,trademark" 	g2="z" 	k="25" />
<hkern g1="numbersign,dollar,percent,parenright,less,equal,greater,question,H,I,M,N,bracketright,asciicircum,underscore,grave,bar,braceright,asciitilde,cent,sterling,currency,yen,brokenbar,section,dieresis,logicalnot,registered,macron,degree,plusminus,acute,paragraph,cedilla,Igrave,Iacute,Icircumflex,Idieresis,Ntilde,multiply,Thorn,eth,circumflex,tilde,bullet,trademark" 	g2="bracketright,braceright" 	k="16" />
<hkern g1="a,agrave,aacute,acircumflex,atilde,adieresis,aring" 	g2="asterisk" 	k="66" />
<hkern g1="a,agrave,aacute,acircumflex,atilde,adieresis,aring" 	g2="backslash" 	k="154" />
<hkern g1="a,agrave,aacute,acircumflex,atilde,adieresis,aring" 	g2="comma,period,ellipsis" 	k="-27" />
<hkern g1="a,agrave,aacute,acircumflex,atilde,adieresis,aring" 	g2="question" 	k="31" />
<hkern g1="a,agrave,aacute,acircumflex,atilde,adieresis,aring" 	g2="quotedbl,quotesingle" 	k="49" />
<hkern g1="a,agrave,aacute,acircumflex,atilde,adieresis,aring" 	g2="quoteleft,quotedblleft" 	k="66" />
<hkern g1="a,agrave,aacute,acircumflex,atilde,adieresis,aring" 	g2="quoteright,quotedblright" 	k="49" />
<hkern g1="a,agrave,aacute,acircumflex,atilde,adieresis,aring" 	g2="quotesinglbase,quotedblbase" 	k="-25" />
<hkern g1="a,agrave,aacute,acircumflex,atilde,adieresis,aring" 	g2="t" 	k="20" />
<hkern g1="a,agrave,aacute,acircumflex,atilde,adieresis,aring" 	g2="trademark" 	k="98" />
<hkern g1="a,agrave,aacute,acircumflex,atilde,adieresis,aring" 	g2="v" 	k="20" />
<hkern g1="a,agrave,aacute,acircumflex,atilde,adieresis,aring" 	g2="w" 	k="20" />
<hkern g1="a,agrave,aacute,acircumflex,atilde,adieresis,aring" 	g2="y,yacute,ydieresis" 	k="27" />
<hkern g1="a,agrave,aacute,acircumflex,atilde,adieresis,aring" 	g2="colon,semicolon" 	k="-14" />
<hkern g1="a,agrave,aacute,acircumflex,atilde,adieresis,aring" 	g2="f" 	k="6" />
<hkern g1="a,agrave,aacute,acircumflex,atilde,adieresis,aring" 	g2="slash" 	k="-29" />
<hkern g1="b,p,thorn" 	g2="asterisk" 	k="72" />
<hkern g1="b,p,thorn" 	g2="backslash" 	k="143" />
<hkern g1="b,p,thorn" 	g2="comma,period,ellipsis" 	k="31" />
<hkern g1="b,p,thorn" 	g2="question" 	k="31" />
<hkern g1="b,p,thorn" 	g2="quotedbl,quotesingle" 	k="70" />
<hkern g1="b,p,thorn" 	g2="quoteleft,quotedblleft" 	k="72" />
<hkern g1="b,p,thorn" 	g2="quoteright,quotedblright" 	k="70" />
<hkern g1="b,p,thorn" 	g2="quotesinglbase,quotedblbase" 	k="31" />
<hkern g1="b,p,thorn" 	g2="t" 	k="31" />
<hkern g1="b,p,thorn" 	g2="trademark" 	k="88" />
<hkern g1="b,p,thorn" 	g2="v" 	k="25" />
<hkern g1="b,p,thorn" 	g2="w" 	k="25" />
<hkern g1="b,p,thorn" 	g2="y,yacute,ydieresis" 	k="27" />
<hkern g1="b,p,thorn" 	g2="f" 	k="20" />
<hkern g1="b,p,thorn" 	g2="slash" 	k="20" />
<hkern g1="b,p,thorn" 	g2="at" 	k="-16" />
<hkern g1="b,p,thorn" 	g2="bracketright,braceright" 	k="27" />
<hkern g1="b,p,thorn" 	g2="parenright" 	k="20" />
<hkern g1="b,p,thorn" 	g2="x" 	k="57" />
<hkern g1="b,p,thorn" 	g2="z" 	k="37" />
<hkern g1="c,ccedilla" 	g2="asterisk" 	k="31" />
<hkern g1="c,ccedilla" 	g2="backslash" 	k="123" />
<hkern g1="c,ccedilla" 	g2="question" 	k="27" />
<hkern g1="c,ccedilla" 	g2="quotedbl,quotesingle" 	k="31" />
<hkern g1="c,ccedilla" 	g2="quoteleft,quotedblleft" 	k="25" />
<hkern g1="c,ccedilla" 	g2="quoteright,quotedblright" 	k="31" />
<hkern g1="c,ccedilla" 	g2="trademark" 	k="63" />
<hkern g1="c,ccedilla" 	g2="v" 	k="6" />
<hkern g1="c,ccedilla" 	g2="w" 	k="6" />
<hkern g1="c,ccedilla" 	g2="y,yacute,ydieresis" 	k="6" />
<hkern g1="c,ccedilla" 	g2="f" 	k="-4" />
<hkern g1="c,ccedilla" 	g2="slash" 	k="-16" />
<hkern g1="c,ccedilla" 	g2="at" 	k="-20" />
<hkern g1="c,ccedilla" 	g2="bracketright,braceright" 	k="10" />
<hkern g1="c,ccedilla" 	g2="x" 	k="37" />
<hkern g1="c,ccedilla" 	g2="z" 	k="10" />
<hkern g1="c,ccedilla" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="20" />
<hkern g1="c,ccedilla" 	g2="AE" 	k="10" />
<hkern g1="c,ccedilla" 	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="-6" />
<hkern g1="c,ccedilla" 	g2="J" 	k="-25" />
<hkern g1="c,ccedilla" 	g2="S" 	k="-14" />
<hkern g1="c,ccedilla" 	g2="T" 	k="184" />
<hkern g1="c,ccedilla" 	g2="V" 	k="109" />
<hkern g1="c,ccedilla" 	g2="W" 	k="72" />
<hkern g1="c,ccedilla" 	g2="X" 	k="37" />
<hkern g1="c,ccedilla" 	g2="Y,Yacute,Ydieresis" 	k="180" />
<hkern g1="c,ccedilla" 	g2="Z" 	k="-6" />
<hkern g1="c,ccedilla" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="-20" />
<hkern g1="c,ccedilla" 	g2="s" 	k="-16" />
<hkern g1="d,i,l,igrave,iacute,icircumflex,idieresis" 	g2="question" 	k="-16" />
<hkern g1="d,i,l,igrave,iacute,icircumflex,idieresis" 	g2="b,h,i,k,l,igrave,iacute,icircumflex,idieresis" 	k="16" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe" 	g2="asterisk" 	k="78" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe" 	g2="backslash" 	k="143" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe" 	g2="comma,period,ellipsis" 	k="16" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe" 	g2="question" 	k="51" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe" 	g2="quotedbl,quotesingle" 	k="51" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe" 	g2="quoteleft,quotedblleft" 	k="61" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe" 	g2="quoteright,quotedblright" 	k="51" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe" 	g2="quotesinglbase,quotedblbase" 	k="16" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe" 	g2="t" 	k="20" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe" 	g2="trademark" 	k="109" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe" 	g2="v" 	k="25" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe" 	g2="w" 	k="25" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe" 	g2="y,yacute,ydieresis" 	k="27" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe" 	g2="f" 	k="20" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe" 	g2="slash" 	k="6" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe" 	g2="at" 	k="-16" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe" 	g2="bracketright,braceright" 	k="10" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe" 	g2="x" 	k="57" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe" 	g2="z" 	k="35" />
<hkern g1="f" 	g2="asterisk" 	k="-27" />
<hkern g1="f" 	g2="comma,period,ellipsis" 	k="88" />
<hkern g1="f" 	g2="question" 	k="-41" />
<hkern g1="f" 	g2="quotedbl,quotesingle" 	k="-35" />
<hkern g1="f" 	g2="quoteleft,quotedblleft" 	k="-25" />
<hkern g1="f" 	g2="quoteright,quotedblright" 	k="-35" />
<hkern g1="f" 	g2="quotesinglbase,quotedblbase" 	k="61" />
<hkern g1="f" 	g2="t" 	k="14" />
<hkern g1="f" 	g2="trademark" 	k="-31" />
<hkern g1="f" 	g2="v" 	k="-27" />
<hkern g1="f" 	g2="w" 	k="-27" />
<hkern g1="f" 	g2="y,yacute,ydieresis" 	k="-27" />
<hkern g1="f" 	g2="colon,semicolon" 	k="10" />
<hkern g1="f" 	g2="f" 	k="16" />
<hkern g1="f" 	g2="slash" 	k="82" />
<hkern g1="f" 	g2="bracketright,braceright" 	k="-31" />
<hkern g1="f" 	g2="parenright" 	k="-25" />
<hkern g1="f" 	g2="x" 	k="4" />
<hkern g1="f" 	g2="z" 	k="27" />
<hkern g1="f" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="31" />
<hkern g1="f" 	g2="s" 	k="20" />
<hkern g1="f" 	g2="ampersand" 	k="57" />
<hkern g1="f" 	g2="asciicircum" 	k="4" />
<hkern g1="f" 	g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="31" />
<hkern g1="f" 	g2="d,q" 	k="37" />
<hkern g1="f" 	g2="g" 	k="37" />
<hkern g1="f" 	g2="guilsinglleft" 	k="61" />
<hkern g1="f" 	g2="hyphen,endash,emdash" 	k="31" />
<hkern g1="f" 	g2="periodcentered" 	k="25" />
<hkern g1="g" 	g2="backslash" 	k="119" />
<hkern g1="g" 	g2="trademark" 	k="57" />
<hkern g1="g" 	g2="x" 	k="4" />
<hkern g1="g" 	g2="j" 	k="-47" />
<hkern g1="germandbls" 	g2="quotedbl,quotesingle" 	k="31" />
<hkern g1="germandbls" 	g2="quoteleft,quotedblleft" 	k="25" />
<hkern g1="germandbls" 	g2="quoteright,quotedblright" 	k="31" />
<hkern g1="germandbls" 	g2="t" 	k="25" />
<hkern g1="germandbls" 	g2="w" 	k="16" />
<hkern g1="germandbls" 	g2="y,yacute,ydieresis" 	k="6" />
<hkern g1="germandbls" 	g2="f" 	k="6" />
<hkern g1="germandbls" 	g2="z" 	k="20" />
<hkern g1="germandbls" 	g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="-16" />
<hkern g1="germandbls" 	g2="hyphen,endash,emdash" 	k="-16" />
<hkern g1="h,m,n,ntilde" 	g2="asterisk" 	k="41" />
<hkern g1="h,m,n,ntilde" 	g2="backslash" 	k="133" />
<hkern g1="h,m,n,ntilde" 	g2="question" 	k="27" />
<hkern g1="h,m,n,ntilde" 	g2="quotedbl,quotesingle" 	k="41" />
<hkern g1="h,m,n,ntilde" 	g2="quoteleft,quotedblleft" 	k="37" />
<hkern g1="h,m,n,ntilde" 	g2="quoteright,quotedblright" 	k="41" />
<hkern g1="h,m,n,ntilde" 	g2="t" 	k="20" />
<hkern g1="h,m,n,ntilde" 	g2="trademark" 	k="98" />
<hkern g1="h,m,n,ntilde" 	g2="v" 	k="20" />
<hkern g1="h,m,n,ntilde" 	g2="w" 	k="20" />
<hkern g1="h,m,n,ntilde" 	g2="y,yacute,ydieresis" 	k="20" />
<hkern g1="h,m,n,ntilde" 	g2="f" 	k="14" />
<hkern g1="j" 	g2="j" 	k="-51" />
<hkern g1="k" 	g2="backslash" 	k="78" />
<hkern g1="k" 	g2="comma,period,ellipsis" 	k="-16" />
<hkern g1="k" 	g2="question" 	k="-16" />
<hkern g1="k" 	g2="quotedbl,quotesingle" 	k="6" />
<hkern g1="k" 	g2="quoteleft,quotedblleft" 	k="6" />
<hkern g1="k" 	g2="quoteright,quotedblright" 	k="6" />
<hkern g1="k" 	g2="quotesinglbase,quotedblbase" 	k="-16" />
<hkern g1="k" 	g2="t" 	k="20" />
<hkern g1="k" 	g2="trademark" 	k="57" />
<hkern g1="k" 	g2="f" 	k="6" />
<hkern g1="k" 	g2="slash" 	k="-16" />
<hkern g1="k" 	g2="at" 	k="27" />
<hkern g1="k" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="27" />
<hkern g1="k" 	g2="s" 	k="35" />
<hkern g1="k" 	g2="ampersand" 	k="47" />
<hkern g1="k" 	g2="asciicircum" 	k="16" />
<hkern g1="k" 	g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="68" />
<hkern g1="k" 	g2="d,q" 	k="68" />
<hkern g1="k" 	g2="g" 	k="68" />
<hkern g1="k" 	g2="guilsinglleft" 	k="92" />
<hkern g1="k" 	g2="hyphen,endash,emdash" 	k="102" />
<hkern g1="k" 	g2="periodcentered" 	k="78" />
<hkern g1="k" 	g2="copyright" 	k="31" />
<hkern g1="k" 	g2="guilsinglright" 	k="16" />
<hkern g1="k" 	g2="registered" 	k="31" />
<hkern g1="k" 	g2="u,ugrave,uacute,ucircumflex,udieresis" 	k="31" />
<hkern g1="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash" 	g2="asterisk" 	k="78" />
<hkern g1="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash" 	g2="backslash" 	k="164" />
<hkern g1="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash" 	g2="comma,period,ellipsis" 	k="45" />
<hkern g1="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash" 	g2="question" 	k="57" />
<hkern g1="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash" 	g2="quotedbl,quotesingle" 	k="70" />
<hkern g1="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash" 	g2="quoteleft,quotedblleft" 	k="80" />
<hkern g1="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash" 	g2="quoteright,quotedblright" 	k="70" />
<hkern g1="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash" 	g2="quotesinglbase,quotedblbase" 	k="41" />
<hkern g1="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash" 	g2="t" 	k="20" />
<hkern g1="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash" 	g2="trademark" 	k="139" />
<hkern g1="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash" 	g2="v" 	k="27" />
<hkern g1="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash" 	g2="w" 	k="27" />
<hkern g1="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash" 	g2="y,yacute,ydieresis" 	k="27" />
<hkern g1="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash" 	g2="f" 	k="20" />
<hkern g1="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash" 	g2="slash" 	k="20" />
<hkern g1="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash" 	g2="at" 	k="-16" />
<hkern g1="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash" 	g2="bracketright,braceright" 	k="27" />
<hkern g1="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash" 	g2="parenright" 	k="16" />
<hkern g1="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash" 	g2="x" 	k="78" />
<hkern g1="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash" 	g2="z" 	k="37" />
<hkern g1="q" 	g2="j" 	k="-63" />
<hkern g1="r" 	g2="asterisk" 	k="-47" />
<hkern g1="r" 	g2="backslash" 	k="51" />
<hkern g1="r" 	g2="comma,period,ellipsis" 	k="127" />
<hkern g1="r" 	g2="question" 	k="-41" />
<hkern g1="r" 	g2="quotedbl,quotesingle" 	k="-41" />
<hkern g1="r" 	g2="quoteleft,quotedblleft" 	k="-41" />
<hkern g1="r" 	g2="quoteright,quotedblright" 	k="-41" />
<hkern g1="r" 	g2="quotesinglbase,quotedblbase" 	k="96" />
<hkern g1="r" 	g2="t" 	k="-6" />
<hkern g1="r" 	g2="v" 	k="-10" />
<hkern g1="r" 	g2="w" 	k="-10" />
<hkern g1="r" 	g2="y,yacute,ydieresis" 	k="-6" />
<hkern g1="r" 	g2="colon,semicolon" 	k="-6" />
<hkern g1="r" 	g2="f" 	k="-6" />
<hkern g1="r" 	g2="slash" 	k="98" />
<hkern g1="r" 	g2="bracketright,braceright" 	k="6" />
<hkern g1="r" 	g2="parenright" 	k="16" />
<hkern g1="r" 	g2="x" 	k="6" />
<hkern g1="r" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="37" />
<hkern g1="r" 	g2="ampersand" 	k="51" />
<hkern g1="r" 	g2="asciicircum" 	k="4" />
<hkern g1="r" 	g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="35" />
<hkern g1="r" 	g2="d,q" 	k="35" />
<hkern g1="r" 	g2="g" 	k="35" />
<hkern g1="r" 	g2="guilsinglleft" 	k="41" />
<hkern g1="r" 	g2="hyphen,endash,emdash" 	k="31" />
<hkern g1="r" 	g2="periodcentered" 	k="8" />
<hkern g1="r" 	g2="copyright" 	k="-25" />
<hkern g1="r" 	g2="registered" 	k="-25" />
<hkern g1="s" 	g2="asterisk" 	k="57" />
<hkern g1="s" 	g2="backslash" 	k="164" />
<hkern g1="s" 	g2="comma,period,ellipsis" 	k="-4" />
<hkern g1="s" 	g2="question" 	k="27" />
<hkern g1="s" 	g2="quotedbl,quotesingle" 	k="49" />
<hkern g1="s" 	g2="quoteleft,quotedblleft" 	k="51" />
<hkern g1="s" 	g2="quoteright,quotedblright" 	k="49" />
<hkern g1="s" 	g2="t" 	k="25" />
<hkern g1="s" 	g2="trademark" 	k="82" />
<hkern g1="s" 	g2="v" 	k="16" />
<hkern g1="s" 	g2="w" 	k="16" />
<hkern g1="s" 	g2="y,yacute,ydieresis" 	k="16" />
<hkern g1="s" 	g2="colon,semicolon" 	k="-4" />
<hkern g1="s" 	g2="f" 	k="20" />
<hkern g1="s" 	g2="x" 	k="27" />
<hkern g1="s" 	g2="z" 	k="16" />
<hkern g1="s" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="-10" />
<hkern g1="s" 	g2="s" 	k="-4" />
<hkern g1="s" 	g2="asciicircum" 	k="-4" />
<hkern g1="s" 	g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="-16" />
<hkern g1="s" 	g2="d,q" 	k="-16" />
<hkern g1="s" 	g2="g" 	k="-16" />
<hkern g1="s" 	g2="guilsinglleft" 	k="16" />
<hkern g1="s" 	g2="hyphen,endash,emdash" 	k="-16" />
<hkern g1="t" 	g2="asterisk" 	k="-6" />
<hkern g1="t" 	g2="backslash" 	k="55" />
<hkern g1="t" 	g2="comma,period,ellipsis" 	k="-20" />
<hkern g1="t" 	g2="question" 	k="-16" />
<hkern g1="t" 	g2="quotedbl,quotesingle" 	k="-6" />
<hkern g1="t" 	g2="quoteleft,quotedblleft" 	k="-6" />
<hkern g1="t" 	g2="quoteright,quotedblright" 	k="-6" />
<hkern g1="t" 	g2="quotesinglbase,quotedblbase" 	k="-16" />
<hkern g1="t" 	g2="t" 	k="25" />
<hkern g1="t" 	g2="v" 	k="-27" />
<hkern g1="t" 	g2="w" 	k="-27" />
<hkern g1="t" 	g2="y,yacute,ydieresis" 	k="-10" />
<hkern g1="t" 	g2="colon,semicolon" 	k="-6" />
<hkern g1="t" 	g2="f" 	k="25" />
<hkern g1="t" 	g2="slash" 	k="-6" />
<hkern g1="t" 	g2="parenright" 	k="-6" />
<hkern g1="t" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="-6" />
<hkern g1="t" 	g2="s" 	k="-6" />
<hkern g1="t" 	g2="ampersand" 	k="10" />
<hkern g1="t" 	g2="asciicircum" 	k="4" />
<hkern g1="t" 	g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="10" />
<hkern g1="t" 	g2="d,q" 	k="10" />
<hkern g1="t" 	g2="g" 	k="10" />
<hkern g1="t" 	g2="guilsinglleft" 	k="25" />
<hkern g1="t" 	g2="hyphen,endash,emdash" 	k="16" />
<hkern g1="t" 	g2="periodcentered" 	k="10" />
<hkern g1="t" 	g2="copyright" 	k="-16" />
<hkern g1="t" 	g2="registered" 	k="-16" />
<hkern g1="u,ugrave,uacute,ucircumflex,udieresis" 	g2="backslash" 	k="119" />
<hkern g1="u,ugrave,uacute,ucircumflex,udieresis" 	g2="trademark" 	k="57" />
<hkern g1="u,ugrave,uacute,ucircumflex,udieresis" 	g2="j" 	k="-10" />
<hkern g1="v" 	g2="comma,period,ellipsis" 	k="119" />
<hkern g1="v" 	g2="quotedbl,quotesingle" 	k="-41" />
<hkern g1="v" 	g2="quoteleft,quotedblleft" 	k="-41" />
<hkern g1="v" 	g2="quoteright,quotedblright" 	k="-41" />
<hkern g1="v" 	g2="quotesinglbase,quotedblbase" 	k="96" />
<hkern g1="v" 	g2="t" 	k="-27" />
<hkern g1="v" 	g2="w" 	k="-35" />
<hkern g1="v" 	g2="y,yacute,ydieresis" 	k="-35" />
<hkern g1="v" 	g2="colon,semicolon" 	k="10" />
<hkern g1="v" 	g2="f" 	k="-27" />
<hkern g1="v" 	g2="bracketright,braceright" 	k="27" />
<hkern g1="v" 	g2="z" 	k="20" />
<hkern g1="v" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="35" />
<hkern g1="v" 	g2="s" 	k="10" />
<hkern g1="v" 	g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="27" />
<hkern g1="v" 	g2="d,q" 	k="25" />
<hkern g1="v" 	g2="g" 	k="25" />
<hkern g1="v" 	g2="guilsinglleft" 	k="57" />
<hkern g1="v" 	g2="hyphen,endash,emdash" 	k="41" />
<hkern g1="w" 	g2="asterisk" 	k="-31" />
<hkern g1="w" 	g2="backslash" 	k="68" />
<hkern g1="w" 	g2="comma,period,ellipsis" 	k="119" />
<hkern g1="w" 	g2="question" 	k="-66" />
<hkern g1="w" 	g2="quotedbl,quotesingle" 	k="-41" />
<hkern g1="w" 	g2="quoteleft,quotedblleft" 	k="-41" />
<hkern g1="w" 	g2="quoteright,quotedblright" 	k="-41" />
<hkern g1="w" 	g2="quotesinglbase,quotedblbase" 	k="96" />
<hkern g1="w" 	g2="t" 	k="-27" />
<hkern g1="w" 	g2="v" 	k="-35" />
<hkern g1="w" 	g2="w" 	k="-35" />
<hkern g1="w" 	g2="y,yacute,ydieresis" 	k="-35" />
<hkern g1="w" 	g2="colon,semicolon" 	k="10" />
<hkern g1="w" 	g2="f" 	k="-27" />
<hkern g1="w" 	g2="slash" 	k="68" />
<hkern g1="w" 	g2="bracketright,braceright" 	k="27" />
<hkern g1="w" 	g2="parenright" 	k="31" />
<hkern g1="w" 	g2="z" 	k="20" />
<hkern g1="w" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="35" />
<hkern g1="w" 	g2="s" 	k="10" />
<hkern g1="w" 	g2="ampersand" 	k="61" />
<hkern g1="w" 	g2="asciicircum" 	k="6" />
<hkern g1="w" 	g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="27" />
<hkern g1="w" 	g2="d,q" 	k="25" />
<hkern g1="w" 	g2="g" 	k="25" />
<hkern g1="w" 	g2="guilsinglleft" 	k="57" />
<hkern g1="w" 	g2="hyphen,endash,emdash" 	k="41" />
<hkern g1="w" 	g2="periodcentered" 	k="16" />
<hkern g1="x" 	g2="quotedbl,quotesingle" 	k="16" />
<hkern g1="x" 	g2="quoteleft,quotedblleft" 	k="20" />
<hkern g1="x" 	g2="quoteright,quotedblright" 	k="16" />
<hkern g1="x" 	g2="t" 	k="41" />
<hkern g1="x" 	g2="f" 	k="20" />
<hkern g1="x" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="31" />
<hkern g1="x" 	g2="s" 	k="45" />
<hkern g1="x" 	g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="76" />
<hkern g1="x" 	g2="d,q" 	k="57" />
<hkern g1="x" 	g2="g" 	k="57" />
<hkern g1="x" 	g2="guilsinglleft" 	k="86" />
<hkern g1="x" 	g2="hyphen,endash,emdash" 	k="86" />
<hkern g1="x" 	g2="copyright" 	k="37" />
<hkern g1="x" 	g2="guilsinglright" 	k="16" />
<hkern g1="x" 	g2="u,ugrave,uacute,ucircumflex,udieresis" 	k="31" />
<hkern g1="y,yacute,ydieresis" 	g2="asterisk" 	k="-31" />
<hkern g1="y,yacute,ydieresis" 	g2="backslash" 	k="68" />
<hkern g1="y,yacute,ydieresis" 	g2="comma,period,ellipsis" 	k="119" />
<hkern g1="y,yacute,ydieresis" 	g2="question" 	k="-66" />
<hkern g1="y,yacute,ydieresis" 	g2="quotedbl,quotesingle" 	k="-41" />
<hkern g1="y,yacute,ydieresis" 	g2="quoteleft,quotedblleft" 	k="-41" />
<hkern g1="y,yacute,ydieresis" 	g2="quoteright,quotedblright" 	k="-41" />
<hkern g1="y,yacute,ydieresis" 	g2="quotesinglbase,quotedblbase" 	k="96" />
<hkern g1="y,yacute,ydieresis" 	g2="t" 	k="-27" />
<hkern g1="y,yacute,ydieresis" 	g2="v" 	k="-35" />
<hkern g1="y,yacute,ydieresis" 	g2="w" 	k="-35" />
<hkern g1="y,yacute,ydieresis" 	g2="y,yacute,ydieresis" 	k="-35" />
<hkern g1="y,yacute,ydieresis" 	g2="colon,semicolon" 	k="10" />
<hkern g1="y,yacute,ydieresis" 	g2="f" 	k="-27" />
<hkern g1="y,yacute,ydieresis" 	g2="slash" 	k="68" />
<hkern g1="y,yacute,ydieresis" 	g2="bracketright,braceright" 	k="27" />
<hkern g1="y,yacute,ydieresis" 	g2="parenright" 	k="31" />
<hkern g1="y,yacute,ydieresis" 	g2="z" 	k="20" />
<hkern g1="y,yacute,ydieresis" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="35" />
<hkern g1="y,yacute,ydieresis" 	g2="s" 	k="10" />
<hkern g1="y,yacute,ydieresis" 	g2="ampersand" 	k="61" />
<hkern g1="y,yacute,ydieresis" 	g2="asciicircum" 	k="6" />
<hkern g1="y,yacute,ydieresis" 	g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="27" />
<hkern g1="y,yacute,ydieresis" 	g2="d,q" 	k="25" />
<hkern g1="y,yacute,ydieresis" 	g2="g" 	k="25" />
<hkern g1="y,yacute,ydieresis" 	g2="guilsinglleft" 	k="57" />
<hkern g1="y,yacute,ydieresis" 	g2="hyphen,endash,emdash" 	k="41" />
<hkern g1="y,yacute,ydieresis" 	g2="periodcentered" 	k="16" />
<hkern g1="z" 	g2="asterisk" 	k="10" />
<hkern g1="z" 	g2="backslash" 	k="109" />
<hkern g1="z" 	g2="comma,period,ellipsis" 	k="-20" />
<hkern g1="z" 	g2="question" 	k="-16" />
<hkern g1="z" 	g2="quotesinglbase,quotedblbase" 	k="-16" />
<hkern g1="z" 	g2="trademark" 	k="10" />
<hkern g1="z" 	g2="at" 	k="6" />
<hkern g1="z" 	g2="s" 	k="16" />
<hkern g1="z" 	g2="ampersand" 	k="37" />
<hkern g1="z" 	g2="asciicircum" 	k="4" />
<hkern g1="z" 	g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="37" />
<hkern g1="z" 	g2="d,q" 	k="25" />
<hkern g1="z" 	g2="g" 	k="25" />
<hkern g1="z" 	g2="guilsinglleft" 	k="31" />
<hkern g1="z" 	g2="hyphen,endash,emdash" 	k="31" />
<hkern g1="z" 	g2="periodcentered" 	k="16" />
<hkern g1="ampersand" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="-31" />
<hkern g1="ampersand" 	g2="AE" 	k="-6" />
<hkern g1="ampersand" 	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="6" />
<hkern g1="ampersand" 	g2="J" 	k="-16" />
<hkern g1="ampersand" 	g2="S" 	k="16" />
<hkern g1="ampersand" 	g2="T" 	k="150" />
<hkern g1="ampersand" 	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	k="6" />
<hkern g1="ampersand" 	g2="W" 	k="98" />
<hkern g1="ampersand" 	g2="Y,Yacute,Ydieresis" 	k="180" />
<hkern g1="ampersand" 	g2="Z" 	k="-25" />
<hkern g1="ampersand" 	g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="10" />
<hkern g1="ampersand" 	g2="f" 	k="6" />
<hkern g1="ampersand" 	g2="j" 	k="-31" />
<hkern g1="ampersand" 	g2="t" 	k="31" />
<hkern g1="ampersand" 	g2="u,ugrave,uacute,ucircumflex,udieresis" 	k="6" />
<hkern g1="ampersand" 	g2="w" 	k="68" />
<hkern g1="ampersand" 	g2="y,yacute,ydieresis" 	k="47" />
<hkern g1="ampersand" 	g2="z" 	k="-16" />
<hkern g1="at" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="68" />
<hkern g1="at" 	g2="AE" 	k="57" />
<hkern g1="at" 	g2="T" 	k="102" />
<hkern g1="at" 	g2="W" 	k="55" />
<hkern g1="at" 	g2="Y,Yacute,Ydieresis" 	k="129" />
<hkern g1="at" 	g2="Z" 	k="37" />
<hkern g1="at" 	g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="-16" />
<hkern g1="at" 	g2="w" 	k="-16" />
<hkern g1="at" 	g2="z" 	k="31" />
<hkern g1="at" 	g2="d,q" 	k="-16" />
<hkern g1="at" 	g2="g" 	k="-16" />
<hkern g1="copyright" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="72" />
<hkern g1="copyright" 	g2="AE" 	k="68" />
<hkern g1="copyright" 	g2="T" 	k="78" />
<hkern g1="copyright" 	g2="W" 	k="55" />
<hkern g1="copyright" 	g2="Y,Yacute,Ydieresis" 	k="119" />
<hkern g1="copyright" 	g2="Z" 	k="57" />
<hkern g1="copyright" 	g2="z" 	k="16" />
<hkern g1="copyright" 	g2="V" 	k="57" />
<hkern g1="copyright" 	g2="X" 	k="92" />
<hkern g1="copyright" 	g2="x" 	k="37" />
<hkern g1="asterisk" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="190" />
<hkern g1="asterisk" 	g2="AE" 	k="195" />
<hkern g1="asterisk" 	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="31" />
<hkern g1="asterisk" 	g2="J" 	k="233" />
<hkern g1="asterisk" 	g2="S" 	k="16" />
<hkern g1="asterisk" 	g2="Y,Yacute,Ydieresis" 	k="27" />
<hkern g1="asterisk" 	g2="Z" 	k="25" />
<hkern g1="asterisk" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="78" />
<hkern g1="asterisk" 	g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="78" />
<hkern g1="asterisk" 	g2="d,q" 	k="72" />
<hkern g1="asterisk" 	g2="f" 	k="-6" />
<hkern g1="asterisk" 	g2="g" 	k="72" />
<hkern g1="asterisk" 	g2="s" 	k="72" />
<hkern g1="asterisk" 	g2="t" 	k="-6" />
<hkern g1="asterisk" 	g2="w" 	k="-31" />
<hkern g1="asterisk" 	g2="y,yacute,ydieresis" 	k="-31" />
<hkern g1="asterisk" 	g2="z" 	k="31" />
<hkern g1="backslash" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="-27" />
<hkern g1="backslash" 	g2="AE" 	k="-6" />
<hkern g1="backslash" 	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="88" />
<hkern g1="backslash" 	g2="J" 	k="6" />
<hkern g1="backslash" 	g2="S" 	k="6" />
<hkern g1="backslash" 	g2="Y,Yacute,Ydieresis" 	k="201" />
<hkern g1="backslash" 	g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="20" />
<hkern g1="backslash" 	g2="d,q" 	k="16" />
<hkern g1="backslash" 	g2="f" 	k="37" />
<hkern g1="backslash" 	g2="g" 	k="16" />
<hkern g1="backslash" 	g2="s" 	k="10" />
<hkern g1="backslash" 	g2="t" 	k="61" />
<hkern g1="backslash" 	g2="w" 	k="68" />
<hkern g1="backslash" 	g2="y,yacute,ydieresis" 	k="37" />
<hkern g1="backslash" 	g2="T" 	k="174" />
<hkern g1="backslash" 	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	k="41" />
<hkern g1="backslash" 	g2="W" 	k="143" />
<hkern g1="backslash" 	g2="u,ugrave,uacute,ucircumflex,udieresis" 	k="20" />
<hkern g1="bracketleft,braceleft" 	g2="J" 	k="31" />
<hkern g1="bracketleft,braceleft" 	g2="Y,Yacute,Ydieresis" 	k="-20" />
<hkern g1="bracketleft,braceleft" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="10" />
<hkern g1="bracketleft,braceleft" 	g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="27" />
<hkern g1="bracketleft,braceleft" 	g2="d,q" 	k="27" />
<hkern g1="bracketleft,braceleft" 	g2="f" 	k="10" />
<hkern g1="bracketleft,braceleft" 	g2="g" 	k="27" />
<hkern g1="bracketleft,braceleft" 	g2="t" 	k="16" />
<hkern g1="bracketleft,braceleft" 	g2="w" 	k="27" />
<hkern g1="bracketleft,braceleft" 	g2="T" 	k="-25" />
<hkern g1="bracketleft,braceleft" 	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	k="-16" />
<hkern g1="bracketleft,braceleft" 	g2="W" 	k="-20" />
<hkern g1="bracketleft,braceleft" 	g2="V" 	k="-20" />
<hkern g1="bracketleft,braceleft" 	g2="j" 	k="-98" />
<hkern g1="bracketleft,braceleft" 	g2="v" 	k="27" />
<hkern g1="colon,semicolon" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="-20" />
<hkern g1="colon,semicolon" 	g2="Y,Yacute,Ydieresis" 	k="133" />
<hkern g1="colon,semicolon" 	g2="Z" 	k="-16" />
<hkern g1="colon,semicolon" 	g2="f" 	k="-6" />
<hkern g1="colon,semicolon" 	g2="s" 	k="-6" />
<hkern g1="colon,semicolon" 	g2="t" 	k="14" />
<hkern g1="colon,semicolon" 	g2="w" 	k="10" />
<hkern g1="colon,semicolon" 	g2="y,yacute,ydieresis" 	k="-6" />
<hkern g1="colon,semicolon" 	g2="T" 	k="127" />
<hkern g1="colon,semicolon" 	g2="W" 	k="51" />
<hkern g1="colon,semicolon" 	g2="V" 	k="72" />
<hkern g1="colon,semicolon" 	g2="j" 	k="-55" />
<hkern g1="colon,semicolon" 	g2="v" 	k="10" />
<hkern g1="comma,period,ellipsis" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="-35" />
<hkern g1="comma,period,ellipsis" 	g2="AE" 	k="-27" />
<hkern g1="comma,period,ellipsis" 	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="92" />
<hkern g1="comma,period,ellipsis" 	g2="J" 	k="-25" />
<hkern g1="comma,period,ellipsis" 	g2="S" 	k="14" />
<hkern g1="comma,period,ellipsis" 	g2="Y,Yacute,Ydieresis" 	k="215" />
<hkern g1="comma,period,ellipsis" 	g2="Z" 	k="-20" />
<hkern g1="comma,period,ellipsis" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="-6" />
<hkern g1="comma,period,ellipsis" 	g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="45" />
<hkern g1="comma,period,ellipsis" 	g2="d,q" 	k="31" />
<hkern g1="comma,period,ellipsis" 	g2="f" 	k="31" />
<hkern g1="comma,period,ellipsis" 	g2="g" 	k="31" />
<hkern g1="comma,period,ellipsis" 	g2="s" 	k="-6" />
<hkern g1="comma,period,ellipsis" 	g2="t" 	k="82" />
<hkern g1="comma,period,ellipsis" 	g2="w" 	k="119" />
<hkern g1="comma,period,ellipsis" 	g2="y,yacute,ydieresis" 	k="57" />
<hkern g1="comma,period,ellipsis" 	g2="z" 	k="-20" />
<hkern g1="comma,period,ellipsis" 	g2="T" 	k="190" />
<hkern g1="comma,period,ellipsis" 	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	k="45" />
<hkern g1="comma,period,ellipsis" 	g2="W" 	k="143" />
<hkern g1="comma,period,ellipsis" 	g2="u,ugrave,uacute,ucircumflex,udieresis" 	k="25" />
<hkern g1="comma,period,ellipsis" 	g2="V" 	k="180" />
<hkern g1="comma,period,ellipsis" 	g2="j" 	k="-20" />
<hkern g1="comma,period,ellipsis" 	g2="v" 	k="119" />
<hkern g1="comma,period,ellipsis" 	g2="X" 	k="-6" />
<hkern g1="exclamdown" 	g2="Y,Yacute,Ydieresis" 	k="61" />
<hkern g1="exclamdown" 	g2="T" 	k="31" />
<hkern g1="exclamdown" 	g2="W" 	k="31" />
<hkern g1="exclamdown" 	g2="j" 	k="-29" />
<hkern g1="guilsinglleft" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="16" />
<hkern g1="guilsinglleft" 	g2="Y,Yacute,Ydieresis" 	k="139" />
<hkern g1="guilsinglleft" 	g2="T" 	k="123" />
<hkern g1="guilsinglleft" 	g2="W" 	k="57" />
<hkern g1="guilsinglleft" 	g2="V" 	k="78" />
<hkern g1="guilsinglleft" 	g2="X" 	k="41" />
<hkern g1="guilsinglleft" 	g2="x" 	k="16" />
<hkern g1="guilsinglright" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="51" />
<hkern g1="guilsinglright" 	g2="AE" 	k="37" />
<hkern g1="guilsinglright" 	g2="J" 	k="-10" />
<hkern g1="guilsinglright" 	g2="Y,Yacute,Ydieresis" 	k="215" />
<hkern g1="guilsinglright" 	g2="Z" 	k="31" />
<hkern g1="guilsinglright" 	g2="f" 	k="25" />
<hkern g1="guilsinglright" 	g2="s" 	k="16" />
<hkern g1="guilsinglright" 	g2="t" 	k="16" />
<hkern g1="guilsinglright" 	g2="w" 	k="57" />
<hkern g1="guilsinglright" 	g2="y,yacute,ydieresis" 	k="41" />
<hkern g1="guilsinglright" 	g2="z" 	k="31" />
<hkern g1="guilsinglright" 	g2="T" 	k="205" />
<hkern g1="guilsinglright" 	g2="W" 	k="113" />
<hkern g1="guilsinglright" 	g2="V" 	k="133" />
<hkern g1="guilsinglright" 	g2="v" 	k="57" />
<hkern g1="guilsinglright" 	g2="X" 	k="102" />
<hkern g1="guilsinglright" 	g2="x" 	k="82" />
<hkern g1="hyphen,endash,emdash" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="41" />
<hkern g1="hyphen,endash,emdash" 	g2="AE" 	k="16" />
<hkern g1="hyphen,endash,emdash" 	g2="J" 	k="-10" />
<hkern g1="hyphen,endash,emdash" 	g2="S" 	k="-20" />
<hkern g1="hyphen,endash,emdash" 	g2="Y,Yacute,Ydieresis" 	k="211" />
<hkern g1="hyphen,endash,emdash" 	g2="Z" 	k="16" />
<hkern g1="hyphen,endash,emdash" 	g2="t" 	k="6" />
<hkern g1="hyphen,endash,emdash" 	g2="w" 	k="41" />
<hkern g1="hyphen,endash,emdash" 	g2="y,yacute,ydieresis" 	k="41" />
<hkern g1="hyphen,endash,emdash" 	g2="z" 	k="16" />
<hkern g1="hyphen,endash,emdash" 	g2="T" 	k="164" />
<hkern g1="hyphen,endash,emdash" 	g2="W" 	k="88" />
<hkern g1="hyphen,endash,emdash" 	g2="V" 	k="133" />
<hkern g1="hyphen,endash,emdash" 	g2="v" 	k="41" />
<hkern g1="hyphen,endash,emdash" 	g2="X" 	k="102" />
<hkern g1="hyphen,endash,emdash" 	g2="x" 	k="86" />
<hkern g1="parenleft" 	g2="J" 	k="35" />
<hkern g1="parenleft" 	g2="S" 	k="16" />
<hkern g1="parenleft" 	g2="Y,Yacute,Ydieresis" 	k="-20" />
<hkern g1="parenleft" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="16" />
<hkern g1="parenleft" 	g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="16" />
<hkern g1="parenleft" 	g2="d,q" 	k="20" />
<hkern g1="parenleft" 	g2="g" 	k="20" />
<hkern g1="parenleft" 	g2="s" 	k="16" />
<hkern g1="parenleft" 	g2="t" 	k="16" />
<hkern g1="parenleft" 	g2="w" 	k="31" />
<hkern g1="parenleft" 	g2="T" 	k="-16" />
<hkern g1="parenleft" 	g2="W" 	k="-20" />
<hkern g1="parenleft" 	g2="u,ugrave,uacute,ucircumflex,udieresis" 	k="16" />
<hkern g1="parenleft" 	g2="j" 	k="-109" />
<hkern g1="periodcentered" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="68" />
<hkern g1="periodcentered" 	g2="AE" 	k="49" />
<hkern g1="periodcentered" 	g2="Y,Yacute,Ydieresis" 	k="150" />
<hkern g1="periodcentered" 	g2="Z" 	k="16" />
<hkern g1="periodcentered" 	g2="f" 	k="-6" />
<hkern g1="periodcentered" 	g2="s" 	k="-6" />
<hkern g1="periodcentered" 	g2="t" 	k="-6" />
<hkern g1="periodcentered" 	g2="w" 	k="16" />
<hkern g1="periodcentered" 	g2="y,yacute,ydieresis" 	k="16" />
<hkern g1="periodcentered" 	g2="z" 	k="16" />
<hkern g1="periodcentered" 	g2="T" 	k="129" />
<hkern g1="periodcentered" 	g2="W" 	k="66" />
<hkern g1="questiondown" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="-20" />
<hkern g1="questiondown" 	g2="AE" 	k="-6" />
<hkern g1="questiondown" 	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="47" />
<hkern g1="questiondown" 	g2="J" 	k="-25" />
<hkern g1="questiondown" 	g2="Y,Yacute,Ydieresis" 	k="225" />
<hkern g1="questiondown" 	g2="Z" 	k="-25" />
<hkern g1="questiondown" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="-16" />
<hkern g1="questiondown" 	g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="27" />
<hkern g1="questiondown" 	g2="d,q" 	k="25" />
<hkern g1="questiondown" 	g2="f" 	k="35" />
<hkern g1="questiondown" 	g2="g" 	k="6" />
<hkern g1="questiondown" 	g2="t" 	k="57" />
<hkern g1="questiondown" 	g2="w" 	k="109" />
<hkern g1="questiondown" 	g2="y,yacute,ydieresis" 	k="51" />
<hkern g1="questiondown" 	g2="T" 	k="178" />
<hkern g1="questiondown" 	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	k="31" />
<hkern g1="questiondown" 	g2="W" 	k="150" />
<hkern g1="questiondown" 	g2="j" 	k="-82" />
<hkern g1="quotedbl,quotesingle" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="59" />
<hkern g1="quotedbl,quotesingle" 	g2="AE" 	k="72" />
<hkern g1="quotedbl,quotesingle" 	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="16" />
<hkern g1="quotedbl,quotesingle" 	g2="J" 	k="76" />
<hkern g1="quotedbl,quotesingle" 	g2="S" 	k="10" />
<hkern g1="quotedbl,quotesingle" 	g2="Y,Yacute,Ydieresis" 	k="-6" />
<hkern g1="quotedbl,quotesingle" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="43" />
<hkern g1="quotedbl,quotesingle" 	g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="39" />
<hkern g1="quotedbl,quotesingle" 	g2="d,q" 	k="43" />
<hkern g1="quotedbl,quotesingle" 	g2="f" 	k="4" />
<hkern g1="quotedbl,quotesingle" 	g2="g" 	k="43" />
<hkern g1="quotedbl,quotesingle" 	g2="s" 	k="27" />
<hkern g1="quotedbl,quotesingle" 	g2="t" 	k="-4" />
<hkern g1="quotedbl,quotesingle" 	g2="w" 	k="-6" />
<hkern g1="quotedbl,quotesingle" 	g2="y,yacute,ydieresis" 	k="-6" />
<hkern g1="quotedbl,quotesingle" 	g2="z" 	k="12" />
<hkern g1="quotedbl,quotesingle" 	g2="T" 	k="-6" />
<hkern g1="quotedbl,quotesingle" 	g2="W" 	k="-6" />
<hkern g1="quotedbl,quotesingle" 	g2="u,ugrave,uacute,ucircumflex,udieresis" 	k="6" />
<hkern g1="quotedbl,quotesingle" 	g2="V" 	k="-6" />
<hkern g1="quotedbl,quotesingle" 	g2="v" 	k="-6" />
<hkern g1="quotedbl,quotesingle" 	g2="X" 	k="10" />
<hkern g1="quotedbl,quotesingle" 	g2="x" 	k="10" />
<hkern g1="quotedbl,quotesingle" 	g2="germandbls" 	k="4" />
<hkern g1="quotedbl,quotesingle" 	g2="m,n,r,ntilde" 	k="6" />
<hkern g1="quotedbl,quotesingle" 	g2="p" 	k="6" />
<hkern g1="quoteleft,quotedblleft" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="160" />
<hkern g1="quoteleft,quotedblleft" 	g2="AE" 	k="180" />
<hkern g1="quoteleft,quotedblleft" 	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="16" />
<hkern g1="quoteleft,quotedblleft" 	g2="J" 	k="215" />
<hkern g1="quoteleft,quotedblleft" 	g2="S" 	k="20" />
<hkern g1="quoteleft,quotedblleft" 	g2="Y,Yacute,Ydieresis" 	k="-16" />
<hkern g1="quoteleft,quotedblleft" 	g2="Z" 	k="6" />
<hkern g1="quoteleft,quotedblleft" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="72" />
<hkern g1="quoteleft,quotedblleft" 	g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="70" />
<hkern g1="quoteleft,quotedblleft" 	g2="d,q" 	k="92" />
<hkern g1="quoteleft,quotedblleft" 	g2="f" 	k="-6" />
<hkern g1="quoteleft,quotedblleft" 	g2="g" 	k="92" />
<hkern g1="quoteleft,quotedblleft" 	g2="s" 	k="49" />
<hkern g1="quoteleft,quotedblleft" 	g2="t" 	k="-25" />
<hkern g1="quoteleft,quotedblleft" 	g2="w" 	k="-37" />
<hkern g1="quoteleft,quotedblleft" 	g2="y,yacute,ydieresis" 	k="-37" />
<hkern g1="quoteleft,quotedblleft" 	g2="T" 	k="-20" />
<hkern g1="quoteleft,quotedblleft" 	g2="W" 	k="-20" />
<hkern g1="quoteleft,quotedblleft" 	g2="V" 	k="-20" />
<hkern g1="quoteleft,quotedblleft" 	g2="v" 	k="-37" />
<hkern g1="quoteleft,quotedblleft" 	g2="X" 	k="20" />
<hkern g1="quoteleft,quotedblleft" 	g2="x" 	k="6" />
<hkern g1="quoteleft,quotedblleft" 	g2="germandbls" 	k="10" />
<hkern g1="quoteright,quotedblright" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="199" />
<hkern g1="quoteright,quotedblright" 	g2="AE" 	k="211" />
<hkern g1="quoteright,quotedblright" 	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="51" />
<hkern g1="quoteright,quotedblright" 	g2="J" 	k="215" />
<hkern g1="quoteright,quotedblright" 	g2="S" 	k="27" />
<hkern g1="quoteright,quotedblright" 	g2="Y,Yacute,Ydieresis" 	k="-20" />
<hkern g1="quoteright,quotedblright" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="133" />
<hkern g1="quoteright,quotedblright" 	g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="123" />
<hkern g1="quoteright,quotedblright" 	g2="d,q" 	k="162" />
<hkern g1="quoteright,quotedblright" 	g2="f" 	k="10" />
<hkern g1="quoteright,quotedblright" 	g2="g" 	k="162" />
<hkern g1="quoteright,quotedblright" 	g2="s" 	k="102" />
<hkern g1="quoteright,quotedblright" 	g2="t" 	k="-4" />
<hkern g1="quoteright,quotedblright" 	g2="w" 	k="-6" />
<hkern g1="quoteright,quotedblright" 	g2="y,yacute,ydieresis" 	k="-20" />
<hkern g1="quoteright,quotedblright" 	g2="z" 	k="41" />
<hkern g1="quoteright,quotedblright" 	g2="T" 	k="-20" />
<hkern g1="quoteright,quotedblright" 	g2="W" 	k="-20" />
<hkern g1="quoteright,quotedblright" 	g2="u,ugrave,uacute,ucircumflex,udieresis" 	k="20" />
<hkern g1="quoteright,quotedblright" 	g2="V" 	k="-20" />
<hkern g1="quoteright,quotedblright" 	g2="v" 	k="-6" />
<hkern g1="quoteright,quotedblright" 	g2="X" 	k="25" />
<hkern g1="quoteright,quotedblright" 	g2="x" 	k="37" />
<hkern g1="quoteright,quotedblright" 	g2="germandbls" 	k="16" />
<hkern g1="quoteright,quotedblright" 	g2="m,n,r,ntilde" 	k="6" />
<hkern g1="quoteright,quotedblright" 	g2="p" 	k="6" />
<hkern g1="quotesinglbase,quotedblbase" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="-35" />
<hkern g1="quotesinglbase,quotedblbase" 	g2="AE" 	k="-27" />
<hkern g1="quotesinglbase,quotedblbase" 	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="92" />
<hkern g1="quotesinglbase,quotedblbase" 	g2="J" 	k="-25" />
<hkern g1="quotesinglbase,quotedblbase" 	g2="S" 	k="14" />
<hkern g1="quotesinglbase,quotedblbase" 	g2="Y,Yacute,Ydieresis" 	k="215" />
<hkern g1="quotesinglbase,quotedblbase" 	g2="Z" 	k="-20" />
<hkern g1="quotesinglbase,quotedblbase" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="-6" />
<hkern g1="quotesinglbase,quotedblbase" 	g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="45" />
<hkern g1="quotesinglbase,quotedblbase" 	g2="d,q" 	k="31" />
<hkern g1="quotesinglbase,quotedblbase" 	g2="f" 	k="31" />
<hkern g1="quotesinglbase,quotedblbase" 	g2="g" 	k="31" />
<hkern g1="quotesinglbase,quotedblbase" 	g2="s" 	k="-6" />
<hkern g1="quotesinglbase,quotedblbase" 	g2="t" 	k="82" />
<hkern g1="quotesinglbase,quotedblbase" 	g2="w" 	k="119" />
<hkern g1="quotesinglbase,quotedblbase" 	g2="y,yacute,ydieresis" 	k="57" />
<hkern g1="quotesinglbase,quotedblbase" 	g2="z" 	k="-20" />
<hkern g1="quotesinglbase,quotedblbase" 	g2="T" 	k="190" />
<hkern g1="quotesinglbase,quotedblbase" 	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	k="45" />
<hkern g1="quotesinglbase,quotedblbase" 	g2="W" 	k="143" />
<hkern g1="quotesinglbase,quotedblbase" 	g2="u,ugrave,uacute,ucircumflex,udieresis" 	k="25" />
<hkern g1="quotesinglbase,quotedblbase" 	g2="V" 	k="180" />
<hkern g1="quotesinglbase,quotedblbase" 	g2="j" 	k="-78" />
<hkern g1="quotesinglbase,quotedblbase" 	g2="v" 	k="119" />
<hkern g1="quotesinglbase,quotedblbase" 	g2="X" 	k="-6" />
<hkern g1="slash" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="174" />
<hkern g1="slash" 	g2="AE" 	k="170" />
<hkern g1="slash" 	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="88" />
<hkern g1="slash" 	g2="J" 	k="188" />
<hkern g1="slash" 	g2="S" 	k="27" />
<hkern g1="slash" 	g2="Y,Yacute,Ydieresis" 	k="-20" />
<hkern g1="slash" 	g2="Z" 	k="27" />
<hkern g1="slash" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="158" />
<hkern g1="slash" 	g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="164" />
<hkern g1="slash" 	g2="d,q" 	k="147" />
<hkern g1="slash" 	g2="f" 	k="41" />
<hkern g1="slash" 	g2="g" 	k="147" />
<hkern g1="slash" 	g2="s" 	k="154" />
<hkern g1="slash" 	g2="t" 	k="55" />
<hkern g1="slash" 	g2="w" 	k="68" />
<hkern g1="slash" 	g2="y,yacute,ydieresis" 	k="53" />
<hkern g1="slash" 	g2="z" 	k="133" />
<hkern g1="slash" 	g2="T" 	k="-20" />
<hkern g1="slash" 	g2="W" 	k="-25" />
<hkern g1="slash" 	g2="u,ugrave,uacute,ucircumflex,udieresis" 	k="102" />
<hkern g1="slash" 	g2="m,n,r,ntilde" 	k="96" />
</font>
</defs></svg> 