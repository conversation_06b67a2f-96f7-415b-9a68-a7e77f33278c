import React, { useEffect, useState } from 'react';
import axios from 'axios';

const About = () => {
  const [aboutItems, setAboutItems] = useState([]);
  const [details, setDetails] = useState('');

  useEffect(() => {
    axios.get('/api/about')
      .then(response => {
        setAboutItems(response.data || []);
        if (response.data?.length > 0) {
          setDetails(response.data[0].details);
        }
      })
      .catch(error => {
        console.error('Error fetching about data:', error);
      });
  }, []);


    const handleScrollToTop = (event) => {
    event.preventDefault();
    const targetElement = event.currentTarget; // this is the clicked element
    if (!targetElement) return;
    const offsetTop = targetElement.getBoundingClientRect().top + window.scrollY; // adjust as needed
    window.scrollTo({
      top: offsetTop - 120,
      behavior: 'smooth',
    });
  };
  
  const aboutTitle = 'About';
  const chars = aboutTitle.split('');
  const totalChars = chars.length;
  const totalDuration = 0.5; // seconds
  const delayPerChar = totalDuration / totalChars;

  return (
    <section className="single-items" id="section1">
      <div className="trriger-menu">
         <div className="row">
          <div className="col-lg-9">
            <h2 className="animated-title" data-title={aboutTitle} onClick={ (event) => handleScrollToTop(event) }>
              {chars.map((char, i) => (
                <span
                  key={i}
                  className="fade-in-char"
                  style={{ animationDelay: `${i * delayPerChar}s` }}
                >
                  {char}
                </span>
              ))}
            </h2>
          </div>
        </div>
      </div>

      {details && (
        <div className="row">
          <div className="col-lg-9">
            <div className="description-wrap" dangerouslySetInnerHTML={{ __html: details }} />
          </div>
        </div>
      )}
    </section>
  );
};

export default About;
