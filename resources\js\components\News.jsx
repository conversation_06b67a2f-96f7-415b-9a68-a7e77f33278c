import React, { useEffect, useState } from 'react';
import axios from 'axios';

const News = () => {
  const [newsItems, setNewsItems] = useState([]);

  useEffect(() => {
    axios.get('/api/news') // Make sure this route exists in Laravel
      .then(response => {
        setNewsItems(response.data);
      })
      .catch(error => {
        console.error('Error fetching news:', error);
      });
  }, []);

  const handleScrollToTop = (event) => {
    event.preventDefault();
    const targetElement = event.currentTarget; // this is the clicked element
    if (!targetElement) return;
    const offsetTop = targetElement.getBoundingClientRect().top + window.scrollY; // adjust as needed
    window.scrollTo({
      top: offsetTop - 120,
      behavior: 'smooth',
    });
  };

  const newsTitle = 'News';
  const chars = newsTitle.split('');
  const totalChars = chars.length;
  const totalDuration = 0.5; // seconds
  const delayPerChar = totalDuration / totalChars;
  return (
    <section className="single-items" id="section5">
      <div className="trriger-menu">
        <div className="row">
          <div className="col-lg-9">
            <h2 className="animated-title" data-title={newsTitle} onClick={(event) => handleScrollToTop(event)}>
              {chars.map((char, i) => (
                <span
                  key={i}
                  className="fade-in-char"
                  style={{ animationDelay: `${i * delayPerChar}s` }}
                >
                  {char}
                </span>
              ))}
            </h2>
          </div>
        </div>

      </div>
      <div className="description-wrap">
        <ul className="news-list">
          {newsItems.map((news, index) => (
            <li key={index}>
              <div className="single-news">
                <div className="menu-btn" onClick={(event) => handleScrollToTop(event)}>
                  <div className="row">
                    <div className="col-lg-9">
                      <h3 onClick={() => setValue(prev => prev + 1)}>{news.title}</h3>
                      <span>{news.publish_date}</span>
                    </div>
                  </div>
                </div>
                <div className="row">
                  <div className="col-lg-9">
                    <div className="menucontent child-menucontent">
                      <p>
                        <span dangerouslySetInnerHTML={{ __html: news.short_description }} />{' '}
                        {news.redirect_link && (
                          <a href={news.redirect_link} target="_blank" rel="noopener noreferrer">
                            {news.redirect_link}
                          </a>
                        )}
                      </p>
                    </div>
                  </div>
                </div>
              </div>
            </li>
          ))}
        </ul>
      </div>
    </section>
  );
};

export default News;
