@extends('backend.master')
@section('main')
<div class="main-content">
    <section class="section">
        <div class="section-body">
            <div class="row">
                <div class="col-12">
                    <div class="card">
                        <div class="card-header">
                            <h4>Add Investigation</h4>
                        </div>
                        <div class="card-body">
                            <form action="{{ route('investigation.store') }}" method="POST">
                                @csrf
                                <div class="form-group row mb-4">
                                    <label class="col-form-label text-md-right col-12 col-md-3 col-lg-3">Title</label>
                                    <div class="col-sm-12 col-md-7">
                                        <input type="text" name="title" class="form-control">
                                    </div>
                                </div>

                                <!-- Repeater Section -->
                                <div class="form-group row mb-4">
                                    <label class="col-form-label text-md-right col-12 col-md-3 col-lg-3">Subtitles & Details</label>
                                    <div class="col-sm-12 col-md-7">
                                        <div class="image-repeater-wrapper">
                                            <div class="row control-group input-group mb-2">
                                                <div class="col-sm-12 mb-2">
                                                    <input type="text" name="sub_title[]" class="form-control" placeholder="Add Subtitle Here" />
                                                </div>
                                                <div class="col-sm-12 mb-2">
                                                    <textarea class="my-editor" name="details[]" id="editor-1"></textarea>
                                                </div>
                                                <div class="col-sm-12">
                                                    <button class="btn btn-danger remove-btn" type="button">Delete</button>
                                                </div>
                                            </div>
                                        </div>

                                        <!-- Hidden Clone Template -->
                                        <div class="clone d-none">
                                            <div class="row control-group input-group mb-2">
                                                <div class="col-sm-12 mb-2">
                                                    <input type="text" name="sub_title[]" class="form-control" placeholder="Add Subtitle Here" />
                                                </div>
                                                <div class="col-sm-12 mb-2">
                                                    <textarea class="my-editor" name="details[]"></textarea>
                                                </div>
                                                <div class="col-sm-12">
                                                    <button class="btn btn-danger remove-btn" type="button">Delete</button>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- Add Button -->
                                <div class="form-group row mb-4">
                                    <div class="col-sm-12 col-md-7 offset-md-3">
                                        <button class="btn btn-success btn-increment" type="button">Add More</button>
                                        <input type="submit" class="btn btn-primary px-4" value="Save Changes" />
                                    </div>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>
</div>

<!-- Scripts -->
<script>
    $(document).ready(function () {
        let editorCounter = 1;

        // Initialize first editor
        initSummernote(`#editor-${editorCounter}`);

        $('body').on('click', '.btn-increment', function () {
            editorCounter++;

            // Clone the hidden template
            let html = $('.clone').html();
            let newElement = $(html);

            // Set a unique ID for Summernote to target
            let newId = 'editor-' + editorCounter;
            newElement.find('textarea').attr('id', newId).val('');

            // Append and initialize new Summernote instance
            $('.image-repeater-wrapper').append(newElement);
            initSummernote(`#${newId}`);
        });

        $('body').on('click', '.remove-btn', function () {
            let $textarea = $(this).closest('.control-group').find('textarea');
            if ($textarea.length && $textarea.summernote) {
                $textarea.summernote('destroy');
            }
            $(this).closest('.control-group').remove();
        });
    });
</script>
@endsection