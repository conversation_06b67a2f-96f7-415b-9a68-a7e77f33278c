import React, { useEffect, useState } from 'react';
import axios from 'axios';

const Contact = () => {
  const [contacts, setContacts] = useState([]);

  useEffect(() => {
    axios.get('/api/contacts')
      .then(response => setContacts(response.data))
      .catch(error => console.error('Error fetching contacts:', error));
  }, []);

  const handleScrollToTop = (event) => {
    event.preventDefault();
    const targetElement = event.currentTarget; // this is the clicked element
    if (!targetElement) return;
    const offsetTop = targetElement.getBoundingClientRect().top + window.scrollY; // adjust as needed
    window.scrollTo({
      top: offsetTop - 120,
      behavior: 'smooth',
    });
  };


  const contactTitle = 'Contacts';
  const chars = contactTitle.split('');
  const totalChars = chars.length;
  const totalDuration = 0.5; // seconds
  const delayPerChar = totalDuration / totalChars;
  return (
    <>
      <section className="single-items" id="section6">
        <div className="trriger-menu">
          <div className="row">
            <div className="col-lg-9">
              <h2 className="animated-title" data-title={contactTitle} onClick={(event) => handleScrollToTop(event)}>
                {chars.map((char, i) => (
                  <span
                    key={i}
                    className="fade-in-char"
                    style={{ animationDelay: `${i * delayPerChar}s` }}
                  >
                    {char}
                  </span>
                ))}
              </h2>
            </div>
          </div>
        </div>
        {contacts.map((contact, ind) => (
          <div className="description-wrap" key={ind}>
            <div className="description contacts">
              <div className="container-one">
                <div className="row">
                  <div className="col-lg-9">
                    <div
                      className="description-wrap"
                      dangerouslySetInnerHTML={{ __html: contact.details }}
                    />
                    <div style={{ height: '400px', paddingTop: '50px' }}>
                      <p style={{ color: '#666', fontStyle: 'italic' }}>
                        For more information about Climate Rights and our ongoing investigations, please don't hesitate to reach out to us using the contact information above.
                      </p>
                    </div>
                  </div>
                </div>


              </div>
            </div>
          </div>
        ))}
      </section>
    </>
  );
};

export default Contact;
