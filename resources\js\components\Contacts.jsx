import React, { useEffect, useState } from 'react';
import axios from 'axios';

const Contact = () => {
  const [contacts, setContacts] = useState([]);

  useEffect(() => {
    axios.get('/api/contacts')
      .then(response => setContacts(response.data))
      .catch(error => console.error('Error fetching contacts:', error));
  }, []);
    const contactTitle = 'Contacts';
    const chars = contactTitle.split('');
    const totalChars = chars.length;
  const totalDuration = 0.5; // seconds
    const delayPerChar = totalDuration / totalChars;
  return (
    <>
        <li className="nav-item dropdown">
              <div className="container-one single-menu-btn">
                <div className="single-menu">
                  <a className="nav-link animated-title" data-title={contactTitle} href="#">
                    {chars.map((char, i) => (
                      <span
                        key={i}
                        className="fade-in-char"
                        style={{ animationDelay: `${i * delayPerChar}s` }}
                      >
                        {char}
                      </span>
                    ))}
                  </a>
                </div>
              </div>
              { contacts.map( ( contact, ind ) => (
                <div className="menucontent" key={ind}>
                  <div className="description contacts">
                    <div className="container-one">
                      <div
                        className="description-wrap"
                        dangerouslySetInnerHTML={{ __html: contact.details }}
                      />
                    </div>
                  </div>
                </div>
              )) }
        </li>
    </>
  );
};

export default Contact;
