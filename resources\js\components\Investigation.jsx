import React, { useEffect, useState } from 'react';
import axios from 'axios';

const Investigation = () => {
  const [investigations, setInvestigations] = useState([]);

  useEffect(() => {
    axios.get('/api/investigations')
      .then(response => setInvestigations(response.data))
      .catch(error => console.error('Error fetching investigations:', error));
  }, []);

    const investigationTitle = 'Investigation';
    const chars = investigationTitle.split('');
    const totalChars = chars.length;
  const totalDuration = 0.5; // seconds
    const delayPerChar = totalDuration / totalChars;

  return (
    <li className="nav-item dropdown">
      <div className="container-one single-menu-btn">
        <div className="single-menu">
          <a className="nav-link animated-title" data-title={investigationTitle} href="#">
            {chars.map((char, i) => (
              <span
                key={i}
                className="fade-in-char"
                style={{ animationDelay: `${i * delayPerChar}s` }}
              >
                {char}
              </span>
            ))}
          </a>
        </div>
      </div>
      <div className="menucontent">
        <div className="description investigations">
          <div className="description-wrap">
            <ul className="news-list">
              {investigations.map((investigation, i) => (
                <li key={i}>
                  <div className="container-one">
                    <div className="row">
                      <div className="col-lg-12">
                        <div className="single-news">
                          <div className="single-menu-btn2">
                            <h3>{investigation.title}</h3>
                          </div>
                          {investigation.investigation_contents.map((content, j) => (
                            <div className="menucontent inv-wrap" key={j}>
                              <p className="inv-sub-title"><strong>{content.sub_title}</strong></p>
                              <br />
                              <div dangerouslySetInnerHTML={{ __html: content.description }} />
                            </div>
                          ))}
                        </div>
                      </div>
                    </div>
                  </div>
                </li>
              ))}
            </ul>
          </div>
        </div>
      </div>
    </li>
  );
};

export default Investigation;
