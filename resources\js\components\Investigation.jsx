import React, { useEffect, useState } from 'react';
import axios from 'axios';

const Investigation = ({ setValue, setInvestigationSubMenu }) => {
  const [investigations, setInvestigations] = useState([]);

  useEffect(() => {
    axios.get('/api/investigations')
      .then(response => {
        setInvestigations(response.data || [])
        setInvestigationSubMenu(response.data || [])
      })
      .catch(error => console.error('Error fetching investigations:', error));
  }, []);

  const handleScrollToTop = (event) => {
    event.preventDefault();
    const targetElement = event.currentTarget; // this is the clicked element
    if (!targetElement) return;
    const offsetTop = targetElement.getBoundingClientRect().top + window.scrollY; // adjust as needed
    window.scrollTo({
      top: offsetTop - 120,
      behavior: 'smooth',
    });
  };


  const investigationTitle = 'Investigation';
  const chars = investigationTitle.split('');
  const totalChars = chars.length;
  const totalDuration = 0.5;
  const delayPerChar = totalDuration / totalChars;

  return (
    <section className="single-items" id="section3">
      <div className="trriger-menu">
        <div className="row">
          <div className="col-lg-9">
            <h2 className="animated-title" data-title={investigationTitle} onClick={(event) => handleScrollToTop(event)}>
              {chars.map((char, i) => (
                <span
                  key={i}
                  className="fade-in-char"
                  style={{ animationDelay: `${i * delayPerChar}s` }}
                >
                  {char}
                </span>
              ))}
            </h2>
          </div>
        </div>

      </div>

      <div className="description-wrap">
        {investigations.map((item, index) => (
          <div className="single-news" key={index}>
            <div className="menu-btn" id={`section3-${index}`} onClick={(event) => handleScrollToTop(event)} >
              <div className="row">
                <div className="col-lg-9">
                  <h3 onClick={() => setValue(prev => prev + 1)}>{item.title}</h3>
                </div>
              </div>
            </div>

            {item.investigation_contents && item.investigation_contents.map((content, j) => (
              <div className="menucontent child-menucontent inv-wrap" key={j}>
                <div className="row">
                  <div className="col-lg-9">
                    <div style={{ paddingTop: '15px' }} dangerouslySetInnerHTML={{ __html: content.description }} />
                  </div>
                   <div className="col-lg-3 position-relative">
                    <p className="inv-sub-title"><strong>{content.sub_title}</strong></p>
                  </div>
                </div>


              </div>
            ))}
          </div>
        ))}
      </div>
    </section>
  );
};

export default Investigation;
