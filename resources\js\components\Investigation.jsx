import React, { useEffect, useState } from 'react';
import axios from 'axios';

const Investigation = ({ setValue }) => {
  const [investigations, setInvestigations] = useState([]);

  useEffect(() => {
    axios.get('/api/investigations')
      .then(response => setInvestigations(response.data || []))
      .catch(error => console.error('Error fetching investigations:', error));
  }, []);

  const investigationTitle = 'Investigation';
  const chars = investigationTitle.split('');
  const totalChars = chars.length;
  const totalDuration = 0.5;
  const delayPerChar = totalDuration / totalChars;

  return (
    <section className="single-items" id="section3">
      <div className="trriger-menu">
        <h2 className="animated-title" data-title={investigationTitle}>
          {chars.map((char, i) => (
            <span
              key={i}
              className="fade-in-char"
              style={{ animationDelay: `${i * delayPerChar}s` }}
            >
              {char}
            </span>
          ))}
        </h2>
      </div>

      <div className="description-wrap">
        {investigations.map((item, index) => (
          <div className="single-news" key={index}>
            <div className="menu-btn">
              <h3 onClick={ () => setValue(prev => prev + 1) }>{item.title}</h3>
            </div>

            {item.investigation_contents && item.investigation_contents.map((content, j) => (
              <div className="menucontent child-menucontent inv-wrap" key={j}>
                <p className="inv-sub-title"><strong>{content.subtitle || 'Introduction'}</strong></p>
                <div dangerouslySetInnerHTML={{ __html: content.description }} />
              </div>
            ))}
          </div>
        ))}
      </div>
    </section>
  );
};

export default Investigation;
