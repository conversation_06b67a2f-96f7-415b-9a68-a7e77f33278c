import React, { useEffect, useState } from 'react';
import axios from 'axios';

const Team = () => {
  const [mainTeam, setMainTeam] = useState([]);
  const [affiliatedResearchers, setAffiliatedResearchers] = useState([]);
  const [collaborators, setCollaborators] = useState([]);

  useEffect(() => {
    axios.get('/api/team')
      .then(response => {
        setMainTeam(response.data.main_team);
        setAffiliatedResearchers(response.data.affiliated_researchers);
        setCollaborators(response.data.collaborators);
      })
      .catch(error => {
        console.error('Error fetching team data:', error);
      });
  }, []);

  const teamTitle = 'Team';
  const chars = teamTitle.split('');
  const totalChars = chars.length;
  const totalDuration = 0.5; // seconds
  const delayPerChar = totalDuration / totalChars;

  return (
    <section className="single-items" id="section4">
      <div className="trriger-menu">
        <h2 className="animated-title" data-title="Team"></h2>
      </div>
      <div className="description-wrap team">
        <ul className="news-list">
          {/* Main Team */}
          {mainTeam.map((member, index) => (
            <li key={`main-${index}`}>
              <div className="single-news">
                <h3>
                  {member.name} <br />
                  <span>{member.position}</span>
                </h3>
                <div className="menucontent" dangerouslySetInnerHTML={{ __html: member.details }} />
              </div>
            </li>
          ))}

          <h3 className="team-title"><strong>Affiliated researchers:</strong></h3>
          {/* Affiliated Researchers */}
          {affiliatedResearchers.map((researcher, index) => (
            <li key={`affiliated-${index}`} className="no-hover">
              <div className="single-news">
                <h3>
                  {researcher.name} <br />
                  <span>{researcher.position}</span>
                </h3>
              </div>
            </li>
          ))}

          <h3 className="team-title"><strong>Collaborators:</strong></h3>
          {/* Collaborators */}
          {collaborators.map((collab, index) => (
            <li key={`collab-${index}`} className="no-hover">
              <div className="single-news">
                <h3>
                  {collab.name} <br />
                  <span>{collab.position}</span>
                </h3>
              </div>
            </li>
          ))}
        </ul>
      </div>
    </section>
  );
};

export default Team;
