import React, { useEffect, useState } from 'react';
import axios from 'axios';

const Team = () => {
  const [mainTeam, setMainTeam] = useState([]);
  const [affiliatedResearchers, setAffiliatedResearchers] = useState([]);
  const [collaborators, setCollaborators] = useState([]);

  useEffect(() => {
    axios.get('/api/team')
      .then(response => {
        setMainTeam(response.data.main_team);
        setAffiliatedResearchers(response.data.affiliated_researchers);
        setCollaborators(response.data.collaborators);
      })
      .catch(error => {
        console.error('Error fetching team data:', error);
      });
  }, []);


  const handleScrollToTop = (event) => {
    event.preventDefault();
    const targetElement = event.currentTarget; // this is the clicked element
    if (!targetElement) return;
    const offsetTop = targetElement.getBoundingClientRect().top + window.scrollY; // adjust as needed
    window.scrollTo({
      top: offsetTop - 120,
      behavior: 'smooth',
    });
  };

  const teamTitle = 'Team';
  const chars = teamTitle.split('');
  const totalChars = chars.length;
  const totalDuration = 0.5; // seconds
  const delayPerChar = totalDuration / totalChars;

  return (
    <section className="single-items" id="section4">
      <div className="trriger-menu">
        <div className="row">
          <div className="col-lg-9">
            <h2 className="animated-title" data-title={teamTitle} onClick={(event) => handleScrollToTop(event)}>
              {chars.map((char, i) => (
                <span
                  key={i}
                  className="fade-in-char"
                  style={{ animationDelay: `${i * delayPerChar}s` }}
                >
                  {char}
                </span>
              ))}
            </h2>
          </div>
        </div>
      </div>
      <div className="description-wrap team">
        <ul className="news-list">
          {/* Main Team */}
          {mainTeam.map((member, index) => (
            <li key={`main-${index}`}>
              <div className="single-news">
                <div className="row">
                  <div className="col-lg-9">
                    <h3>
                      {member.name} <br />
                      <span>{member.position}</span>
                    </h3>
                    <div className="menucontent" dangerouslySetInnerHTML={{ __html: member.details }} />
                  </div>
                </div>

              </div>
            </li>
          ))}
          <div className="row justify-content-end">
            <div className="col-lg-3">
              <h3 className="team-title"><strong>Affiliated researchers:</strong></h3>
            </div>
          </div>
          {/* Affiliated Researchers */}
          {affiliatedResearchers.map((researcher, index) => (
            <li key={`affiliated-${index}`} className="no-hover">
              <div className="single-news">
                <div className="row">
                  <div className="col-lg-9">
                    <h3>
                      {researcher.name} <br />
                      <span>{researcher.position}</span>
                    </h3>
                  </div>
                </div>
              </div>
            </li>
          ))}
          <div className="row justify-content-end">
            <div className="col-lg-3">
              <h3 className="team-title"><strong>Collaborators:</strong></h3>
            </div>
          </div>
          {/* Collaborators */}
          {collaborators.map((collab, index) => (
            <li key={`collab-${index}`} className="no-hover">
              <div className="single-news">
                <div className="row">
                  <div className="col-lg-9">
                    <h3>
                      {collab.name} <br />
                      <span>{collab.position}</span>
                    </h3>
                  </div>
                </div>
              </div>
            </li>
          ))}
        </ul>
      </div>
    </section>
  );
};

export default Team;
