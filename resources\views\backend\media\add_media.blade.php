@extends('backend.master')
@section('main')
<!-- Start app main Content -->
<div class="main-content">
    <section class="section">
        <div class="section-body">
            <div class="row">
                <div class="col-12">
                    <div class="card">
                        <div class="card-header">
                            <h4>Add Settings</h4>
                        </div>
                        <div class="card-body">
                            <form action="{{ route('media.store')}}" method="POST" enctype="multipart/form-data">
                                @csrf
                             


                             <div class="form-group row mb-4">
                                <label class="col-form-label text-md-right col-12 col-md-3 col-lg-3">Logo</label>
                                <div class="col-sm-12 col-md-7">
                                    <input type="file" name="image" class="form-control" required onchange="previewImage(event)">
                                    <img id="imagePreview" src="#" alt="Image Preview" style="margin-top: 15px; display: none; max-width: 200px;" />
                                </div>
                            </div>


                               
                                 


                                <!-- Submit Button -->
                                <div class="form-group row mb-4">
                                    <label class="col-form-label text-md-right col-12 col-md-3 col-lg-3"></label>
                                    <div class="col-sm-12 col-md-7">
                                 
                                        <input type="submit" class="btn btn-primary px-4" value="Save Changes" />
                                    </div>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>
</div>

<!-- Repeater Script -->



<script>
    function previewImage(event) {
        const input = event.target;
        const preview = document.getElementById('imagePreview');
        
        if (input.files && input.files[0]) {
            const reader = new FileReader();
            
            reader.onload = function(e) {
                preview.src = e.target.result;
                preview.style.display = 'block';
            }
            
            reader.readAsDataURL(input.files[0]);
        }
    }
</script>
@endsection
