import React, { useEffect, useState } from 'react';
import axios from 'axios';

const Methodology = () => {
  const [methodologies, setMethodologies] = useState([]);

  useEffect(() => {
    axios.get('/api/methodology')
      .then(response => {
        setMethodologies(response.data || []);
      })
      .catch(error => {
        console.error('Error fetching Methodology data:', error);
      });
  }, []);

    const handleScrollToTop = (event) => {
    event.preventDefault();
    const targetElement = event.currentTarget; // this is the clicked element
    if (!targetElement) return;
    const offsetTop = targetElement.getBoundingClientRect().top + window.scrollY; // adjust as needed
    window.scrollTo({
      top: offsetTop - 120,
      behavior: 'smooth',
    });
  };

  const methodologyTitle = 'Methodology';
  const chars = methodologyTitle.split('');
  const totalChars = chars.length;
  const totalDuration = 0.5; // seconds
  const delayPerChar = totalDuration / totalChars;

  return (
    <section className="single-items" id="section2">
      <div className="trriger-menu">
        <div className="row">
          <div className="col-lg-9">
            <h2 className="animated-title" data-title={methodologyTitle} onClick={ (event) => handleScrollToTop(event) }>
              {chars.map((char, i) => (
                <span
                  key={i}
                  className="fade-in-char"
                  style={{ animationDelay: `${i * delayPerChar}s` }}
                >
                  {char}
                </span>
              ))}
            </h2>
          </div>
        </div>
      </div>

      {methodologies.map((item, index) => (
        <div className="row">
          <div className="col-lg-9">
            <div className="description-wrap" key={index}>
              <div
                className="description-wrap"
                dangerouslySetInnerHTML={{ __html: item.details }}
              />
            </div>
          </div>
        </div>
      ))}
    </section>
  );
};

export default Methodology;
