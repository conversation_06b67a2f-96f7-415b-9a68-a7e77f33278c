import React, { useEffect, useState } from 'react';
import axios from 'axios';

const Methodology = () => {
  const [methodologies, setMethodologies] = useState([]);

  useEffect(() => {
    axios.get('/api/methodology')
      .then(response => {
        setMethodologies(response.data || []);
      })
      .catch(error => {
        console.error('Error fetching Methodology data:', error);
      });
  }, []);

  const methodologyTitle = 'Methodology';
  const chars = methodologyTitle.split('');
  const totalChars = chars.length;
  const totalDuration = 0.5; // seconds
  const delayPerChar = totalDuration / totalChars;

  return (
    <section className="single-items" id="section2">
      <div className="trriger-menu">
        <h2 className="animated-title" data-title={methodologyTitle}>
          {chars.map((char, i) => (
            <span
              key={i}
              className="fade-in-char"
              style={{ animationDelay: `${i * delayPerChar}s` }}
            >
              {char}
            </span>
          ))}
        </h2>
      </div>

      {methodologies.map((item, index) => (
        <div
          key={index}
          className="description-wrap"
          dangerouslySetInnerHTML={{ __html: item.details }}
        />
      ))}
    </section>
  );
};

export default Methodology;
