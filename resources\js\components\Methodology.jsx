import React, { useEffect, useState } from 'react';
import axios from 'axios';

const Methodology = () => {
  const [methodologies, setMethodologies] = useState([]);
  useEffect(() => {
    axios.get('/api/methodology') // Adjust endpoint if needed
      .then(response => {
        setMethodologies(response.data);
      })
      .catch(error => {
        console.error('Error fetching Methodology data:', error);
      });
  }, []);

  const methodologyTitlte = 'Methodology';
  const chars = methodologyTitlte.split('');
  const totalChars = chars.length;
  const totalDuration = 0.5; // seconds
  const delayPerChar = totalDuration / totalChars;

  return (
    <>
          <li className="nav-item dropdown">
            <div className="container-one single-menu-btn">
              <div className="single-menu">
                <a className="nav-link animated-title" data-title={methodologyTitlte} href="#">
                  {chars.map((char, i) => (
                    <span
                      key={i}
                      className="fade-in-char"
                      style={{ animationDelay: `${i * delayPerChar}s` }}
                    >
                      {char}
                    </span>
                  ))}
                </a>
              </div>
            </div>
            {methodologies.map((item, index) => {
              const chars = item.title.split('');
              const totalChars = chars.length;
              const totalDuration = 0.2; // seconds
              const delayPerChar = totalDuration / totalChars;
              return (
                  <div className="menucontent" key={index}>
                    <div className="description approach">
                      <div className="container-one">
                        <div className="description-wrap" dangerouslySetInnerHTML={{ __html: item.details }} />
                      </div>
                    </div>
                  </div>
              );
            })}
          </li>

    </>
  );
};

export default Methodology;
