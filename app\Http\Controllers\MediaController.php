<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\media;

class MediaController extends Controller
{
    //
    public function index(){
        $media=media::orderBy('id','Desc')->get();
        return view ('backend.media.all_media');
    }
    public function create(){
        return view('backend.media.add_media');
    }

    public function store(Request $request){
            if ($request->hasFile('image')) {
                $file = $request->file('image');

                 $filename = time() . '_' . $file->getClientOriginalName();
                   // Get the file extension
                $extension = $file->getClientOriginalExtension(); // e.g., jpg, png, pdf

               
                // Save to public/uploads
                $file->move(public_path('uploads/media'), $filename);
                // got file path
                $file_path='uploads/media/'.$filename;
                
                     $media=media::insert([
                    'image'=>$file_path,
                    'file_extention'=>$extension,
                    ]);

                // You can now use $extension as needed
                return redirect()->route('media');
        } else {
            return redirect()->back();
        }
    }
}
