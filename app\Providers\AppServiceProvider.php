<?php

namespace App\Providers;
use App\Models\siteSetting;
use Illuminate\Support\ServiceProvider;
use Illuminate\Support\Facades\View;
class AppServiceProvider extends ServiceProvider
{
    /**
     * Register any application services.
     */
    public function register(): void
    {
        //
    }

    /**
     * Bootstrap any application services.
     */
    public function boot(): void
    {
        //

         View::composer('*', function ($view) {
        $siteSettings = siteSetting::first(); // or wherever your logo is stored
        $view->with('siteLogo', $siteSettings?->logo);
    });
    }
}
