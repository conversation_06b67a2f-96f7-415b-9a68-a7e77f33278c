-- Adminer 4.7.8 MySQL dump

SET NAMES utf8;
SET time_zone = '+00:00';
SET foreign_key_checks = 0;
SET sql_mode = 'NO_AUTO_VALUE_ON_ZERO';

SET NAMES utf8mb4;

DROP TABLE IF EXISTS `cache`;
CREATE TABLE `cache` (
  `key` varchar(255) NOT NULL,
  `value` mediumtext NOT NULL,
  `expiration` int(11) NOT NULL,
  PRIMARY KEY (`key`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

INSERT INTO `cache` (`key`, `value`, `expiration`) VALUES
('<EMAIL>|**************',	'i:1;',	1747318404),
('<EMAIL>|**************:timer',	'i:1747318404;',	1747318404),
('<EMAIL>|**************',	'i:1;',	1747401661),
('<EMAIL>|**************:timer',	'i:1747401661;',	1747401661);

DROP TABLE IF EXISTS `cache_locks`;
CREATE TABLE `cache_locks` (
  `key` varchar(255) NOT NULL,
  `owner` varchar(255) NOT NULL,
  `expiration` int(11) NOT NULL,
  PRIMARY KEY (`key`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;


DROP TABLE IF EXISTS `failed_jobs`;
CREATE TABLE `failed_jobs` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `uuid` varchar(255) NOT NULL,
  `connection` text NOT NULL,
  `queue` text NOT NULL,
  `payload` longtext NOT NULL,
  `exception` longtext NOT NULL,
  `failed_at` timestamp NOT NULL DEFAULT current_timestamp(),
  PRIMARY KEY (`id`),
  UNIQUE KEY `failed_jobs_uuid_unique` (`uuid`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;


DROP TABLE IF EXISTS `general_datas`;
CREATE TABLE `general_datas` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `details` longtext NOT NULL,
  `title` varchar(255) NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

INSERT INTO `general_datas` (`id`, `details`, `title`, `created_at`, `updated_at`) VALUES
(1,	'<p style=\"box-sizing: border-box; margin: 0px; padding: 0px; text-align: justify; line-height: 1.3em; color: #212529; font-family: \'DM Mono\', monospace; font-size: 14px; background-color: #ffffff;\"><span style=\"box-sizing: border-box; font-weight: bolder;\">Climate Rights is dedicated to advancing climate justice in legal contexts. We develop methodologies for designing evidence across the geographies of climate cases. By grounding our approach in political ecology and design research, Climate Rights reframes climate litigation beyond conventional human rights and jurisdictional frameworks.</span></p>\r\n<p>&nbsp;</p>\r\n<p style=\"box-sizing: border-box; margin: 0px; padding: 0px; text-align: justify; line-height: 1.3em; color: #212529; font-family: \'DM Mono\', monospace; font-size: 14px; background-color: #ffffff;\">We need every tool at our disposal in the fight for racial and environmental justice. Across the world, from the Arctic to the Pacific, civil society is turning to courts, human rights mechanisms, and emerging legal frameworks to defend territories, safeguard traditional ways of life, and demand climate action. Unlike most human rights violations, environmental damage has cascading effects that are widespread, long-term, severe, and transboundary. These effects entangle with planetary boundaries, complicating causation. Beyond leveraging and critically examining the law, we urgently need new imaginaries. Against this background, Climate Rights investigates what constitutes evidence in the evolving landscape of climate justice, both within and beyond the courtroom.</p>',	'About',	NULL,	'2025-05-16 13:49:51'),
(2,	'<p style=\"box-sizing: border-box; margin: 0px; padding: 0px; text-align: justify; line-height: 1.3em; color: #212529; font-family: \'DM Mono\', monospace; font-size: 14px; background-color: #ffffff;\"><span style=\"box-sizing: border-box; font-weight: bolder;\">Climate change is a transversal phenomenon: its destruction cuts across borders, ecosystems, and human societies. It evades traditional institutions of deterrence and redress while undermining a broad range of rights. To confront this challenge Climate Rights leverages the analytical and creative toolkits of artistic research and architecture</span></p>\r\n<p>&nbsp;</p>\r\n<p style=\"box-sizing: border-box; margin: 0px; padding: 0px; text-align: justify; line-height: 1.3em; color: #212529; font-family: \'DM Mono\', monospace; font-size: 14px; background-color: #ffffff;\">Climate Rights is organised in four work streams:</p>\r\n<p>&nbsp;</p>\r\n<p style=\"box-sizing: border-box; margin: 0px; padding: 0px; text-align: justify; line-height: 1.3em; color: #212529; font-family: \'DM Mono\', monospace; font-size: 14px; background-color: #ffffff;\"><span style=\"box-sizing: border-box; font-weight: bolder;\">Documentation:</span>Climate Rights explores climate cases brought before judicial bodies and their evidentiary material. The project documents how civil society organisations and impacted communities are gathering, producing, and representing evidence and how this evidence is made admissible to the court and contested.</p>\r\n<p>&nbsp;</p>\r\n<p style=\"box-sizing: border-box; margin: 0px; padding: 0px; text-align: justify; line-height: 1.3em; color: #212529; font-family: \'DM Mono\', monospace; font-size: 14px; background-color: #ffffff;\"><span style=\"box-sizing: border-box; font-weight: bolder;\">Design:</span>Climate Rights develops and tests techniques for visualising geospatial data and the findings of attribution science, leveraging design technology and software and a wide array of data sources.</p>\r\n<p>&nbsp;</p>\r\n<p style=\"box-sizing: border-box; margin: 0px; padding: 0px; text-align: justify; line-height: 1.3em; color: #212529; font-family: \'DM Mono\', monospace; font-size: 14px; background-color: #ffffff;\"><span style=\"box-sizing: border-box; font-weight: bolder;\">Investigation:</span>Climate Rights undertakes case studies into high-profile investigations that represent climate rights in legal processes, building on the spatial and visual techniques of the Design stream.</p>\r\n<p>&nbsp;</p>\r\n<p style=\"box-sizing: border-box; margin: 0px; padding: 0px; text-align: justify; line-height: 1.3em; color: #212529; font-family: \'DM Mono\', monospace; font-size: 14px; background-color: #ffffff;\"><span style=\"box-sizing: border-box; font-weight: bolder;\">Participation:</span>Climate Rights goes beyond the courtroom to work directly with impacted communities, exploring participatory mapping and 3D reconstruction and testing techniques of evidence production, situation, and verification.</p>',	'Methodology',	NULL,	'2025-05-16 13:54:54'),
(3,	'<p style=\"box-sizing: border-box; margin: 0px; padding: 0px; text-align: justify; line-height: 1.3em; color: #212529; font-family: \'DM Mono\', monospace; font-size: 14px; background-color: #ffffff;\"><span style=\"box-sizing: border-box; font-weight: bolder;\">Email:</span>&nbsp;<a style=\"box-sizing: border-box; color: #0056b3; text-decoration-line: none; background-color: transparent; transition: 0.3s linear; outline: none; cursor: pointer;\" href=\"mailto:<EMAIL>\"><EMAIL></a></p>\r\n<p style=\"box-sizing: border-box; margin: 0px; padding: 0px; text-align: justify; line-height: 1.3em; color: #212529; font-family: \'DM Mono\', monospace; font-size: 14px; background-color: #ffffff;\">++<br style=\"box-sizing: border-box;\" />Logos&hellip;NTNU, climate rights, University of Chicago, NFR&hellip;<br style=\"box-sizing: border-box;\" />Research Council of Norway (Project number 335706) with additional programme funding from University of Chicago&rsquo;s John W. Boyer Center in Paris</p>',	'Contacts',	NULL,	'2025-05-16 14:22:08');

DROP TABLE IF EXISTS `investigarions`;
CREATE TABLE `investigarions` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `title` varchar(255) NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

INSERT INTO `investigarions` (`id`, `title`, `created_at`, `updated_at`) VALUES
(48,	'Øyfjellet: Land Rights Case in Sápmi',	NULL,	NULL),
(49,	'Climate Justice for Veraibari',	NULL,	NULL);

DROP TABLE IF EXISTS `investigarion_contents`;
CREATE TABLE `investigarion_contents` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `title_id` int(11) DEFAULT NULL,
  `sub_title` varchar(255) DEFAULT NULL,
  `description` longtext DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

INSERT INTO `investigarion_contents` (`id`, `title_id`, `sub_title`, `description`, `created_at`, `updated_at`) VALUES
(70,	NULL,	'new Subtitledfadfasdf',	'<p>new detailsadsfasdf</p>',	'2025-05-07 22:23:44',	'2025-05-07 22:23:44'),
(93,	48,	'Introduction',	'<p>Between 2023 and 2024, INTERPRT and Climate Rights assisted the Jillen-Njaarke reindeer herding district to review, interpret, analyze, and present visual evidence in collaboration and cooperation with Dalan Advokatfirma and Protect Sapmi. Jillen Njaarke is a Southern Sámi reindeer herding community in Vefsn municipality, or Vaapste in Southern Sapmi, in Helgeland in Nordland county.</p><p>&nbsp;</p><p>The particular focus of the work was the Øyfjellet wind power plant and its supporting infrastructure. Øyfjellet is one of Norway’s largest onshore wind energy projects, with 72 wind turbines and more than 70 km of roads covering an area of 40 square kilometers. The plant occupies a crucial migration route and grazing area of Jillen-Njaarke. The Jillen-Njaarke never consented to the green colonial project on their land – it was with this basis that they took the power plant’s operators, Øyfjellet Wind AS, to court.</p><p>&nbsp;</p><p>Climate Rights produced video analysis to support Jillen-Njaarke\'s testimony in Helgeland District Court in Mosjøen. Principal investigator of Climate Rights, Nabil Ahmed, also testified as an expert witness, presenting spatial analysis of reindeer GPS data.</p>',	'2025-05-16 15:09:51',	'2025-05-16 15:09:51'),
(94,	48,	'Background',	'<p><img src=\"http://localhost:10003/wp-content/themes/climate/assets/images/Community%20Clean-Up%20Effort%201.jpg\" alt=\"\"></p><p>&nbsp;</p><p>The Sámi, Europe’s only Indigenous people, have long faced land dispossession and cultural suppression under Norwegian policies. Beginning in the 19th century, the Norwegianisation campaign banned Sámi languages, restricted land rights, and justified marginalisation through scientific racism. While formal policies of forced assimilation ended in the 1960s, their effects persist, particularly in land disputes and legal struggles over Indigenous sovereignty.</p><p>&nbsp;</p><p>Today, the Sámi face a number of emerging threats, such as climate change and green colonialism. Reindeer herding, intrinsic to Sámi culture, is under pressure from warming winters and shrinking pastures. Yet, despite Norway’s claims of moral leadership on climate, it remains a major oil and gas exporter, while Indigenous lands in Norway are being dispossessed under the guise of a green transition.</p><p>&nbsp;</p><p>This project began as a commission from the 2023 Helsinki Biennale, where INTERPRT had produced the exhibition&nbsp;<i>COLONIAL PRESENT:</i>&nbsp;Counter-mapping the Truth and Reconciliation Commissions in Sápmi. The central question of the exhibition was: “How can past injustices be reconciled within the ongoing structures of colonialism?” To answer it, we interviewed and met with Sámi activists, archeologists, historians, journalists, and researchers and examined cartographic evidence, photographs and other archival sources. The relationships we built and the research we uncovered during this process led to our involvement in the Jillen-Njaarke legal action.</p><p>&nbsp;</p><p>The Øyfjellet case was preceded by the Fosen wind power project, which the Norwegian Supreme Court declared illegal in 2021. Despite the ruling, the turbines remain in operation, prompting mass Sámi-led protests in 2023. A similar conflict is unfolding in Helgeland, where the Øyfjellet project was built without Free, Prior, and Informed Consent (FPIC) from the Jillen-Njaarke reindeer herding community.</p><p>&nbsp;</p><p>Øyfjellet Wind AS—owned by a German investment firm—has received direct state support despite the ongoing legal process. Both Fosen and Øyfjellet lie in Southern Sápmi, home to just 600 speakers of the endangered Southern Sámi language. For this minority, green colonial projects threaten their language, culture, and way of life. With no fair outcome at the district court, Jillen-Njaarke is now preparing to appeal to the Supreme Court.</p>',	'2025-05-16 15:09:51',	'2025-05-16 15:09:51'),
(95,	48,	'Methodology',	'<p>The methodology developed for the Øyfjellet case was based on collaborative investigative methods that redefine evidentiary practices and foregrounded the perspectives of Sámi herders, whose ancestral knowledge encompasses reindeer herding practices, the animals, and the landscape. Like many other Indigenous communities, the Sámi have a legitimate concern that their knowledge is often excluded from legal-scientific contexts, despite its widely recognized importance in environmental protection and climate change mitigation.</p><p>&nbsp;</p><p>At the core of this method is a state-of-the-art 3D environment that integrates spatial and environmental data, multimedia evidence, and testimonies into an immersive digital model built in Unreal Engine. This digital environment functions as a navigable database for legal evidence and analysis, offering a dynamic tool for visualizing environmental change and its impacts on Sámi herding practices.</p><p>&nbsp;</p><p>Participatory workshops with Jillen-Njaarke Sámi herders, including Torstein Appfjell, Ole Henrik Kappfjell, and Nils Anders Appfjell, played a crucial role in ensuring that the data and visualizations accurately reflected their lived experiences and traditional knowledge. These workshops facilitated knowledge exchange, allowing herders to directly contribute to the representation of their landscape and its transformations. Aerial videography, captured via UAV by Jillen-Njaarke herder Ole Henrik Kappfjellallowed us to ground-truth and validate our digital representations in real-world conditions.</p><div class=\"single-images\"><p><video src=\"http://localhost:10003/wp-content/themes/climate/assets/images/Community%20Clean-Up%20Effort%201.jpg\" poster=\"http://localhost:10003/wp-content/themes/climate/assets/images/Community%20Clean-Up%20Effort%201.jpg\" width=\"300\" height=\"150\" data-mce-fragment=\"1\">\r\n</video></p><div pseudo=\"-webkit-media-controls\" class=\"sizing-small phase-pre-ready state-no-source\">\r\n<div pseudo=\"-webkit-media-controls-enclosure\"></div>\r\n</div>\r\n<p></p><p></p></div><div class=\"single-images\">&nbsp;</div>',	'2025-05-16 15:09:51',	'2025-05-16 15:09:51'),
(96,	48,	'Spring Migration Video Analysis',	'<p>Climate Rights/INTERPRT conducted a counter-analysis of video evidence submitted by Øyfjellet Wind to support Jillen-Njaarke’s court testimony. The footage depicted two distinct scenarios: reindeer being forcibly pushed by helicopters flying just meters above them and a seemingly calm herd unaffected by the turbines. Øyfjellet Wind claimed that any disturbance was caused by helicopters, not the wind turbines.</p><p>&nbsp;</p><div class=\"single-images\"><img src=\"http://localhost:10003/wp-content/themes/climate/assets/images/March%20for%20Climate%20Justice%201.jpg\" alt=\"\"></div><p>&nbsp;</p><p>To assess this claim, we conducted a video-to-space analysis by geolocating the footage, camera positions, and the herd within a 3D environment, incorporating firsthand accounts from reindeer herders. Our analysis revealed that the ‘calm’ reindeer were too far from the turbines to be affected. However, footage captured from a great distance with a long-focus lens created a compressed perspective effect, making the reindeer appear much closer to the turbines than they actually were.</p><p>&nbsp;</p><div class=\"single-images\"><img src=\"http://localhost:10003/wp-content/themes/climate/assets/images/March%20for%20Climate%20Justice%201.jpg\" alt=\"\"></div><p>&nbsp;</p><p>In reality, reindeer near the turbines had to be relocated by helicopter, directly contradicting the company’s claim that the wind power plant did not disrupt traditional herding practices. Our analysis also exposed the contrast between the company\'s narrative and actual migration patterns. Normally, migration takes several weeks, allowing reindeer to graze and rest, but the wind power plant reduced this to just three days, forcing herders to rely on helicopters.</p><p>&nbsp;</p><p>The 3D reconstruction helped herders to demonstrate how the company’s silent footage misrepresented the situation and obscured the wind power plant impact on spring migration. To our knowledge, this was the first time when a game-engine-driven, data-based 3D model was used as legal evidence in Norway for a frontline community in a climate and land rights case.</p>',	'2025-05-16 15:09:51',	'2025-05-16 15:09:51'),
(97,	48,	'ØYFJELLET – From the Frontline of Land Rights in Sápmi',	'<p>As part of our dissemination efforts—and in response to the overwhelmingly one-sided media coverage favoring the wind power company—Climate Rights, in collaboration with INTERPRT, produced the exhibition ØYFJELLET – From the Frontline of Land Rights in Sápmi at Stormen Public Library in Bodø.</p><p>&nbsp;</p><p>The exhibition explores the question: How can we connect the broader responsibility for planetary climate change in the Arctic to local struggles, such as the Sámi’s fight against green colonialism? As part of Climate Rights project on designing evidence for climate justice and land rights protection, the exhibition brings together maps, video, legal evidence, and an interactive 3D model—developed in collaboration with the Jillen-Njaarke reindeer herding community—to document their fight against Øyfjellet Wind in the courtroom and beyond.</p><p>&nbsp;</p><div class=\"single-images\"><img src=\"http://localhost:10003/wp-content/themes/climate/assets/images/March%20for%20Climate%20Justice%201.jpg\" alt=\"\"></div>',	'2025-05-16 15:09:51',	'2025-05-16 15:09:51'),
(98,	49,	'Introduction',	'<p>Climate Rights is developing a multimedia report on the impacts of climate change on Veraibari Village in the Kikori Delta, Papua New Guinea (PNG). The investigation aims to establish a material link between climate change and the forced relocation of the Veraibari community due to shoreline erosion. By integrating testimonies within a 3D environment and reconstructing the village before and after climate-induced changes using participatory mapping and geospatial data analysis, the project seeks to produce visual evidence of the lived experiences of the community, including the loss of homes, sacred places, and biodiversity.</p>',	'2025-05-16 15:11:40',	'2025-05-16 15:11:40'),
(99,	49,	'Background',	'<div class=\"single-images\"><img src=\"http://localhost:10003/wp-content/themes/climate/assets/images/Community%20Clean-Up%20Effort%201.jpg\" alt=\"\"></div><p>&nbsp;</p><p>Veraibari is a coastal village located at the mouth of the Kikori River—known as Ouro in the local Urama language—where the river flows into the Kikori Delta and meets the Gulf of Papua. The community, part of the Paia\'a tribe, is one of only 11 villages where the endangered Urama language is still spoken, with deep ancestral and cultural ties to the land and sea.</p><p>&nbsp;</p><p>The history of Veraibari is deeply intertwined with the impacts of climate change. In the early 1980s, the nearby village of Damaibari—once located behind Veraibari on the other side of the island—was completely destroyed by rising sea levels, forcing the community to relocate to what is now Veraibari. Today, Veraibari remains one of the few surviving villages in the region, but it faces increasing environmental threats.</p><p>&nbsp;</p><div class=\"single-images\"><img src=\"http://localhost:10003/wp-content/themes/climate/assets/images/March%20for%20Climate%20Justice%201.jpg\" alt=\"\"></div><p>&nbsp;</p><p>This province is rich in resources, with extensive logging and extractive industries, including oil and gas projects operated by Exxon and Total. However, communities like Veraibari are left to bear the brunt of climate change impacts, without receiving any meaningful economic benefits from these industries. In Veraibari, these impacts include rising sea levels, storm surges, the previously unexperienced occurrence of cyclones, the decline and extinction of marine life, as well as the degradation of mangroves and other plant species. Freshwater sources have been damaged or contaminated, and the community faces the gradual loss of cultural heritage, social cohesion, and traditional political structures.</p><p>&nbsp;</p><div class=\"single-images\">&nbsp;</div><p>&nbsp;</p><p>The project began as a short multimedia evidence video to support the Melanesian Spearhead Group (MSG) in their oral statements to the UN’s International Court of Justice in December 2024. In partnership with INTERPRT and in collaboration with BlueOceanLaw, a field trip was conducted in Veraibari.</p><p>&nbsp;</p><div class=\"single-images\"><img src=\"http://localhost:10003/wp-content/themes/climate/assets/images/March%20for%20Climate%20Justice%201.jpg\" alt=\"\"></div><p>&nbsp;</p><p>The fieldwork was made possible through the longstanding efforts and community engagement of the Piku Biodiversity Network, a local grassroots organization dedicated to environmental conservation of aquatic species and Indigenous advocacy. The project is continuing working closely with the Piku Biodiversity Network to develop an expanded multimedia report.</p>',	'2025-05-16 15:11:40',	'2025-05-16 15:11:40'),
(100,	49,	'Methodology',	'<p>Our methodology integrates fieldwork with visual-spatial investigations, including remote sensing, GIS modeling, climate science, and legal research. During fieldwork, we video recorded testimonies and organized three days long participatory mapping workshops on the impacts of climate change in Veraibari. We also conducted aerial surveys using UAV drones, along with detailed video and image documentation, which formed the basis for developing an accurate 3D environment in Unreal Engine in collaboration with INTERPRT. This 3D environment integrates testimonies and participatory workshops, visual evidence, and field recordings along with geospatial, environmental, and climate data.</p><p>&nbsp;</p><p>Our next steps focus on extending the video to reconstruct the historical landscape in more detail, develop visual strategies in representing cultural and biodiversity loss based on the participatory workshops and analyzing the role of anthropogenic climate change</p><p>&nbsp;</p><div class=\"row\"><div class=\"col-md-4\"><img src=\"http://localhost:10003/wp-content/themes/climate/assets/images/Climate%20Refugees%201.jpg\" alt=\"\"></div><div class=\"col-md-4\"><img src=\"http://localhost:10003/wp-content/themes/climate/assets/images/Community%20Clean-Up%20Effort%201.jpg\" alt=\"\"></div><div class=\"col-md-4\"><img src=\"http://localhost:10003/wp-content/themes/climate/assets/images/Renewable%20Energy%20Revolution%201.jpg\" alt=\"\"></div></div>',	'2025-05-16 15:11:40',	'2025-05-16 15:11:40');

DROP TABLE IF EXISTS `jobs`;
CREATE TABLE `jobs` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `queue` varchar(255) NOT NULL,
  `payload` longtext NOT NULL,
  `attempts` tinyint(3) unsigned NOT NULL,
  `reserved_at` int(10) unsigned DEFAULT NULL,
  `available_at` int(10) unsigned NOT NULL,
  `created_at` int(10) unsigned NOT NULL,
  PRIMARY KEY (`id`),
  KEY `jobs_queue_index` (`queue`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;


DROP TABLE IF EXISTS `job_batches`;
CREATE TABLE `job_batches` (
  `id` varchar(255) NOT NULL,
  `name` varchar(255) NOT NULL,
  `total_jobs` int(11) NOT NULL,
  `pending_jobs` int(11) NOT NULL,
  `failed_jobs` int(11) NOT NULL,
  `failed_job_ids` longtext NOT NULL,
  `options` mediumtext DEFAULT NULL,
  `cancelled_at` int(11) DEFAULT NULL,
  `created_at` int(11) NOT NULL,
  `finished_at` int(11) DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;


DROP TABLE IF EXISTS `migrations`;
CREATE TABLE `migrations` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `migration` varchar(255) NOT NULL,
  `batch` int(11) NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES
(1,	'0001_01_01_000000_create_users_table',	1),
(2,	'0001_01_01_000001_create_cache_table',	1),
(3,	'0001_01_01_000002_create_jobs_table',	1),
(4,	'2025_05_06_064437_create_general_datas_table',	2),
(5,	'2025_05_06_064900_create_general_datas_table',	3),
(6,	'2025_05_06_065102_create_general_datas_table',	4),
(7,	'2025_05_06_075422_create_news_table',	5),
(8,	'2025_05_06_081020_create_teams_table',	6),
(9,	'2025_05_06_084254_create_investigarions_table',	7),
(10,	'2025_05_06_091311_create_investigarion_contents_table',	8),
(11,	'2025_05_08_052623_create_team_categories_table',	9);

DROP TABLE IF EXISTS `news`;
CREATE TABLE `news` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `title` varchar(255) DEFAULT NULL,
  `publish_date` varchar(255) DEFAULT NULL,
  `short_description` longtext DEFAULT NULL,
  `redirect_link` varchar(255) DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

INSERT INTO `news` (`id`, `title`, `publish_date`, `short_description`, `redirect_link`, `created_at`, `updated_at`) VALUES
(2,	'Workshop with Protect Sapmi',	'1-2 February 2024',	'<p>Climate rights and Protect Sapmi run a joint workshop on developing evidentiary approaches to Sami land rights and reindeer herding</p>',	NULL,	NULL,	'2025-05-16 14:53:26'),
(3,	'Seminar #1 in Trondheim',	'2–4 March 2024',	'<p>Climate Rights organises its first international meeting of its collaborators at the Trondheim Academy of Fine Art (KiT).</p>',	NULL,	NULL,	'2025-05-16 14:53:11'),
(4,	'Promise of International law in the face of ecological crisis conference, Amsterdam',	'27–29 May 2024',	'<p>Professor Nabil Ahmed speaks at the practitoners&rsquo; forum at the conference co-organized by the Promise Institute Europe, UCLA School of Law and the University of Amsterdam (<a href=\"https://www.promiseeurope.law.ucla.edu/programme\">https://www.promiseeurope.law.ucla.edu/programme</a>)</p>',	NULL,	NULL,	'2025-05-16 14:52:58'),
(5,	'Expert witness testimony in the Øyfjellet case',	'10 June 2024',	'<p>Professor Nabil Ahmed gives expert witness testimony in the &Oslash;yfjellet case in Mosjoen district court in Mosjoen, Norway</p>',	NULL,	NULL,	'2025-05-16 14:50:16'),
(6,	'Video evidence in the Øyfjellet case',	'3–7 June 2024',	'<p>Climate Rights&rsquo; video evidence files support the testimony of Torstein Appfjell, leader of the Jillen Njaarke reindeer herding community in the &Oslash;yfjellet case in Mosjoen district court, Mosjoen, Norway</p>',	NULL,	NULL,	'2025-05-16 14:52:27'),
(7,	'Expedition to Veraibari, Papua New Guinea',	'21–30 October 2024',	'<p>Professor Nabil Ahmed and Oskar Johanson join conservationists from PIKU Biodiversity Group and lawyers from Blue Ocean Law on a field trip to the village of Veraibari, in the Gulf Region of Papua New Guinea. The team was there to document the impacts of climate change on the village, including recording situated testimonies by community leaders.</p>',	NULL,	NULL,	'2025-05-16 14:52:13'),
(8,	'Oral hearings of the ICJ Advisory Opinion on Climate Change in The Hague',	'2 December 2024',	'<p>Professor Nabil Ahmed and Oskar Johanson were in The Hague as part of the delegation for the Melanesian Spearhead Group for the oral proceedings of the Advisory Opinion regarding Obligations of States in respect of Climate Change at the International Court of Justice.</p>',	NULL,	NULL,	'2025-05-16 14:51:59'),
(9,	'Exhibition in Stormen library',	'14 Decemeber 2024',	'<p>Climate Rights opens &Oslash;YFJELLET: From the Frontline of Land Rights in S&aacute;pmi at Stormen kunst/d&aacute;jdda, Bod&oslash; in collaboration with Artica Svalbard. The exhibition focuses on the Climate Rights/INTERPRT investigation&nbsp;<a href=\"https://www.articasvalbard.no/2024/exhibition-from-the-frontline-of-land-rights-in-spmi\">https://www.articasvalbard.no/2024/exhibition-from-the-frontline-of-land-rights-in-spmi</a></p>',	NULL,	NULL,	'2025-05-16 14:51:42'),
(10,	'Climate Rights is recruiting: Call for PhD and postdoc applications',	NULL,	'<p><a href=\"https://www.e-flux.com/announcements/644998/climate-rights-call-for-phd-and-postdoc-applications/\" target=\"_blank\" rel=\"noopener\">Climate Rights is recruiting: Call for PhD and postdoc applications</a></p>',	NULL,	NULL,	NULL),
(11,	'Seminar #2 in Paris>',	'Upcoming',	'<p>Spring seminar at University of Chicago&rsquo;s John W. Boyer Center in Paris.</p>',	NULL,	NULL,	'2025-05-16 14:51:24');

DROP TABLE IF EXISTS `password_reset_tokens`;
CREATE TABLE `password_reset_tokens` (
  `email` varchar(255) NOT NULL,
  `token` varchar(255) NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`email`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;


DROP TABLE IF EXISTS `sessions`;
CREATE TABLE `sessions` (
  `id` varchar(255) NOT NULL,
  `user_id` bigint(20) unsigned DEFAULT NULL,
  `ip_address` varchar(45) DEFAULT NULL,
  `user_agent` text DEFAULT NULL,
  `payload` longtext NOT NULL,
  `last_activity` int(11) NOT NULL,
  PRIMARY KEY (`id`),
  KEY `sessions_user_id_index` (`user_id`),
  KEY `sessions_last_activity_index` (`last_activity`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

INSERT INTO `sessions` (`id`, `user_id`, `ip_address`, `user_agent`, `payload`, `last_activity`) VALUES
('BYQNdyQD0GQPMR932pYbBMKpVvshmilJ6AvTWbXk',	1,	'**************',	'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',	'YTo1OntzOjY6Il90b2tlbiI7czo0MDoib2tGbkNSUm12THdUamd2TFRQeFloRk5kVGtJUW5DU3A4b3pTTUp6QyI7czo5OiJfcHJldmlvdXMiO2E6MTp7czozOiJ1cmwiO3M6NTg6Imh0dHBzOi8vcGhwbGFyYXZlbC0xMzEyNTcyLTU0OTYyMTIuY2xvdWR3YXlzYXBwcy5jb20vYWRtaW4iO31zOjY6Il9mbGFzaCI7YToyOntzOjM6Im9sZCI7YTowOnt9czozOiJuZXciO2E6MDp7fX1zOjM6InVybCI7YTowOnt9czo1MDoibG9naW5fd2ViXzU5YmEzNmFkZGMyYjJmOTQwMTU4MGYwMTRjN2Y1OGVhNGUzMDk4OWQiO2k6MTt9',	1747638238),
('FxqUNCldo6WXmwIdr7OcmQGEXVUKWVB9UeFElikb',	NULL,	'*************',	'Slackbot-LinkExpanding 1.0 (+https://api.slack.com/robots)',	'YTozOntzOjY6Il90b2tlbiI7czo0MDoiNWhZNndXdmZGVmNnanBlVEptbzNxUHJ0QnR0UUdBNmdzcG5lQlBjayI7czo5OiJfcHJldmlvdXMiO2E6MTp7czozOiJ1cmwiO3M6NTg6Imh0dHBzOi8vcGhwbGFyYXZlbC0xMzEyNTcyLTU0OTYyMTIuY2xvdWR3YXlzYXBwcy5jb20vbG9naW4iO31zOjY6Il9mbGFzaCI7YToyOntzOjM6Im9sZCI7YTowOnt9czozOiJuZXciO2E6MDp7fX19',	1747403496),
('KT5omswnSk9FYIkRJAfA0B2DWPm7efjuj04SgH2u',	5,	'**************',	'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',	'YTo0OntzOjY6Il90b2tlbiI7czo0MDoiNUhPT2lnWjZ1cTRScmFDQTVwYTh2RUdvUFI0cXNvSjhUR3Y1NklQOSI7czo1MDoibG9naW5fd2ViXzU5YmEzNmFkZGMyYjJmOTQwMTU4MGYwMTRjN2Y1OGVhNGUzMDk4OWQiO2k6NTtzOjk6Il9wcmV2aW91cyI7YToxOntzOjM6InVybCI7czo2NjoiaHR0cHM6Ly9waHBsYXJhdmVsLTEzMTI1NzItNTQ5NjIxMi5jbG91ZHdheXNhcHBzLmNvbS9pbnZlc3RpZ2F0aW9uIjt9czo2OiJfZmxhc2giO2E6Mjp7czozOiJvbGQiO2E6MDp7fXM6MzoibmV3IjthOjA6e319fQ==',	1747634863),
('LGvoD5py2yMWtqv6igMl49P2V43AyzWySquAhQWL',	NULL,	'**************',	'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',	'YTozOntzOjY6Il90b2tlbiI7czo0MDoiQzRFVGpmclVqTHdJR0YxYzZyek5MQXlCM2U4eDVncGN0M0w2MjE5TyI7czo5OiJfcHJldmlvdXMiO2E6MTp7czozOiJ1cmwiO3M6NTI6Imh0dHBzOi8vcGhwbGFyYXZlbC0xMzEyNTcyLTU0OTYyMTIuY2xvdWR3YXlzYXBwcy5jb20iO31zOjY6Il9mbGFzaCI7YToyOntzOjM6Im9sZCI7YTowOnt9czozOiJuZXciO2E6MDp7fX19',	1747663544),
('m9wKMwQZHdlFwgDYGDk5VKBKdKbKav04Zd2DNzIA',	NULL,	'*************',	'Slackbot-LinkExpanding 1.0 (+https://api.slack.com/robots)',	'YTozOntzOjY6Il90b2tlbiI7czo0MDoiTzlscmlySzVuS1VLV2VZSkVNc2tvcU10YmJENGVLckZjNVdoSndJRyI7czo5OiJfcHJldmlvdXMiO2E6MTp7czozOiJ1cmwiO3M6NTI6Imh0dHBzOi8vcGhwbGFyYXZlbC0xMzEyNTcyLTU0OTYyMTIuY2xvdWR3YXlzYXBwcy5jb20iO31zOjY6Il9mbGFzaCI7YToyOntzOjM6Im9sZCI7YTowOnt9czozOiJuZXciO2E6MDp7fX19',	1747401827),
('nh9gs8IXJ6bTmyA6jWE4ptAb7lNlyxwAXQzx1VlF',	1,	'**************',	'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',	'YTo1OntzOjY6Il90b2tlbiI7czo0MDoidkxkRVhaU2tUY0lFeDVjTVE4UzYwSjhuZ2Y4WHpOeGF2WHRUdU9mUiI7czo2OiJfZmxhc2giO2E6Mjp7czozOiJvbGQiO2E6MDp7fXM6MzoibmV3IjthOjA6e319czo5OiJfcHJldmlvdXMiO2E6MTp7czozOiJ1cmwiO3M6NTg6Imh0dHBzOi8vcGhwbGFyYXZlbC0xMzEyNTcyLTU0OTYyMTIuY2xvdWR3YXlzYXBwcy5jb20vYWJvdXQiO31zOjM6InVybCI7YTowOnt9czo1MDoibG9naW5fd2ViXzU5YmEzNmFkZGMyYjJmOTQwMTU4MGYwMTRjN2Y1OGVhNGUzMDk4OWQiO2k6MTt9',	1747406652),
('NqQvFuY7NcB62FhdIU5TArhzDOpQq2ZcNnrxGP9H',	5,	'**************',	'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',	'YTo0OntzOjY6Il90b2tlbiI7czo0MDoiSWRmcTZPWHhKQTBLeGJKMmpSWUNSdXl1V0E3S3lhWmJPZktyM2I1YiI7czo2OiJfZmxhc2giO2E6Mjp7czozOiJvbGQiO2E6MDp7fXM6MzoibmV3IjthOjA6e319czo5OiJfcHJldmlvdXMiO2E6MTp7czozOiJ1cmwiO3M6NjY6Imh0dHBzOi8vcGhwbGFyYXZlbC0xMzEyNTcyLTU0OTYyMTIuY2xvdWR3YXlzYXBwcy5jb20vaW52ZXN0aWdhdGlvbiI7fXM6NTA6ImxvZ2luX3dlYl81OWJhMzZhZGRjMmIyZjk0MDE1ODBmMDE0YzdmNThlYTRlMzA5ODlkIjtpOjU7fQ==',	1747409014),
('OicDTemHGZuVYC7tJ6yYhFoRVNcvgtZRKuZruhHb',	5,	'78.67.214.36',	'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',	'YTo0OntzOjY6Il90b2tlbiI7czo0MDoiUGZYWDVjSlJZYzJlYzk2cFBTSEVHOHAxNFBQZ2pJcjBxclBzbklUZiI7czo5OiJfcHJldmlvdXMiO2E6MTp7czozOiJ1cmwiO3M6NTg6Imh0dHBzOi8vcGhwbGFyYXZlbC0xMzEyNTcyLTU0OTYyMTIuY2xvdWR3YXlzYXBwcy5jb20vYWRtaW4iO31zOjY6Il9mbGFzaCI7YToyOntzOjM6Im9sZCI7YTowOnt9czozOiJuZXciO2E6MDp7fX1zOjUwOiJsb2dpbl93ZWJfNTliYTM2YWRkYzJiMmY5NDAxNTgwZjAxNGM3ZjU4ZWE0ZTMwOTg5ZCI7aTo1O30=',	1747404545),
('QWxCuLdgplC9Rkps5sMWsDEVke5kZ9KX1L7N0ubc',	1,	'**************',	'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',	'YTo1OntzOjY6Il90b2tlbiI7czo0MDoienBMSFdSTTJra1VCMUNYVW5UMlBkNVhjcVRSOEJxYW1WYldzb0dFcSI7czo5OiJfcHJldmlvdXMiO2E6MTp7czozOiJ1cmwiO3M6NTI6Imh0dHBzOi8vcGhwbGFyYXZlbC0xMzEyNTcyLTU0OTYyMTIuY2xvdWR3YXlzYXBwcy5jb20iO31zOjY6Il9mbGFzaCI7YToyOntzOjM6Im9sZCI7YTowOnt9czozOiJuZXciO2E6MDp7fX1zOjM6InVybCI7YTowOnt9czo1MDoibG9naW5fd2ViXzU5YmEzNmFkZGMyYjJmOTQwMTU4MGYwMTRjN2Y1OGVhNGUzMDk4OWQiO2k6MTt9',	1747402766);

DROP TABLE IF EXISTS `site_settings`;
CREATE TABLE `site_settings` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `logo` varchar(255) DEFAULT NULL,
  `meta_title` varchar(255) NOT NULL,
  `meta_description` text NOT NULL,
  `meta_author` varchar(255) NOT NULL,
  `meta_keywords` varchar(255) NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

INSERT INTO `site_settings` (`id`, `logo`, `meta_title`, `meta_description`, `meta_author`, `meta_keywords`, `created_at`, `updated_at`) VALUES
(6,	'uploads/settings/1747379288_logo.png',	'Climet',	'<p>fadfadsfadfadfadfsfgsfgadfad</p>',	'afafadsfadsf',	'afadfadsaadfadfdfadfsfgsfgasdfad',	NULL,	'2025-05-16 01:52:51'),
(7,	'uploads/settings/1747402069_1747379288_logo.png',	'Climet',	'<p>fadfadsfadfadfadfsfgsfgadfad</p>',	'afafadsfadsf',	'afadfadsaadfadfdfadfsfgsfgasdfad',	NULL,	NULL);

DROP TABLE IF EXISTS `teams`;
CREATE TABLE `teams` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `name` varchar(255) NOT NULL,
  `position` varchar(255) DEFAULT NULL,
  `details` longtext DEFAULT NULL,
  `category_name` varchar(255) DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

INSERT INTO `teams` (`id`, `name`, `position`, `details`, `category_name`, `created_at`, `updated_at`) VALUES
(8,	'Nabil Ahmed',	'Principal Investigator (NTNU)',	'<p><span style=\"color: rgb(33, 37, 41); font-family: &quot;DM Mono&quot;, monospace; text-align: justify; background-color: rgb(255, 255, 238);\">Nabil Ahmed is Professor of Visual Intervention at the Trondheim Academy of Fine Art (KiT) in the faculty of architecture and design at Norwegian University of Science and Technology (NTNU). For over 15 years, his spatial practice and writing have examined the aesthetic, representational, and legal challenges of environmental destruction and conflict across visual culture, environmental humanities, and international law. He is the founder and co-director of INTERPRT, a research agency dedicated to environmental justice through spatial and visual investigations.</span></p>',	'Main Team',	NULL,	'2025-05-16 14:11:54'),
(9,	'Alexander Arroyo',	'Co-Investigator (University of Chicago)',	'<p><span style=\"color: rgb(33, 37, 41); font-family: &quot;DM Mono&quot;, monospace; text-align: justify; background-color: rgb(255, 255, 238);\">Alexander Arroyo is Associate Director and Senior Research Associate in Global Political Ecology at the Urban Theory Lab, University of Chicago, where he is also affiliated faculty with the Committee on Environment, Geography, and Urbanization. Drawing on training in the critical social sciences, humanities, and design, his work explores relations between the environmental geographies, infrastructural formations, and spatial imaginaries of American empire. His first book, Ecologies of Power (MIT Press, 2016), co-authored with Pierre Bélanger, was awarded the John Brinkerhoff Jackson Book Prize for Landscape Studies.</span></p>',	'Main Team',	NULL,	'2025-05-16 14:11:48'),
(10,	'Oskar Johanson',	NULL,	'<p><span style=\"color: rgb(33, 37, 41); font-family: &quot;DM Mono&quot;, monospace; text-align: justify; background-color: rgb(255, 255, 238);\">Oskar Frederick Johanson is a PhD candidate at NTNU with a background in architectural design, writing, and research. Since 2021 he has been co-program head of the AA Visiting School Sydney. From 2021-22 he was an Agent of Change for the 10th Architecture Biennale Rotterdam. He has taught at the University of Sydney and the AA and written for AA Files, The Avery Review, and The Sydney Morning Herald, among others.</span></p>',	'Main Team',	NULL,	'2025-05-16 14:11:29'),
(11,	'OIga Lucko',	NULL,	'<p><span style=\"color: rgb(33, 37, 41); font-family: &quot;DM Mono&quot;, monospace; text-align: justify; background-color: rgb(255, 255, 238);\">Olga is an architect and co-director of INTERPRT. She develops innovative visual and spatial methodologies and is technical lead for INTERPRT’s investigations. Currently, Olga is pursuing her doctoral studies at NTNU KiT in artistic research, where she explores representational methodologies to critically investigate the emerging deep sea mining industry. She studied architecture at the School of Architecture at London Metropolitan University, and is an ARB registered architect with experience working on public building projects in the UK.</span></p>',	'Main Team',	NULL,	NULL),
(12,	'Grga Basic',	NULL,	'<p><span style=\"color: rgb(33, 37, 41); font-family: &quot;DM Mono&quot;, monospace; text-align: justify; background-color: rgb(255, 255, 238);\">Grga Bašić is Senior Research Associate in Cartography and Spatial Media at the Urban Theory Lab, University of Chicago, where he is also affiliated faculty with the Committee on Environment, Geography, and Urbanization. His work draws together approaches from urbanism, media, digital art, and journalism. His cartographic work has been commissioned and exhibited widely, most recently by the Fondation Cartier pour l’art contemporain and the Venice Biennale of Architecture.</span></p>',	'Main Team',	NULL,	NULL),
(13,	'Sol Kim',	NULL,	'<p><span style=\"color: rgb(33, 37, 41); font-family: &quot;DM Mono&quot;, monospace; text-align: justify; background-color: rgb(255, 255, 238);\">Sol Kim is Research Associate in Planetary Environmental Transformation at the Urban Theory Lab, University of Chicago, where he is also affiliated faculty with the Committee on Environment, Geography, and Urbanization. Trained as a climate scientist (PhD, UC Berkeley Geography) with a particular interest in weather/climate extremes, his research has focused on various aspects of atmospheric rivers, heat waves, and droughts. Before joining the UTL, he worked with earth observations at NASA\'s Jet Propulsion Lab, the European Space Agency, and conducted independent research through a Fulbright grant in Finland.</span></p>',	'Main Team',	NULL,	NULL),
(14,	'Tanya Mangion',	'PhD candidate, Department of computer science, NTNU',	NULL,	'Affliate Researchers',	NULL,	NULL),
(15,	'Margarita Torrijos',	'PhD candidate, Trondheim Academy of Fine Art, NTNU',	NULL,	'Affliate Researchers',	NULL,	NULL),
(16,	'Kristian Byskov',	'PhD candidate, Trondheim Academy of Fine Art, NTNU',	NULL,	'Affliate Researchers',	NULL,	NULL),
(17,	'Dr Margaretha Wewerinke-Singh',	'(University of Amsterdam)',	NULL,	'Collaborators',	NULL,	'2025-05-16 14:15:43'),
(18,	'Jamon Van den Hoek',	'(Oregon State University)',	NULL,	'Collaborators',	NULL,	NULL),
(19,	'Ricardo Torres',	'(Wageningen University)',	NULL,	'Collaborators',	NULL,	NULL);

DROP TABLE IF EXISTS `team_categories`;
CREATE TABLE `team_categories` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `category_name` varchar(255) NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

INSERT INTO `team_categories` (`id`, `category_name`, `created_at`, `updated_at`) VALUES
(2,	'Main Team',	NULL,	NULL),
(3,	'Affliate Researchers',	NULL,	NULL),
(4,	'Collaborators',	NULL,	NULL);

DROP TABLE IF EXISTS `users`;
CREATE TABLE `users` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `name` varchar(255) NOT NULL,
  `email` varchar(255) NOT NULL,
  `email_verified_at` timestamp NULL DEFAULT NULL,
  `password` varchar(255) NOT NULL,
  `remember_token` varchar(100) DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `users_email_unique` (`email`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

INSERT INTO `users` (`id`, `name`, `email`, `email_verified_at`, `password`, `remember_token`, `created_at`, `updated_at`) VALUES
(1,	'Shifat E Rasul',	'<EMAIL>',	NULL,	'$2y$12$xqGdUgDzp210vdjmuXSdt.EPWlaQMHjxojzd7v8/Ovd8KuecZ.lTe',	'd1UjjugvczAoF9wsKCtHIGhIwm5K0QtkUa7dIUwLWsXajxCCnFPTC4nyQt9X',	'2025-05-06 04:19:00',	'2025-05-06 04:19:00'),
(2,	'Admin',	'<EMAIL>',	NULL,	'$2y$12$EqHtUGM7848vbDKhPgczo.rkxbNLOy6ALNAibE3Rzzs0QOIxVjlsS',	NULL,	'2025-05-07 04:49:23',	'2025-05-07 04:49:23'),
(3,	'Galib',	'<EMAIL>',	NULL,	'$2y$12$9.KcDJWzq8m89SBnt4ixLehEBUAZPhF5lJe3yfTRopqQbypWMzBJ6',	NULL,	'2025-05-15 13:43:04',	'2025-05-15 13:43:04'),
(4,	'Egenslab LTD.',	'<EMAIL>',	NULL,	'$2y$12$oRQFwPOq5SYzAZREh73xYOWgB0458Uh9JwRQoO0dUa6STlkLmRqJC',	NULL,	'2025-05-15 14:12:56',	'2025-05-15 14:12:56'),
(5,	'james.​dose',	'<EMAIL>',	NULL,	'$2y$12$mA7rWXgXZBNdV34KTdddw.HhiFI0go.SV5E5IyYvwGl5.Az2ym4Nm',	'rAvO7UEcqG9DlaeMPjgTlVOfXIHEfCYIb4vMPA897YlP8xzvNJphhcgmjgwo',	'2025-05-16 13:45:55',	'2025-05-16 13:45:55');

-- 2025-05-19 14:09:49