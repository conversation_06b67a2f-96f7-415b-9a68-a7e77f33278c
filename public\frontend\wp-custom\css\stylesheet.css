body,
html {
    margin: 0;
    /* overflow-x: hidden;
        overflow-y: auto; */
    font-family: "DM Mono", monospace;
}

.container-fluid {
    min-height: 100vh;
}

.sidebar {
    background-color: transparent;
    color: #666;
    position: sticky;
    top: 106px;
    left: 0;
    width: 100%;
    height: 100vh;
    overflow-y: auto;
    padding: 2rem 1rem;
    padding-left: 0;
    border-right: 1px solid #e9ecef;
    z-index: 1000;
    opacity: 0.4;
    transition: opacity 0.5s ease-out;
}
.sidebar:hover {
    opacity: 1;
}

.nav-item {
    margin-bottom: 0.5rem;
}

.nav-link {
    color: #666;
    padding: 0.25rem 1rem;
    border-left: 2px solid rgba(0, 0, 0, 0.1);
    text-decoration: none;
    display: block;
    background: linear-gradient(to right, #333 50%, #333 75%);
    background-repeat: no-repeat;
    background-position: 0 100%;
    background-size: 0% 100%;
    transition: background-size 0.5s ease-out;
}

.nav-link:hover {
    background-size: 100% 100%;
    color: #fff;
}

.nav-link.active {
    border-left: 2px solid #333;
    transition: border-left-color 0.5s;
}

.submenu .nav-link {
    padding-left: 2rem;
    font-size: 0.9rem;
}

section {
    padding: 50px 0;
    border-bottom: 1px solid #e9ecef;
    scroll-margin-top: 100px; /* Smooth scroll offset */
}

h2 {
    font-size: 38px;
    font-weight: 600;
    padding-bottom: 25px;
    color: #333;
}
h3 {
    font-size: 21px;
    font-weight: 400;
    color: #333;
}
h3 span {
    font-size: 14px;
}
.team-title strong {
    font-size: 14px;
    line-height: 1.3;
    text-align: justify;
    font-weight: normal;
    margin-bottom: 1rem;
    color: #333;
    margin-top: 42px;
    display: block;
}
/* .single-news {
        margin-bottom: 20px;
      } */
.single-news:last-child {
    margin-bottom: 0px;
}

/* Accordion functionality - Simple approach */
.child-menucontent {
    display: none;
}

.child-menucontent.active {
    display: block;
}

.menu-btn,
.smenu-btn {
    cursor: pointer;
    padding: 10px;
    border-radius: 4px;
    transition: all 0.3s ease;
    position: relative;
    z-index: 1;
}
.menu-btn h3,
.smenu-btn h3 {
    margin: 0;
}
.menu-btn:after,
.smenu-btn:after {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(
        90deg,
        rgba(227, 250, 227, 1) 0%,
        rgba(230, 230, 250, 1) 50%
    );
    z-index: -1;
    opacity: 0;
}

.menu-btn:hover::after,
.smenu-btn:hover::after {
    opacity: 1;
}

p {
    color: #555;
    font-size: 14px;
    line-height: 1.3;
    text-align: justify;
}
.single-menu-btn2 span {
    color: #555;
    font-size: 14px;
    line-height: 1.3;
    text-align: justify;
}
.inv-sub-title {
    color: #333;
}
a {
    color: #007bff;
    text-decoration: underline;
}
a:hover {
    color: #0056b3;
}
.news-list {
    margin: 0;
    list-style: none;
    padding: 0;
}
.team .news-list li {
    margin-bottom: 20px;
}

/* Background */
body {
    background-color: #ffffff;
    color: #333333;
}
.single-news {
    position: relative;
}
.single-news::after {
    content: "";
    height: 5px;
    width: 5px;
    background-color: #333333;
    position: absolute;
    left: -10px;
    top: 18px;
}
.team .single-news::after {
    left: -10px;
    top: 8px;
}
/* #section5 .news-list li {
        margin-bottom: 20px;
      } */
header {
    background: linear-gradient(
        90deg,
        rgba(227, 250, 227, 1) 0%,
        rgba(230, 230, 250, 1) 50%
    ) !important;
    padding: 20px 0;
    position: sticky;
    top: 0;
    z-index: 100000;
    border-bottom: 1px solid #e9ecef;
}
.sidebar-btn svg {
    fill: #333;
}

.fade-in-char {
    opacity: 0;
    display: inline-block;
    animation: fadeInUp 0.5s forwards;
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(8px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}
/* Responsive Design */
@media (max-width: 768px) {
    .sidebar {
        position: relative;
        width: 100%;
        height: auto;
        max-width: none;
    }

    .content-area {
        margin-left: 0;
        max-width: 100%;
    }
}
.trriger-menu {
    z-index: 999999;
}
