<?xml version="1.0" standalone="no"?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN" "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd" >
<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" version="1.1">
<metadata></metadata>
<defs>
<font id="neue_montreallight" horiz-adv-x="1069" >
<font-face units-per-em="2048" ascent="1581" descent="-467" />
<missing-glyph horiz-adv-x="471" />
<glyph horiz-adv-x="0" />
<glyph horiz-adv-x="682" />
<glyph unicode=" "  horiz-adv-x="471" />
<glyph unicode="&#x09;" horiz-adv-x="471" />
<glyph unicode="&#xa0;" horiz-adv-x="471" />
<glyph unicode="!" horiz-adv-x="411" d="M141 969v495h82v-495l-20 -592h-41zM147 53q0 29 19 45.5t43 16.5t42.5 -16.5t18.5 -45.5t-18.5 -45t-42.5 -16t-43 16t-19 45z" />
<glyph unicode="&#x22;" horiz-adv-x="481" d="M88 1053v411h66v-411h-66zM328 1053v411h65v-411h-65z" />
<glyph unicode="#" horiz-adv-x="1095" d="M66 467l10 57h190l68 379h-203l10 58h203l80 440h65l-79 -440h321l80 440h66l-80 -440h233l-10 -58h-234l-67 -379h242l-11 -57h-241l-84 -467h-66l84 467h-321l-84 -467h-66l84 467h-190zM332 524h321l68 379h-322z" />
<glyph unicode="$" horiz-adv-x="1118" d="M41 451h65q14 -182 121.5 -293t307.5 -121v655q-198 25 -317.5 120.5t-119.5 238.5q0 159 123 257t314 103v139h65v-141q192 -9 298 -100t118 -252h-66q-15 133 -96.5 209.5t-253.5 85.5v-609q171 -24 272 -75t142 -125.5t41 -184.5q0 -159 -118 -267t-337 -114v-151h-65 v154q-222 10 -351 134.5t-143 336.5zM164 1051q0 -231 371 -297v600q-168 -5 -269.5 -87.5t-101.5 -215.5zM600 35q193 6 290 96.5t99 226.5q2 148 -85.5 221.5t-303.5 104.5v-649z" />
<glyph unicode="%" horiz-adv-x="1460" d="M57 1034q0 165 74 271t215 106t214 -106t73 -271t-73 -271t-214 -106t-215 106t-74 271zM123 1034q0 -63 11 -116.5t35.5 -101t69.5 -74.5t107 -27q49 0 88 18.5t63.5 48.5t40.5 72.5t22.5 86.5t6.5 93q0 63 -11 116.5t-35 101t-69 75t-106 27.5q-62 0 -107 -27.5 t-69.5 -75t-35.5 -101t-11 -116.5zM274 0l838 1403h72l-838 -1403h-72zM825 354q0 165 74 271t215 106t214 -106t73 -271t-73 -271t-214 -106t-215 106t-74 271zM891 354q0 -63 11 -116.5t35.5 -101t69.5 -74.5t107 -27q49 0 88 18.5t63.5 48.5t40.5 72.5t22.5 86.5t6.5 93 q0 63 -11 116.5t-35 101t-69 75t-106 27.5q-62 0 -107 -27.5t-69.5 -75t-35.5 -101t-11 -116.5z" />
<glyph unicode="&#x26;" horiz-adv-x="1146" d="M72 381q0 69 22 131.5t54.5 109t81.5 91t92 73.5t98 60q-66 82 -98 126t-60.5 110t-28.5 128q0 51 17.5 99t51.5 88.5t92.5 65t133.5 24.5q134 0 214.5 -83.5t80.5 -193.5q0 -119 -75.5 -208t-233.5 -179l389 -504q77 150 86 312h66q-14 -200 -111 -365l205 -266h-82 l-160 209q-164 -232 -430 -232q-183 0 -294 110.5t-111 293.5zM137 381q0 -162 93.5 -254t246.5 -92q123 0 222 60.5t169 164.5l-393 514q-1 2 -4 6t-5.5 7.5t-4.5 7.5q-55 -30 -93.5 -54.5t-85.5 -64t-76 -79.5t-49 -96.5t-20 -119.5zM299 1210q0 -53 26.5 -113t50.5 -94.5 t81 -107.5q5 -7 8.5 -11.5t7.5 -9.5l2 3q34 18 50.5 28t48 29t49 33.5t42.5 36t38.5 42.5t28 46.5t20.5 55t6 62.5q0 91 -63 155.5t-167 64.5q-115 0 -172 -64t-57 -156z" />
<glyph unicode="'" horiz-adv-x="241" d="M88 1053v411h66v-411h-66z" />
<glyph unicode="(" horiz-adv-x="512" d="M147 649q0 486 256 897h66q-123 -208 -189.5 -420t-66.5 -477q0 -259 66.5 -470t189.5 -419h-66q-111 178 -183.5 405t-72.5 484z" />
<glyph unicode=")" horiz-adv-x="512" d="M43 -240q123 208 189.5 419t66.5 470q0 265 -66.5 477t-189.5 420h66q256 -411 256 -897q0 -257 -72.5 -484t-183.5 -405h-66z" />
<glyph unicode="*" horiz-adv-x="704" d="M86 1245l16 45l222 -80v254h55v-254l223 80l16 -45l-221 -82l144 -196l-41 -29l-148 203l-145 -203l-41 29l141 196z" />
<glyph unicode="+" horiz-adv-x="1073" d="M111 639v57l395 -2l-2 416h65l-2 -416l396 2v-57h-396l2 -416h-65l2 416h-395z" />
<glyph unicode="," horiz-adv-x="260" d="M49 51q0 30 17 47t43 17q63 0 63 -111q0 -175 -113 -209v58q58 27 58 139q-28 -9 -48 6.5t-20 52.5z" />
<glyph unicode="-" horiz-adv-x="696" d="M74 494v57h549v-57h-549z" />
<glyph unicode="." horiz-adv-x="299" d="M88 53q0 29 19 45.5t43 16.5t42.5 -16.5t18.5 -45.5t-18.5 -45t-42.5 -16t-43 16t-19 45z" />
<glyph unicode="/" horiz-adv-x="757" d="M61 0l598 1464h66l-596 -1464h-68z" />
<glyph unicode="0" horiz-adv-x="1177" d="M82 707q0 153 31.5 284t92.5 231.5t159.5 158t224.5 57.5t224.5 -57.5t159 -158.5t91.5 -231.5t31 -283.5q0 -122 -19 -230t-59.5 -200t-99.5 -158.5t-143 -104t-185 -37.5q-126 0 -224.5 57t-159.5 158t-92.5 231.5t-31.5 283.5zM147 707q0 -142 27.5 -262.5t80.5 -213 t139 -144.5t196 -52t195.5 52t138 144.5t79.5 213t27 262.5t-27 262.5t-79.5 213t-138 145t-195.5 52.5t-196 -52.5t-139 -144.5t-80.5 -213t-27.5 -263z" />
<glyph unicode="1" horiz-adv-x="616" d="M70 1051v57q152 0 230.5 79.5t78.5 227.5h65v-1415h-65v1194q-71 -143 -309 -143z" />
<glyph unicode="2" horiz-adv-x="1042" d="M63 0q0 198 105 339.5t301 244.5q185 97 249 141q138 97 172 216q13 46 13 99q0 142 -93.5 241t-250.5 99q-175 0 -285 -116.5t-110 -282.5h-66q0 88 31 169t87.5 146t145.5 103.5t197 38.5q183 0 296.5 -109t113.5 -289q0 -82 -27 -150t-72.5 -117t-105 -91t-126 -78.5 t-134 -73t-130.5 -81.5t-113.5 -97.5t-84.5 -127t-43 -163.5v-4h836v-57h-906z" />
<glyph unicode="3" horiz-adv-x="1077" d="M45 459h66q0 -189 118 -306.5t324 -117.5q184 0 288.5 91.5t104.5 250.5q0 79 -32 144.5t-84 106t-113 62t-123 21.5h-154v57h146q129 0 217 85t88 220q0 126 -88.5 216.5t-249.5 90.5q-108 0 -188 -31.5t-124 -85.5t-64.5 -116.5t-20.5 -134.5h-66q0 188 121 307 t342 119q166 0 284.5 -100t118.5 -265q0 -237 -229 -332v-4q138 -41 211.5 -134.5t73.5 -225.5q0 -189 -126 -294.5t-333 -105.5q-232 0 -370 129.5t-138 352.5z" />
<glyph unicode="4" horiz-adv-x="1093" d="M49 373v57l715 985h65v-985h215v-57h-215v-373h-65v373h-715zM125 430h639v881z" />
<glyph unicode="5" horiz-adv-x="1032" d="M51 377h66q8 -157 109 -249.5t278 -92.5q82 0 152.5 25.5t125.5 76t86 132.5t31 188q0 192 -108.5 290.5t-276.5 98.5q-71 0 -134 -18.5t-107 -48.5t-73.5 -60t-47.5 -60h-72l153 756h662v-57h-610l-121 -596v-4q66 73 161.5 109t202.5 36q202 0 319.5 -126t117.5 -320 q0 -217 -124 -348.5t-337 -131.5q-113 0 -202 34.5t-141.5 92.5t-80 127t-29.5 146z" />
<glyph unicode="6" horiz-adv-x="1101" d="M82 647q0 394 131 592.5t379 198.5q162 0 272.5 -97t122.5 -264h-65q-10 139 -101.5 221t-228.5 82q-102 0 -181.5 -40.5t-139.5 -126t-92 -228.5t-32 -338v-119v-4q11 78 42.5 141.5t75 104t98 68.5t109 39.5t110.5 11.5q203 0 325.5 -126.5t122.5 -324.5 q0 -201 -124.5 -331t-332.5 -130q-237 0 -364 173.5t-127 496.5zM182 416q0 -70 25.5 -136.5t73 -122t124 -89t168.5 -33.5q181 0 286.5 113t105.5 290q0 174 -103 283.5t-280 109.5q-56 0 -110.5 -13.5t-107.5 -45t-93 -78t-64.5 -118.5t-24.5 -160z" />
<glyph unicode="7" horiz-adv-x="1001" d="M47 1360v55h922v-49q-105 -80 -191.5 -177t-146.5 -195t-105.5 -208.5t-72.5 -210.5t-43.5 -207.5t-22.5 -193.5t-6 -174h-66q0 129 13 255.5t51 281.5t98.5 292.5t163.5 278.5t238 250v2h-832z" />
<glyph unicode="8" horiz-adv-x="1114" d="M68 393q0 139 80.5 234.5t216.5 142.5v4q-106 43 -170 121.5t-64 191.5q0 163 121 257t305 94t305 -94t121 -257q0 -113 -64 -191.5t-169 -121.5v-4q135 -47 216 -143t81 -234q0 -129 -71.5 -225.5t-181.5 -143.5t-237 -47t-236.5 47t-181 143.5t-71.5 225.5zM133 393 q0 -160 124.5 -259t299.5 -99t299.5 99t124.5 259q0 156 -124.5 253t-299.5 97t-299.5 -97t-124.5 -253zM197 1087q0 -131 102 -208.5t258 -77.5t258.5 77.5t102.5 208.5q0 137 -101.5 215t-259.5 78q-157 0 -258.5 -78t-101.5 -215z" />
<glyph unicode="9" horiz-adv-x="1097" d="M74 977q0 201 124 331t332 130q238 0 365 -173.5t127 -496.5q0 -394 -131 -592.5t-379 -198.5q-162 0 -272.5 97t-122.5 264h65q10 -139 101.5 -221t228.5 -82q102 0 181.5 40.5t139 126t91.5 228.5t32 338v119v4q-11 -78 -42.5 -141.5t-75 -104t-98 -68.5t-108.5 -39.5 t-110 -11.5q-203 0 -325.5 126.5t-122.5 324.5zM139 977q0 -174 103 -283.5t280 -109.5q56 0 110.5 13.5t107.5 45t93 78t64.5 118.5t24.5 160q0 70 -26 136.5t-73.5 122t-124 89t-168.5 33.5q-181 0 -286 -113t-105 -290z" />
<glyph unicode=":" horiz-adv-x="315" d="M96 53q0 29 19 45.5t43 16.5t42.5 -16.5t18.5 -45.5t-18.5 -45t-42.5 -16t-43 16t-19 45zM96 676q0 29 19 45t43 16t42.5 -16t18.5 -45t-18.5 -45.5t-42.5 -16.5t-43 16.5t-19 45.5z" />
<glyph unicode=";" horiz-adv-x="313" d="M57 51q0 30 17 47t43 17q63 0 63 -111q0 -175 -112 -209v58q57 27 57 139q-28 -9 -48 6.5t-20 52.5zM96 678q0 29 19 45t43 16t42.5 -16t18.5 -45t-18.5 -45.5t-42.5 -16.5t-43 16.5t-19 45.5z" />
<glyph unicode="&#x3c;" horiz-adv-x="1013" d="M102 596v82l801 342v-66l-733 -315v-4l733 -311v-66z" />
<glyph unicode="=" d="M111 463v57h847v-57h-847zM111 752v57h847v-57h-847z" />
<glyph unicode="&#x3e;" horiz-adv-x="1013" d="M111 258v66l733 311v4l-733 315v66l800 -342v-82z" />
<glyph unicode="?" horiz-adv-x="980" d="M51 1051q0 86 30.5 164.5t86.5 139t143 96.5t191 36q175 0 288 -94t113 -254q0 -38 -7.5 -73t-26 -68.5t-33 -57t-44.5 -54.5t-43 -43.5t-46.5 -42.5t-38.5 -34q-48 -42 -68.5 -62.5t-49 -57.5t-39.5 -72.5t-19.5 -90.5t-8.5 -129h-65q0 96 12 162t45 121.5t69 93.5 t105 98q37 32 54 48t49 51t47.5 62.5t29 67.5t13.5 81q0 127 -87.5 209t-248.5 82q-169 0 -277 -106t-108 -273h-66zM410 53q0 29 18.5 45.5t42.5 16.5t42.5 -16.5t18.5 -45.5t-18.5 -45t-42.5 -16t-42.5 16t-18.5 45z" />
<glyph unicode="@" horiz-adv-x="1765" d="M76 592q0 133 33 252.5t91.5 212t138.5 167.5t174.5 123.5t198 74.5t210.5 26q119 0 231 -29.5t210 -90.5t171 -147.5t115.5 -207.5t42.5 -262q0 -230 -96.5 -378.5t-254.5 -148.5q-103 0 -156 60t-50 153q-45 -103 -121 -164t-178 -61q-159 0 -253 114.5t-94 303.5 q0 124 45.5 229.5t138.5 173.5t216 68q108 0 184 -65t105 -187h2l24 233h58l-74 -579q-14 -104 24.5 -162.5t128.5 -58.5q131 0 208 133.5t77 335.5q0 161 -57.5 292t-155.5 214.5t-224 128.5t-267 45q-122 0 -237.5 -35.5t-214 -104.5t-172 -164.5t-115.5 -223t-42 -271.5 q0 -218 95 -385.5t264 -258.5t385 -91q193 0 393 92l25 -58q-192 -92 -420 -92q-232 0 -415.5 98t-287.5 279t-104 416zM555 590q0 -162 74 -261.5t205 -99.5q148 0 233.5 130t85.5 321q0 151 -75 237.5t-189 86.5q-81 0 -146.5 -36.5t-105.5 -96.5t-61 -132t-21 -149z" />
<glyph unicode="A" horiz-adv-x="1236" d="M16 0l562 1464h77l566 -1464h-66l-192 498h-689l-192 -498h-66zM295 555h645l-322 834h-4z" />
<glyph unicode="B" horiz-adv-x="1245" d="M133 0v1464h569q199 0 311.5 -96t112.5 -270q0 -120 -62 -203t-179 -121v-4q141 -31 223 -126.5t82 -237.5q0 -106 -34 -185t-96 -127t-144 -71t-183 -23h-600zM199 57h528q118 0 204.5 34t139.5 114t53 201q0 152 -101 242.5t-267 90.5h-557v-682zM199 797h549 q139 0 226 84t87 217q0 142 -93.5 225.5t-265.5 83.5h-503v-610z" />
<glyph unicode="C" horiz-adv-x="1382" d="M76 731q0 220 81 391.5t233.5 268t354.5 96.5q218 0 367.5 -126t183.5 -329h-65q-27 172 -160 285t-326 113q-188 0 -326.5 -92.5t-208 -249.5t-69.5 -357q0 -203 70.5 -360t207 -246.5t320.5 -89.5q107 0 199.5 38.5t157 104t105 149.5t52.5 177h66 q-29 -233 -189.5 -380t-390.5 -147q-300 0 -481.5 207t-181.5 547z" />
<glyph unicode="D" horiz-adv-x="1337" d="M133 0v1464h524q124 0 225.5 -38t171 -104.5t117 -160t69.5 -202.5t22 -234t-23.5 -233t-72.5 -199t-121.5 -156t-174.5 -101t-227 -36h-510zM195 57h446q264 0 409.5 177.5t145.5 490.5q0 316 -140 499t-401 183h-460v-1350z" />
<glyph unicode="E" horiz-adv-x="1191" d="M133 0v1464h963v-57h-897v-600h825v-57h-825v-693h917v-57h-983z" />
<glyph unicode="F" horiz-adv-x="1128" d="M133 0v1464h942v-57h-876v-614h753v-58h-753v-735h-66z" />
<glyph unicode="G" horiz-adv-x="1464" d="M76 729q0 158 45 295t129.5 240.5t215 163t292.5 59.5q226 0 378.5 -118t190.5 -322h-65q-34 170 -171 276.5t-333 106.5q-150 0 -269.5 -56.5t-194.5 -154t-114 -222.5t-39 -268q0 -146 37.5 -271t110.5 -220t189 -149t263 -54q252 0 397.5 172t145.5 461h-508v57h574 v-725h-54l-8 387h-4q-19 -85 -63 -158t-110.5 -130t-162 -89.5t-207.5 -32.5q-160 0 -287.5 57.5t-209.5 159t-125 238t-43 297.5z" />
<glyph unicode="H" horiz-adv-x="1333" d="M133 0v1464h66v-657h936v657h65v-1464h-65v750h-936v-750h-66z" />
<glyph unicode="I" horiz-adv-x="344" d="M139 0v1464h66v-1464h-66z" />
<glyph unicode="J" horiz-adv-x="929" d="M45 373v59h66v-59q0 -159 85.5 -248.5t229.5 -89.5q145 0 231 89.5t86 248.5v1091h66v-1091q0 -176 -99.5 -286t-283.5 -110t-282.5 110t-98.5 286z" />
<glyph unicode="K" horiz-adv-x="1243" d="M133 0v1464h66v-948l915 948h86l-604 -626l643 -838h-80l-608 791l-352 -365v-426h-66z" />
<glyph unicode="L" horiz-adv-x="1085" d="M133 0v1464h66v-1407h839v-57h-905z" />
<glyph unicode="M" horiz-adv-x="1624" d="M133 0v1464h96l580 -1376h4l582 1376h96v-1464h-66v1368h-4l-575 -1368h-70l-573 1368h-4v-1368h-66z" />
<glyph unicode="N" horiz-adv-x="1351" d="M133 0v1464h82l934 -1372h4v1372h66v-1464h-82l-934 1368h-4v-1368h-66z" />
<glyph unicode="O" horiz-adv-x="1499" d="M76 733q0 218 84 389.5t237.5 268t352.5 96.5t352.5 -96.5t237 -268t83.5 -389.5t-83.5 -390t-237.5 -269t-352 -97t-352 97t-238 269t-84 390zM141 733q0 -205 80 -365t218.5 -246.5t310.5 -86.5q129 0 241 51t193 141t127.5 221t46.5 285t-46.5 285t-127.5 221 t-193 140.5t-241 50.5t-241 -50.5t-193.5 -140.5t-128 -221t-46.5 -285z" />
<glyph unicode="P" horiz-adv-x="1163" d="M133 0v1464h520q463 0 463 -415q0 -416 -463 -416h-454v-633h-66zM199 690h473q180 0 279.5 88.5t99.5 270.5t-99.5 270t-279.5 88h-473v-717z" />
<glyph unicode="Q" horiz-adv-x="1499" d="M76 733q0 218 84 389.5t237.5 268t352.5 96.5t352.5 -96.5t237 -268t83.5 -389.5q0 -186 -61 -339t-174 -255l180 -180l-45 -45l-184 184q-164 -121 -389 -121q-198 0 -352 97t-238 269t-84 390zM141 733q0 -205 80 -365t218.5 -246.5t310.5 -86.5q195 0 342 110 l-170 170l45 45l176 -176q102 94 158.5 235.5t56.5 313.5q0 154 -46.5 285t-127.5 221t-193 140.5t-241 50.5t-241 -50.5t-193.5 -140.5t-128 -221t-46.5 -285z" />
<glyph unicode="R" horiz-adv-x="1239" d="M133 0v1464h574q206 0 324 -102.5t118 -306.5q0 -155 -87 -253t-239 -126v-4q67 -8 118.5 -40t81.5 -78t52.5 -103t32 -115.5t17.5 -115t12.5 -101t14 -74t24.5 -33.5v-12h-72q-13 11 -21.5 45.5t-12.5 81t-12 104t-19 115t-36 111.5t-59.5 95.5t-92.5 67t-134 25.5h-518 v-645h-66zM199 702h469q186 0 300.5 91.5t114.5 261.5q0 182 -104 267t-311 85h-469v-705z" />
<glyph unicode="S" horiz-adv-x="1198" d="M45 471h66q13 -210 152.5 -323t371.5 -113q104 0 186 28t133.5 76.5t78.5 110.5t28 133v6q0 69 -21 123q-22 56 -60 94t-105.5 66.5t-141.5 46.5t-184 37q-111 19 -189.5 46t-138.5 70.5t-89.5 109t-29.5 153.5q0 168 130 260t337 92q219 0 354 -109.5t148 -306.5h-65 q-15 168 -125.5 263.5t-311.5 95.5q-181 0 -291 -79.5t-110 -215.5q0 -68 21.5 -118.5t56 -83.5t97 -58.5t123 -40t155.5 -31.5q132 -23 221.5 -54t156 -79.5t97 -119t30.5 -167.5q0 -174 -132 -290t-359 -116q-122 0 -225.5 30.5t-183 90.5t-127 155.5t-54.5 217.5z" />
<glyph unicode="T" horiz-adv-x="1191" d="M41 1407v57h1110v-57h-522v-1407h-66v1407h-522z" />
<glyph unicode="U" horiz-adv-x="1277" d="M113 508v956h65v-956q0 -239 114.5 -356t346.5 -117t346.5 117t114.5 356v956h65v-956q0 -531 -526 -531t-526 531z" />
<glyph unicode="V" horiz-adv-x="1210" d="M18 1464h68l518 -1347h2l518 1347h68l-561 -1464h-51z" />
<glyph unicode="W" horiz-adv-x="1777" d="M27 1464h67l361 -1358h2l391 1358h82l391 -1358h2l360 1358h68l-389 -1464h-76l-395 1358h-4l-395 -1358h-76z" />
<glyph unicode="X" horiz-adv-x="1179" d="M27 0l528 748l-496 716h78l451 -655h2l452 655h78l-495 -716l528 -748h-78l-485 686h-2l-484 -686h-77z" />
<glyph unicode="Y" horiz-adv-x="1179" d="M16 1464h76l494 -792h4l497 792h76l-542 -864v-600h-66v600z" />
<glyph unicode="Z" horiz-adv-x="1204" d="M55 0v57l979 1346v4h-940v57h1024v-57l-981 -1346v-4h1000v-57h-1082z" />
<glyph unicode="[" horiz-adv-x="518" d="M162 -240v1786h291v-57h-226v-1671h226v-58h-291z" />
<glyph unicode="\" horiz-adv-x="729" d="M33 1464h65l598 -1464h-67z" />
<glyph unicode="]" horiz-adv-x="518" d="M66 -182h225v1671h-225v57h290v-1786h-290v58z" />
<glyph unicode="^" horiz-adv-x="778" d="M23 901l329 563h72l330 -563h-70l-295 502h-2l-293 -502h-71z" />
<glyph unicode="_" horiz-adv-x="768" d="M-4 -283h776v-57h-776v57z" />
<glyph unicode="`" horiz-adv-x="954" d="M301 1477h84l141 -254h-59z" />
<glyph unicode="a" horiz-adv-x="1003" d="M49 256q0 145 93 219t300 105q21 3 59 8t60.5 8.5t55 10.5t52 15t42.5 22t35.5 32t21 44.5t8.5 59.5q0 116 -77.5 175t-204.5 59q-155 0 -240.5 -68.5t-93.5 -189.5h-62q4 59 25.5 110.5t65 98.5t121.5 74.5t184 27.5q75 0 134.5 -17t108 -53t75 -100.5t26.5 -153.5v-532 v-14v-31.5t1.5 -29.5t6 -29.5t11 -24.5t18.5 -20.5t27.5 -12.5t38.5 -5t52 7v-53q-36 -8 -67 -8q-80 0 -115 41.5t-35 140.5h-4q-118 -195 -385 -195q-152 0 -245 76t-93 203zM111 274q0 -120 75 -181.5t217 -61.5q91 0 165.5 28.5t118 73t66.5 93.5t23 96v311 q-21 -46 -97.5 -68.5t-215.5 -38.5q-168 -21 -260 -78t-92 -174z" />
<glyph unicode="b" horiz-adv-x="1073" d="M109 0v1464h61v-688h4q49 131 149 211t246 80q94 0 176 -37.5t143 -105.5t96.5 -172t35.5 -230t-35.5 -230t-96.5 -172t-143 -105.5t-176 -37.5q-145 0 -246 80.5t-149 204.5h-4v-262h-61zM170 522q0 -222 113 -356.5t272 -134.5q180 0 291.5 132t111.5 359t-111.5 359.5 t-291.5 132.5q-159 0 -272 -135t-113 -357z" />
<glyph unicode="c" horiz-adv-x="1005" d="M53 522q0 245 135 395t353 150q177 0 292.5 -103.5t133.5 -254.5h-62q-3 48 -27.5 99t-67.5 98.5t-113.5 77.5t-155.5 30q-100 0 -182.5 -40.5t-134.5 -109t-80.5 -156.5t-28.5 -186t28.5 -186t80.5 -156t134.5 -108.5t182.5 -40.5q69 0 129 20t101 52.5t71 74t45.5 83.5 t17.5 83h62q-11 -153 -129.5 -260t-296.5 -107q-218 0 -353 150t-135 395z" />
<glyph unicode="d" horiz-adv-x="1073" d="M53 522q0 126 35.5 230t96.5 172t143 105.5t176 37.5q146 0 246 -80t149 -211h4v688h62v-1464h-62v262h-4q-48 -124 -149 -204.5t-246 -80.5q-94 0 -176 37.5t-143 105.5t-96.5 172t-35.5 230zM115 522q0 -227 111.5 -359t291.5 -132q159 0 272 134.5t113 356.5t-113 357 t-272 135q-180 0 -291.5 -132.5t-111.5 -359.5z" />
<glyph unicode="e" horiz-adv-x="1021" d="M53 522q0 253 130 399t345 146q112 0 198 -40t139.5 -112.5t80.5 -169t27 -213.5h-858v-12q0 -102 26.5 -190t77 -155t130.5 -105.5t181 -38.5q70 0 129 19t98.5 48t69.5 68.5t46 75.5t24 73h61q-36 -153 -144 -245.5t-284 -92.5q-219 0 -348 149t-129 396zM115 586h796 q0 82 -23.5 156.5t-68.5 136t-120.5 98.5t-170.5 37q-94 0 -172.5 -35.5t-129.5 -95t-80 -136t-31 -161.5z" />
<glyph unicode="f" horiz-adv-x="520" d="M37 999v54h149v155q0 147 53 208t156 61q35 0 80 -11v-53q-41 7 -75 7q-11 0 -22 -1q-42 -3 -71 -23t-44 -70t-15 -134v-139h227v-54h-227v-999h-62v999h-149z" />
<glyph unicode="g" horiz-adv-x="1067" d="M53 563q0 125 37 223.5t100 158.5t141.5 91t166.5 31q138 0 243.5 -70t151.5 -192h4v248h61v-996q0 -204 -116.5 -305.5t-325.5 -101.5q-191 0 -306 85.5t-128 211.5h61q19 -104 112.5 -174t260.5 -70q178 0 279.5 89t101.5 267v256h-4q-45 -115 -152 -185.5t-243 -70.5 q-88 0 -166.5 31t-141.5 91t-100 158.5t-37 223.5zM115 563q0 -112 31.5 -199.5t86.5 -141.5t126 -81.5t153 -27.5q56 0 110 17t104 53.5t88 88t60.5 127t22.5 164.5q0 110 -34 199t-90.5 142.5t-123.5 81.5t-137 28q-82 0 -153 -28t-126 -82t-86.5 -141.5t-31.5 -199.5z " />
<glyph unicode="h" horiz-adv-x="989" d="M113 0v1464h61v-602h4q28 42 59.5 75t78.5 64.5t112.5 48.5t144.5 17q131 0 223.5 -82.5t92.5 -230.5v-754h-62v713q0 50 -7 91.5t-26 81.5t-49.5 67.5t-81.5 44t-118 16.5q-83 0 -155.5 -29t-118 -73.5t-71.5 -93.5t-26 -93v-725h-61z" />
<glyph unicode="i" horiz-adv-x="323" d="M111 1354q0 23 16 36t35 13q21 0 36 -13t15 -36q0 -22 -15.5 -35.5t-35.5 -13.5q-19 0 -35 13.5t-16 35.5zM131 0v1053h62v-1053h-62z" />
<glyph unicode="j" horiz-adv-x="323" d="M-45 -274q61 -16 99.5 -16t56.5 32.5t18 107.5v1203h61v-1186q0 -37 -2.5 -64.5t-10.5 -57t-22 -48.5t-39 -31t-59 -12q-61 0 -102 18v54zM109 1354q0 23 16 36t35 13q21 0 36 -13t15 -36q0 -22 -15.5 -35.5t-35.5 -13.5q-19 0 -35 13.5t-16 35.5z" />
<glyph unicode="k" horiz-adv-x="921" d="M109 0v1464h61v-989l600 578h86l-422 -404l486 -649h-76l-453 608l-221 -215v-393h-61z" />
<glyph unicode="l" horiz-adv-x="290" d="M115 0v1464h61v-1464h-61z" />
<glyph unicode="m" horiz-adv-x="1576" d="M109 0v1053h61v-195h4q118 209 344 209q118 0 189.5 -58.5t93.5 -168.5h4q59 109 145.5 168t223.5 59q150 0 225.5 -88.5t75.5 -249.5v-729h-62v688q0 164 -56 245t-188 81q-79 0 -146.5 -27t-110.5 -70.5t-67 -96.5t-24 -107v-713h-61v688q0 170 -64.5 248t-177.5 78 q-77 0 -145 -30t-111 -75.5t-67.5 -97.5t-24.5 -98v-713h-61z" />
<glyph unicode="n" horiz-adv-x="987" d="M109 0v1053h61v-191h4q28 42 59.5 75t78.5 64.5t112.5 48.5t144.5 17q131 0 223.5 -82.5t92.5 -230.5v-754h-62v713q0 50 -7 91.5t-26 81.5t-49.5 67.5t-81.5 44t-118 16.5q-83 0 -155.5 -29t-118 -73.5t-71.5 -93.5t-26 -93v-725h-61z" />
<glyph unicode="o" d="M53 522q0 247 130 396t352 149t351.5 -149t129.5 -396t-129.5 -396t-351.5 -149t-352 149t-130 396zM115 522q0 -219 111.5 -355t308.5 -136t308 136t111 355t-111 355.5t-308 136.5t-308.5 -136.5t-111.5 -355.5z" />
<glyph unicode="p" horiz-adv-x="1075" d="M109 -340v1393h61v-277h4q49 131 149 211t246 80q94 0 176 -37.5t143 -105.5t96.5 -172t35.5 -230t-35.5 -230t-96.5 -172t-143 -105.5t-176 -37.5q-145 0 -246 80.5t-149 204.5h-4v-602h-61zM170 522q0 -222 113 -356.5t272 -134.5q180 0 291.5 132t111.5 359 t-111.5 359.5t-291.5 132.5q-159 0 -272 -135t-113 -357z" />
<glyph unicode="q" horiz-adv-x="1075" d="M55 522q0 126 35.5 230t96.5 172t143 105.5t176 37.5q146 0 246 -80t149 -211h4v277h62v-1393h-62v602h-4q-48 -124 -149 -204.5t-246 -80.5q-94 0 -176 37.5t-143 105.5t-96.5 172t-35.5 230zM117 522q0 -227 111.5 -359t291.5 -132q159 0 272 134.5t113 356.5t-113 357 t-272 135q-180 0 -291.5 -132.5t-111.5 -359.5z" />
<glyph unicode="r" horiz-adv-x="602" d="M109 0v1053h61v-226h4q34 104 122.5 172t195.5 68q45 0 75 -6v-53h-4q-27 4 -53 4q-72 0 -138 -30q-90 -41 -146 -129t-56 -200v-653h-61z" />
<glyph unicode="s" horiz-adv-x="909" d="M41 328h61q8 -134 108 -215.5t279 -81.5q129 0 220.5 67t91.5 166q0 52 -18 92t-38.5 63t-71 45t-78.5 30t-98 26q-12 3 -19 5t-18 5t-20 5q-174 47 -233 75q-106 52 -124 145q-5 23 -5 50q0 119 97 190.5t265 71.5q93 0 165 -25t116.5 -68.5t69.5 -93.5t34 -108h-61 q-9 47 -31.5 88.5t-60 77t-97.5 56t-135 20.5q-78 0 -144 -20.5t-111.5 -69.5t-45.5 -119q0 -28 9 -52t23 -41.5t39.5 -33.5t49 -27t61.5 -23t67 -19.5t76 -19.5q18 -5 28 -7q79 -20 126.5 -35t99.5 -40t80 -54.5t46 -74.5t18 -104q0 -133 -102.5 -215t-270.5 -82 q-215 0 -327.5 96.5t-120.5 254.5z" />
<glyph unicode="t" horiz-adv-x="542" d="M37 999v54h153v340h62v-340h231v-54h-231v-727q0 -30 0.5 -46t2.5 -42t6 -40t12 -33.5t19.5 -29t29 -19.5t40 -12.5t53.5 -0.5t68 10v-53q-55 -12 -110 -12q-40 0 -70.5 11.5t-57 39t-41 81t-14.5 130.5v743h-153z" />
<glyph unicode="u" horiz-adv-x="987" d="M102 299v754h62v-713q0 -50 7 -91.5t26 -81.5t49.5 -67.5t81.5 -44t118 -16.5q83 0 155.5 29t118 73.5t71.5 93.5t26 93v725h62v-1053h-62v190h-4q-28 -42 -59.5 -75t-78.5 -64t-112.5 -48t-144.5 -17q-131 0 -223.5 82.5t-92.5 230.5z" />
<glyph unicode="v" horiz-adv-x="888" d="M10 1053h64l368 -971h2l371 971h64l-402 -1053h-67z" />
<glyph unicode="w" horiz-adv-x="1421" d="M16 1053h64l307 -977h2l279 977h84l280 -977h2l307 977h64l-332 -1053h-82l-280 973h-4l-277 -973h-82z" />
<glyph unicode="x" horiz-adv-x="952" d="M14 0l420 539l-387 514h76l350 -471h4l350 471h76l-387 -514l422 -539h-78l-383 500h-4l-381 -500h-78z" />
<glyph unicode="y" horiz-adv-x="901" d="M10 1053h66l381 -965h2l366 965h66l-414 -1088q-23 -60 -36.5 -93t-34 -75t-37.5 -63.5t-40.5 -42.5t-51.5 -29t-62 -8q-61 0 -123 20v54h4q39 -8 57 -10.5t51 -4.5t56.5 6.5t40.5 26.5q32 32 67.5 106t57.5 152z" />
<glyph unicode="z" horiz-adv-x="903" d="M61 0v53l693 942v4h-660v54h737v-54l-692 -942v-4h713v-53h-791z" />
<glyph unicode="{" horiz-adv-x="538" d="M49 625v57q68 -2 115 50t47 155v323q0 68 5 116.5t19.5 92.5t39 70.5t65 41.5t96.5 15h23v-57h-23q-100 0 -130 -63t-30 -255v-297q0 -84 -32 -135t-94 -84v-4q62 -33 94 -84.5t32 -136.5v-287q0 -194 30 -259.5t130 -65.5h23v-58h-23q-56 0 -96.5 16t-65 43.5t-39 72.5 t-19.5 94.5t-5 117.5v314q0 102 -46.5 154.5t-115.5 52.5z" />
<glyph unicode="|" horiz-adv-x="380" d="M158 -262v1786h65v-1786h-65z" />
<glyph unicode="}" horiz-adv-x="538" d="M80 -182h22q100 0 130 65.5t30 259.5v287q0 85 32 136.5t95 84.5v4q-62 33 -94.5 84t-32.5 135v297q0 192 -30 255t-130 63h-22v57h22q70 0 115.5 -22t69 -68t32.5 -103.5t9 -142.5v-323q0 -103 46.5 -155t114.5 -50v-57q-69 0 -115 -52.5t-46 -154.5v-314 q0 -68 -5 -117.5t-19.5 -94.5t-39.5 -72.5t-65.5 -43.5t-96.5 -16h-22v58z" />
<glyph unicode="~" horiz-adv-x="782" d="M76 596q6 86 49 135t113 49q43 0 88.5 -21t77 -45.5t70.5 -45.5t71 -21q43 0 68 34t28 91h66q-6 -85 -49 -133.5t-113 -48.5q-43 0 -88 21t-76.5 45.5t-71 45.5t-71.5 21q-44 0 -68 -34.5t-29 -92.5h-65z" />
<glyph unicode="&#xa1;" horiz-adv-x="391" d="M121 229l20 592h41l21 -592v-495h-82v495zM127 1145q0 29 18.5 45t42.5 16t43 -16t19 -45t-19 -45.5t-43 -16.5t-42.5 16.5t-18.5 45.5z" />
<glyph unicode="&#xa2;" horiz-adv-x="1011" d="M53 522q0 232 122 380t321 163v145h65v-143q168 -7 278 -110t128 -248h-62q-2 36 -17 75.5t-44 79.5t-68 72t-95 53.5t-120 24.5v-983q80 4 146.5 35t107.5 78t64 98.5t26 101.5h62q-11 -147 -123.5 -253.5t-282.5 -113.5v-151h-65v154q-199 15 -321 162.5t-122 379.5z M115 522q0 -124 42.5 -229t130.5 -176.5t208 -83.5v979q-120 -12 -208 -83.5t-130.5 -177t-42.5 -229.5z" />
<glyph unicode="&#xa3;" horiz-adv-x="1073" d="M70 51q92 76 145 182t53 230q0 66 -37 168h-161v53h139q-3 7 -24 53t-27 59.5t-21 53.5t-20.5 63.5t-11 58.5t-5.5 68q0 66 27.5 131.5t80.5 123.5t144 94t205 36q112 0 200.5 -35t143.5 -95.5t83 -138t28 -165.5h-66q0 177 -106.5 277t-282.5 100q-165 0 -278 -91.5 t-113 -250.5q0 -45 12.5 -96t23.5 -79.5t39 -89t35 -77.5h367v-53h-344q35 -93 35 -168q0 -99 -38 -195t-106 -164l3 -4q100 62 206 62q53 0 108.5 -20t94 -43.5t86.5 -43.5t88 -20q66 0 113 29t106 88l37 -46q-29 -30 -48.5 -47t-52 -39t-71 -32.5t-84.5 -10.5 q-52 0 -105 20t-90 43.5t-86.5 43.5t-95.5 20q-140 0 -290 -104z" />
<glyph unicode="&#xa4;" horiz-adv-x="899" d="M35 309l123 131q-88 110 -88 265q0 157 86 264l-121 133l39 41l123 -131q111 94 252 94t249 -94l125 131l41 -41l-125 -133q86 -111 86 -264q0 -156 -86 -263l125 -131l-41 -43l-123 131q-110 -96 -251 -96q-145 0 -252 94l-123 -129zM135 705q0 -161 91 -253t223 -92 q131 0 221 92t90 253t-90 252.5t-221 91.5q-133 0 -223.5 -91.5t-90.5 -252.5z" />
<glyph unicode="&#xa5;" horiz-adv-x="1083" d="M23 1405h71l445 -758h4l444 758h74l-479 -811h409v-57h-418v-185h418v-57h-418v-295h-65v295h-418v57h418v185h-418v57h410z" />
<glyph unicode="&#xa6;" horiz-adv-x="380" d="M158 -262v764h65v-764h-65zM158 786v738h65v-738h-65z" />
<glyph unicode="&#xa7;" horiz-adv-x="1116" d="M57 727q0 123 75.5 198.5t205.5 84.5q-66 51 -99.5 105t-33.5 124q0 109 90.5 178.5t267.5 69.5q94 0 166 -26.5t114.5 -72t64.5 -100t24 -117.5h-66q-15 259 -303 259q-138 0 -215.5 -48t-77.5 -143q0 -35 10.5 -65.5t34 -58t48 -49.5t64.5 -47.5t70.5 -43t81 -45 t80.5 -45.5q180 -104 184 -107q131 -81 177 -160q39 -67 39 -155q0 -123 -75.5 -198.5t-205.5 -84.5q66 -51 99.5 -105t33.5 -124q0 -109 -90.5 -178.5t-267.5 -69.5q-94 0 -166 26.5t-114.5 71.5t-64.5 99.5t-24 117.5h66q15 -258 303 -258q138 0 215.5 48t77.5 143 q0 35 -10.5 65.5t-34 58t-48 49.5t-64.5 47.5t-70.5 43t-81 45t-80.5 45.5q-180 104 -184 107q-131 81 -177 160q-39 67 -39 155zM123 727q0 -42 9.5 -77.5t31.5 -65.5t44.5 -52.5t61 -47.5t68 -42t79.5 -44.5t81 -45.5q32 -19 105.5 -61t107.5 -64q64 6 110 18.5t87.5 37.5 t63 70.5t21.5 109.5q0 42 -9.5 77.5t-31.5 65.5t-44.5 52.5t-61 47.5t-68 42t-79.5 44.5t-81 45.5q-31 19 -104.5 61t-107.5 64q-50 -5 -89 -13t-77 -25t-62.5 -42t-39.5 -64.5t-15 -91.5z" />
<glyph unicode="&#xa8;" horiz-adv-x="1036" d="M293 1376q0 28 17 47t48 19t49.5 -19.5t18.5 -46.5t-18.5 -46t-49.5 -19t-48 18.5t-17 46.5zM610 1376q0 28 17.5 47t48.5 19t49 -19t18 -47q0 -27 -18 -46t-49 -19t-48.5 18.5t-17.5 46.5z" />
<glyph unicode="&#xa9;" horiz-adv-x="1648" d="M84 729q0 160 51 299.5t144.5 242t234 161.5t311.5 59q220 0 390 -101.5t260 -274t90 -386.5q0 -216 -90 -389t-259.5 -274t-390.5 -101q-227 0 -397 101.5t-257 273.5t-87 389zM150 729q0 -204 79 -363t233.5 -251t362.5 -92q202 0 356.5 92t236 251.5t81.5 362.5 q0 201 -83 361t-237.5 252t-353.5 92q-205 0 -360 -92.5t-235 -251.5t-80 -361zM414 729q0 209 113 343t302 134q153 0 247.5 -78.5t115.5 -203.5h-64q-18 97 -97.5 161t-201.5 64q-160 0 -255 -117t-95 -303t94.5 -301t255.5 -115q137 0 217.5 74.5t92.5 196.5h61 q-10 -151 -110.5 -239.5t-260.5 -88.5q-189 0 -302 132t-113 341z" />
<glyph unicode="&#xab;" horiz-adv-x="675" d="M61 522v82l275 246v-82l-225 -203v-4l225 -203v-82zM340 522v82l274 246v-82l-225 -203v-4l225 -203v-82z" />
<glyph unicode="&#xac;" horiz-adv-x="1075" d="M123 727v57h788v-483h-65v426h-723z" />
<glyph unicode="&#xad;" horiz-adv-x="696" d="M74 494v57h549v-57h-549z" />
<glyph unicode="&#xae;" d="M53 1008q0 209 140.5 349t341.5 140t341 -140t140 -349q0 -139 -64.5 -251.5t-174.5 -175.5t-242 -63t-242 63t-175 175.5t-65 251.5zM119 1008q0 -125 58 -225.5t152.5 -154t205.5 -53.5q172 0 293.5 122t121.5 311q0 188 -122 310t-293 122q-170 0 -293 -122.5 t-123 -309.5zM350 750v530h223q75 0 120.5 -38.5t45.5 -106.5q0 -50 -31 -85.5t-90 -48.5v-2q43 -9 66 -46t27 -77t11.5 -76.5t22.5 -43.5v-6h-61q-18 11 -26 39t-11 59t-9.5 60.5t-28.5 49t-62 19.5h-131v-227h-66zM416 1034h145q62 0 87.5 22.5t25.5 73.5q0 49 -26 71 t-85 22h-147v-189z" />
<glyph unicode="&#xaf;" horiz-adv-x="978" d="M256 1337v54h467v-54h-467z" />
<glyph unicode="&#xb0;" horiz-adv-x="768" d="M76 1186q0 53 19 106t55 98.5t96.5 74t136.5 28.5q75 0 135.5 -28.5t97 -74t55.5 -98.5t19 -106t-19 -106t-55.5 -98.5t-97 -74t-135.5 -28.5q-76 0 -136.5 28.5t-96.5 74t-55 98.5t-19 106zM137 1186q0 -107 66.5 -176.5t179.5 -69.5t179.5 69.5t66.5 176.5t-66.5 176.5 t-179.5 69.5t-179.5 -69.5t-66.5 -176.5z" />
<glyph unicode="&#xb1;" horiz-adv-x="1073" d="M111 0v57h852v-57h-852zM111 674v57h393v381h65v-381h394v-57h-394v-389h-65v389h-393z" />
<glyph unicode="&#xb4;" horiz-adv-x="960" d="M430 1223l141 254h84l-166 -254h-59z" />
<glyph unicode="&#xb6;" horiz-adv-x="929" d="M59 1126q0 176 93 257t258 81h387v-1464h-66v1407h-174v-1407h-65v788h-82q-165 0 -258 81t-93 257z" />
<glyph unicode="&#xb7;" horiz-adv-x="296" d="M90 639q0 29 19 45t43 16t42.5 -16t18.5 -45t-18.5 -45t-42.5 -16t-43 16t-19 45z" />
<glyph unicode="&#xb8;" horiz-adv-x="1011" d="M264 -317l21 34q74 -41 164 -41q65 0 103 23t38 68q0 79 -111 79q-50 0 -96 -22l-25 31l150 139h65l-118 -103v-4q89 20 142.5 -14.5t53.5 -101.5q0 -68 -53.5 -105t-140.5 -37q-106 0 -193 54z" />
<glyph unicode="&#xbb;" horiz-adv-x="675" d="M61 276v82l226 203v4l-226 203v82l275 -246v-82zM340 276v82l225 203v4l-225 203v82l274 -246v-82z" />
<glyph unicode="&#xbf;" horiz-adv-x="980" d="M78 57q0 38 7.5 73t26 68.5t33 57t44.5 54.5t43 43.5t46.5 42.5t38.5 34q48 42 68.5 62.5t49 57.5t39.5 72.5t19.5 90.5t8.5 129h65q0 -96 -12 -162t-45 -121.5t-69 -93.5t-105 -98q-37 -32 -54 -48t-49 -51t-47.5 -62.5t-29 -67.5t-13.5 -81q0 -127 87.5 -208.5 t248.5 -81.5q169 0 277 105.5t108 272.5h66q0 -86 -30.5 -164.5t-86.5 -139t-143 -96.5t-191 -36q-175 0 -288 94t-113 254zM449 1143q0 29 18.5 45t42.5 16t42.5 -16t18.5 -45t-18.5 -45.5t-42.5 -16.5t-42.5 16.5t-18.5 45.5z" />
<glyph unicode="&#xc0;" horiz-adv-x="1236" d="M16 0l562 1464h77l566 -1464h-66l-192 498h-689l-192 -498h-66zM295 555h645l-322 834h-4zM410 1888h84l141 -254h-60z" />
<glyph unicode="&#xc1;" horiz-adv-x="1236" d="M16 0l562 1464h77l566 -1464h-66l-192 498h-689l-192 -498h-66zM295 555h645l-322 834h-4zM592 1634l141 254h84l-166 -254h-59z" />
<glyph unicode="&#xc2;" horiz-adv-x="1236" d="M16 0l562 1464h77l566 -1464h-66l-192 498h-689l-192 -498h-66zM295 555h645l-322 834h-4zM397 1686l178 212h76l180 -212h-79l-136 165h-2l-135 -165h-82z" />
<glyph unicode="&#xc3;" horiz-adv-x="1236" d="M16 0l562 1464h77l566 -1464h-66l-192 498h-689l-192 -498h-66zM295 555h645l-322 834h-4zM336 1696q4 79 40 120.5t97 41.5q38 0 77.5 -17t67 -37.5t60.5 -37.5t59 -17q67 0 76 109h61q-4 -80 -40 -121t-97 -41q-46 0 -95 27t-92.5 54t-76.5 27q-67 0 -76 -108h-61z" />
<glyph unicode="&#xc4;" horiz-adv-x="1236" d="M16 0l562 1464h77l566 -1464h-66l-192 498h-689l-192 -498h-66zM295 555h645l-322 834h-4zM389 1788q0 28 17.5 46.5t48.5 18.5t49 -19t18 -46q0 -28 -18 -47t-49 -19t-48.5 19t-17.5 47zM707 1788q0 28 17 46.5t48 18.5t49.5 -19t18.5 -46t-18.5 -46.5t-49.5 -19.5 t-48 19t-17 47z" />
<glyph unicode="&#xc5;" horiz-adv-x="1236" d="M16 0l562 1464h77l566 -1464h-66l-192 498h-689l-192 -498h-66zM295 555h645l-322 834h-4zM451 1763q0 72 47 120t116 48q71 0 119.5 -48t48.5 -120t-48 -118t-120 -46q-70 0 -116.5 45.5t-46.5 118.5zM502 1763q0 -52 29 -83t83 -31q55 0 85 31t30 83t-30.5 84.5 t-84.5 32.5t-83 -32t-29 -85z" />
<glyph unicode="&#xc6;" horiz-adv-x="1871" d="M25 0l561 1464h1190v-57h-846v-600h774v-57h-774v-693h866v-57h-932v498h-581l-193 -498h-65zM303 555h561v852h-235z" />
<glyph unicode="&#xc7;" horiz-adv-x="1382" d="M76 731q0 220 81 391.5t233.5 268t354.5 96.5q218 0 367.5 -126t183.5 -329h-65q-27 172 -160 285t-326 113q-188 0 -326.5 -92.5t-208 -249.5t-69.5 -357q0 -203 70.5 -360t207 -246.5t320.5 -89.5q107 0 199.5 38.5t157 104t105 149.5t52.5 177h66 q-29 -228 -182.5 -374t-376.5 -153l-101 -86v-4q89 20 143 -14.5t54 -101.5q0 -68 -53.5 -105t-140.5 -37q-106 0 -193 54l20 34q74 -41 164 -41q65 0 103.5 23.5t38.5 67.5q0 79 -111 79q-50 0 -96 -22l-25 31l133 125q-283 15 -451.5 219.5t-168.5 531.5z" />
<glyph unicode="&#xc8;" horiz-adv-x="1191" d="M133 0v1464h963v-57h-897v-600h825v-57h-825v-693h917v-57h-983zM418 1888h84l141 -254h-59z" />
<glyph unicode="&#xc9;" horiz-adv-x="1191" d="M133 0v1464h963v-57h-897v-600h825v-57h-825v-693h917v-57h-983zM600 1634l141 254h84l-166 -254h-59z" />
<glyph unicode="&#xca;" horiz-adv-x="1191" d="M133 0v1464h963v-57h-897v-600h825v-57h-825v-693h917v-57h-983zM406 1686l178 212h75l181 -212h-80l-135 165h-2l-136 -165h-81z" />
<glyph unicode="&#xcb;" horiz-adv-x="1191" d="M133 0v1464h963v-57h-897v-600h825v-57h-825v-693h917v-57h-983zM397 1788q0 28 17.5 46.5t48.5 18.5t49 -19t18 -46q0 -28 -18 -47t-49 -19t-48.5 19t-17.5 47zM715 1788q0 28 17 46.5t48 18.5t49.5 -19t18.5 -46t-18.5 -46.5t-49.5 -19.5t-48 19t-17 47z" />
<glyph unicode="&#xcc;" horiz-adv-x="344" d="M-35 1888h84l141 -254h-59zM139 0v1464h66v-1464h-66z" />
<glyph unicode="&#xcd;" horiz-adv-x="344" d="M139 0v1464h66v-1464h-66zM147 1634l142 254h84l-166 -254h-60z" />
<glyph unicode="&#xce;" horiz-adv-x="344" d="M-47 1686l178 212h76l180 -212h-80l-135 165h-2l-135 -165h-82zM139 0v1464h66v-1464h-66z" />
<glyph unicode="&#xcf;" horiz-adv-x="458" d="M4 1788q0 28 17.5 46.5t48.5 18.5t49 -19t18 -46q0 -28 -18 -47t-49 -19t-48.5 19t-17.5 47zM199 0v1464h65v-1464h-65zM322 1788q0 28 17 46.5t48 18.5t49.5 -19t18.5 -46t-18.5 -46.5t-49.5 -19.5t-48 19t-17 47z" />
<glyph unicode="&#xd0;" horiz-adv-x="1437" d="M6 729v57h221v678h525q124 0 225 -38t171 -104.5t117 -160t69 -202.5t22 -234t-23.5 -233t-72.5 -199t-121.5 -156t-174.5 -101t-227 -36h-510v729h-221zM289 57h446q264 0 409.5 177.5t145.5 490.5q0 317 -139.5 499.5t-400.5 182.5h-461v-621h459v-57h-459v-672z" />
<glyph unicode="&#xd1;" horiz-adv-x="1351" d="M133 0v1464h82l934 -1372h4v1372h66v-1464h-82l-934 1368h-4v-1368h-66zM395 1696q4 79 40 120.5t97 41.5q38 0 77.5 -17t67.5 -37.5t61 -37.5t59 -17q66 0 75 109h62q-4 -80 -40 -121t-97 -41q-46 0 -95.5 27t-93 54t-76.5 27q-66 0 -75 -108h-62z" />
<glyph unicode="&#xd2;" horiz-adv-x="1499" d="M76 733q0 218 84 389.5t237.5 268t352.5 96.5t352.5 -96.5t237 -268t83.5 -389.5t-83.5 -390t-237.5 -269t-352 -97t-352 97t-238 269t-84 390zM141 733q0 -205 80 -365t218.5 -246.5t310.5 -86.5q129 0 241 51t193 141t127.5 221t46.5 285t-46.5 285t-127.5 221 t-193 140.5t-241 50.5t-241 -50.5t-193.5 -140.5t-128 -221t-46.5 -285zM543 1888h84l141 -254h-59z" />
<glyph unicode="&#xd3;" horiz-adv-x="1499" d="M76 733q0 218 84 389.5t237.5 268t352.5 96.5t352.5 -96.5t237 -268t83.5 -389.5t-83.5 -390t-237.5 -269t-352 -97t-352 97t-238 269t-84 390zM141 733q0 -205 80 -365t218.5 -246.5t310.5 -86.5q129 0 241 51t193 141t127.5 221t46.5 285t-46.5 285t-127.5 221 t-193 140.5t-241 50.5t-241 -50.5t-193.5 -140.5t-128 -221t-46.5 -285zM725 1634l141 254h84l-166 -254h-59z" />
<glyph unicode="&#xd4;" horiz-adv-x="1499" d="M76 733q0 218 84 389.5t237.5 268t352.5 96.5t352.5 -96.5t237 -268t83.5 -389.5t-83.5 -390t-237.5 -269t-352 -97t-352 97t-238 269t-84 390zM141 733q0 -205 80 -365t218.5 -246.5t310.5 -86.5q129 0 241 51t193 141t127.5 221t46.5 285t-46.5 285t-127.5 221 t-193 140.5t-241 50.5t-241 -50.5t-193.5 -140.5t-128 -221t-46.5 -285zM530 1686l179 212h75l181 -212h-80l-135 165h-2l-136 -165h-82z" />
<glyph unicode="&#xd5;" horiz-adv-x="1499" d="M76 733q0 218 84 389.5t237.5 268t352.5 96.5t352.5 -96.5t237 -268t83.5 -389.5t-83.5 -390t-237.5 -269t-352 -97t-352 97t-238 269t-84 390zM141 733q0 -205 80 -365t218.5 -246.5t310.5 -86.5q129 0 241 51t193 141t127.5 221t46.5 285t-46.5 285t-127.5 221 t-193 140.5t-241 50.5t-241 -50.5t-193.5 -140.5t-128 -221t-46.5 -285zM469 1696q4 79 40 120.5t97 41.5q38 0 77.5 -17t67 -37.5t60.5 -37.5t59 -17q67 0 76 109h62q-4 -80 -40.5 -121t-97.5 -41q-46 0 -95 27t-92.5 54t-76.5 27q-67 0 -76 -108h-61z" />
<glyph unicode="&#xd6;" horiz-adv-x="1499" d="M76 733q0 218 84 389.5t237.5 268t352.5 96.5t352.5 -96.5t237 -268t83.5 -389.5t-83.5 -390t-237.5 -269t-352 -97t-352 97t-238 269t-84 390zM141 733q0 -205 80 -365t218.5 -246.5t310.5 -86.5q129 0 241 51t193 141t127.5 221t46.5 285t-46.5 285t-127.5 221 t-193 140.5t-241 50.5t-241 -50.5t-193.5 -140.5t-128 -221t-46.5 -285zM522 1788q0 28 17.5 46.5t48.5 18.5t49 -19t18 -46q0 -28 -18 -47t-49 -19t-48.5 19t-17.5 47zM840 1788q0 28 17 46.5t48 18.5t49.5 -19t18.5 -46t-18.5 -46.5t-49.5 -19.5t-48 19t-17 47z" />
<glyph unicode="&#xd7;" horiz-adv-x="1050" d="M154 338l327 328l-327 327l43 43l327 -327l328 327l43 -43l-328 -327l328 -328l-43 -43l-328 328l-327 -328z" />
<glyph unicode="&#xd8;" horiz-adv-x="1525" d="M68 53l180 189q-146 200 -146 491q0 218 84 389.5t237.5 268t352.5 96.5q287 0 475 -197l158 166l45 -41l-164 -172q160 -202 160 -510q0 -218 -84 -390t-238 -269t-352 -97q-150 0 -275 56.5t-216 161.5l-176 -185zM168 733q0 -257 125 -444l913 954 q-82 90 -192.5 138.5t-237.5 48.5q-129 0 -241 -50.5t-193 -140.5t-127.5 -221t-46.5 -285zM330 242q84 -100 198.5 -153.5t247.5 -53.5q129 0 241 51t193 141t127.5 221t46.5 285q0 275 -139 463z" />
<glyph unicode="&#xd9;" horiz-adv-x="1277" d="M113 508v956h65v-956q0 -239 114.5 -356t346.5 -117t346.5 117t114.5 356v956h65v-956q0 -531 -526 -531t-526 531zM432 1888h84l141 -254h-59z" />
<glyph unicode="&#xda;" horiz-adv-x="1277" d="M113 508v956h65v-956q0 -239 114.5 -356t346.5 -117t346.5 117t114.5 356v956h65v-956q0 -531 -526 -531t-526 531zM614 1634l142 254h84l-166 -254h-60z" />
<glyph unicode="&#xdb;" horiz-adv-x="1277" d="M113 508v956h65v-956q0 -239 114.5 -356t346.5 -117t346.5 117t114.5 356v956h65v-956q0 -531 -526 -531t-526 531zM420 1686l178 212h76l180 -212h-80l-135 165h-2l-135 -165h-82z" />
<glyph unicode="&#xdc;" horiz-adv-x="1277" d="M113 508v956h65v-956q0 -239 114.5 -356t346.5 -117t346.5 117t114.5 356v956h65v-956q0 -531 -526 -531t-526 531zM412 1788q0 28 17 46.5t48 18.5t49.5 -19t18.5 -46t-18.5 -46.5t-49.5 -19.5t-48 19t-17 47zM729 1788q0 28 17.5 46.5t48.5 18.5t49 -19t18 -46 q0 -28 -18 -47t-49 -19t-48.5 19t-17.5 47z" />
<glyph unicode="&#xdd;" horiz-adv-x="1179" d="M16 1464h76l494 -792h4l497 792h76l-542 -864v-600h-66v600zM565 1634l142 254h84l-166 -254h-60z" />
<glyph unicode="&#xde;" horiz-adv-x="1163" d="M133 0v1464h66v-225h454q463 0 463 -416q0 -415 -463 -415h-454v-408h-66zM199 465h473q180 0 279.5 88t99.5 270t-99.5 270.5t-279.5 88.5h-473v-717z" />
<glyph unicode="&#xdf;" horiz-adv-x="966" d="M109 0v1073q0 200 92.5 307t281.5 107q69 0 129 -20t108 -60.5t75.5 -107.5t27.5 -154q0 -247 -207 -332v-4q299 -90 299 -412q0 -182 -100 -301t-260 -119q-107 0 -184 29v53q127 -33 218.5 -22t150.5 63.5t86.5 127.5t27.5 169q0 87 -27 156t-69.5 111.5t-98.5 70.5 t-108.5 38.5t-104.5 10.5h-57v54h51q65 0 121.5 18t102 54t72 96.5t26.5 138.5q0 128 -73.5 208.5t-205.5 80.5q-89 0 -151.5 -28t-97 -83t-49.5 -125.5t-15 -165.5v-1032h-61z" />
<glyph unicode="&#xe0;" horiz-adv-x="1003" d="M49 256q0 145 93 219t300 105q21 3 59 8t60.5 8.5t55 10.5t52 15t42.5 22t35.5 32t21 44.5t8.5 59.5q0 116 -77.5 175t-204.5 59q-155 0 -240.5 -68.5t-93.5 -189.5h-62q4 59 25.5 110.5t65 98.5t121.5 74.5t184 27.5q75 0 134.5 -17t108 -53t75 -100.5t26.5 -153.5v-532 v-14v-31.5t1.5 -29.5t6 -29.5t11 -24.5t18.5 -20.5t27.5 -12.5t38.5 -5t52 7v-53q-36 -8 -67 -8q-80 0 -115 41.5t-35 140.5h-4q-118 -195 -385 -195q-152 0 -245 76t-93 203zM111 274q0 -120 75 -181.5t217 -61.5q91 0 165.5 28.5t118 73t66.5 93.5t23 96v311 q-21 -46 -97.5 -68.5t-215.5 -38.5q-168 -21 -260 -78t-92 -174zM281 1477h84l141 -254h-60z" />
<glyph unicode="&#xe1;" horiz-adv-x="1003" d="M49 256q0 145 93 219t300 105q21 3 59 8t60.5 8.5t55 10.5t52 15t42.5 22t35.5 32t21 44.5t8.5 59.5q0 116 -77.5 175t-204.5 59q-155 0 -240.5 -68.5t-93.5 -189.5h-62q4 59 25.5 110.5t65 98.5t121.5 74.5t184 27.5q75 0 134.5 -17t108 -53t75 -100.5t26.5 -153.5v-532 v-14v-31.5t1.5 -29.5t6 -29.5t11 -24.5t18.5 -20.5t27.5 -12.5t38.5 -5t52 7v-53q-36 -8 -67 -8q-80 0 -115 41.5t-35 140.5h-4q-118 -195 -385 -195q-152 0 -245 76t-93 203zM111 274q0 -120 75 -181.5t217 -61.5q91 0 165.5 28.5t118 73t66.5 93.5t23 96v311 q-21 -46 -97.5 -68.5t-215.5 -38.5q-168 -21 -260 -78t-92 -174zM463 1223l141 254h84l-166 -254h-59z" />
<glyph unicode="&#xe2;" horiz-adv-x="1003" d="M49 256q0 145 93 219t300 105q21 3 59 8t60.5 8.5t55 10.5t52 15t42.5 22t35.5 32t21 44.5t8.5 59.5q0 116 -77.5 175t-204.5 59q-155 0 -240.5 -68.5t-93.5 -189.5h-62q4 59 25.5 110.5t65 98.5t121.5 74.5t184 27.5q75 0 134.5 -17t108 -53t75 -100.5t26.5 -153.5v-532 v-14v-31.5t1.5 -29.5t6 -29.5t11 -24.5t18.5 -20.5t27.5 -12.5t38.5 -5t52 7v-53q-36 -8 -67 -8q-80 0 -115 41.5t-35 140.5h-4q-118 -195 -385 -195q-152 0 -245 76t-93 203zM111 274q0 -120 75 -181.5t217 -61.5q91 0 165.5 28.5t118 73t66.5 93.5t23 96v311 q-21 -46 -97.5 -68.5t-215.5 -38.5q-168 -21 -260 -78t-92 -174zM268 1274l178 213h76l180 -213h-79l-136 166h-2l-135 -166h-82z" />
<glyph unicode="&#xe3;" horiz-adv-x="1003" d="M49 256q0 145 93 219t300 105q21 3 59 8t60.5 8.5t55 10.5t52 15t42.5 22t35.5 32t21 44.5t8.5 59.5q0 116 -77.5 175t-204.5 59q-155 0 -240.5 -68.5t-93.5 -189.5h-62q4 59 25.5 110.5t65 98.5t121.5 74.5t184 27.5q75 0 134.5 -17t108 -53t75 -100.5t26.5 -153.5v-532 v-14v-31.5t1.5 -29.5t6 -29.5t11 -24.5t18.5 -20.5t27.5 -12.5t38.5 -5t52 7v-53q-36 -8 -67 -8q-80 0 -115 41.5t-35 140.5h-4q-118 -195 -385 -195q-152 0 -245 76t-93 203zM111 274q0 -120 75 -181.5t217 -61.5q91 0 165.5 28.5t118 73t66.5 93.5t23 96v311 q-21 -46 -97.5 -68.5t-215.5 -38.5q-168 -21 -260 -78t-92 -174zM207 1284q4 79 40 120.5t97 41.5q38 0 77.5 -17t67 -37.5t60.5 -37.5t59 -17q67 0 76 109h61q-4 -80 -40 -121t-97 -41q-46 0 -95 27t-92.5 54.5t-76.5 27.5q-67 0 -76 -109h-61z" />
<glyph unicode="&#xe4;" horiz-adv-x="1003" d="M49 256q0 145 93 219t300 105q21 3 59 8t60.5 8.5t55 10.5t52 15t42.5 22t35.5 32t21 44.5t8.5 59.5q0 116 -77.5 175t-204.5 59q-155 0 -240.5 -68.5t-93.5 -189.5h-62q4 59 25.5 110.5t65 98.5t121.5 74.5t184 27.5q75 0 134.5 -17t108 -53t75 -100.5t26.5 -153.5v-532 v-14v-31.5t1.5 -29.5t6 -29.5t11 -24.5t18.5 -20.5t27.5 -12.5t38.5 -5t52 7v-53q-36 -8 -67 -8q-80 0 -115 41.5t-35 140.5h-4q-118 -195 -385 -195q-152 0 -245 76t-93 203zM111 274q0 -120 75 -181.5t217 -61.5q91 0 165.5 28.5t118 73t66.5 93.5t23 96v311 q-21 -46 -97.5 -68.5t-215.5 -38.5q-168 -21 -260 -78t-92 -174zM260 1376q0 28 17.5 47t48.5 19t49 -19t18 -47q0 -27 -18 -46t-49 -19t-48.5 18.5t-17.5 46.5zM578 1376q0 28 17 47t48 19t49.5 -19.5t18.5 -46.5t-18.5 -46t-49.5 -19t-48 18.5t-17 46.5z" />
<glyph unicode="&#xe5;" horiz-adv-x="1003" d="M49 256q0 145 93 219t300 105q21 3 59 8t60.5 8.5t55 10.5t52 15t42.5 22t35.5 32t21 44.5t8.5 59.5q0 116 -77.5 175t-204.5 59q-155 0 -240.5 -68.5t-93.5 -189.5h-62q4 59 25.5 110.5t65 98.5t121.5 74.5t184 27.5q75 0 134.5 -17t108 -53t75 -100.5t26.5 -153.5v-532 v-14v-31.5t1.5 -29.5t6 -29.5t11 -24.5t18.5 -20.5t27.5 -12.5t38.5 -5t52 7v-53q-36 -8 -67 -8q-80 0 -115 41.5t-35 140.5h-4q-118 -195 -385 -195q-152 0 -245 76t-93 203zM111 274q0 -120 75 -181.5t217 -61.5q91 0 165.5 28.5t118 73t66.5 93.5t23 96v311 q-21 -46 -97.5 -68.5t-215.5 -38.5q-168 -21 -260 -78t-92 -174zM322 1352q0 72 47 120t116 48q71 0 119.5 -48t48.5 -120t-48 -118t-120 -46q-70 0 -116.5 45.5t-46.5 118.5zM373 1352q0 -52 29 -83.5t83 -31.5t84.5 31.5t30.5 83.5t-30.5 84t-84.5 32t-83 -31.5t-29 -84.5 z" />
<glyph unicode="&#xe6;" horiz-adv-x="1746" d="M49 256q0 144 92.5 217.5t300.5 106.5q21 3 59 8t60.5 8.5t55 10.5t52 15t42.5 22t35.5 32t21 44.5t8.5 59.5q0 116 -77.5 175t-204.5 59q-155 0 -240.5 -68.5t-93.5 -189.5h-62q4 59 25.5 110.5t65 98.5t121.5 74.5t184 27.5q304 0 340 -258q58 124 166 191t251 67 q112 0 198 -40t139.5 -112.5t80.5 -169t27 -213.5h-858v-12q0 -102 26.5 -190t77 -155t130.5 -105.5t181 -38.5q70 0 129 19t98.5 48t69.5 68.5t46 75.5t24 73h61q-36 -153 -144 -245.5t-284 -92.5q-156 0 -269.5 79t-166.5 220q-14 -73 -50 -128.5t-80 -87t-100 -51 t-103 -26t-97 -6.5q-152 0 -245 76t-93 203zM111 274q0 -120 75 -181.5t217 -61.5q92 0 165.5 28.5t117.5 76.5t67 105.5t23 120.5v271q-21 -46 -97.5 -68.5t-215.5 -38.5q-168 -21 -260 -78t-92 -174zM838 586h796q0 82 -23.5 156.5t-68.5 136t-120.5 98.5t-170.5 37 q-94 0 -172.5 -35.5t-129.5 -95t-80 -136t-31 -161.5z" />
<glyph unicode="&#xe7;" horiz-adv-x="1005" d="M53 522q0 245 135 395t353 150q177 0 292.5 -103.5t133.5 -254.5h-62q-3 48 -27.5 99t-67.5 98.5t-113.5 77.5t-155.5 30q-100 0 -182.5 -40.5t-134.5 -109t-80.5 -156.5t-28.5 -186t28.5 -186t80.5 -156t134.5 -108.5t182.5 -40.5q69 0 129 20t101 52.5t71 74t45.5 83.5 t17.5 83h62q-11 -149 -124 -255t-284 -112l-100 -86v-4q89 20 142.5 -14.5t53.5 -101.5q0 -68 -53.5 -105t-140.5 -37q-106 0 -193 54l21 34q74 -41 164 -41q65 0 103 23t38 68q0 79 -111 79q-50 0 -96 -22l-25 31l134 125q-199 15 -321 162.5t-122 379.5z" />
<glyph unicode="&#xe8;" horiz-adv-x="1021" d="M53 522q0 253 130 399t345 146q112 0 198 -40t139.5 -112.5t80.5 -169t27 -213.5h-858v-12q0 -102 26.5 -190t77 -155t130.5 -105.5t181 -38.5q70 0 129 19t98.5 48t69.5 68.5t46 75.5t24 73h61q-36 -153 -144 -245.5t-284 -92.5q-219 0 -348 149t-129 396zM115 586h796 q0 82 -23.5 156.5t-68.5 136t-120.5 98.5t-170.5 37q-94 0 -172.5 -35.5t-129.5 -95t-80 -136t-31 -161.5zM328 1477h84l141 -254h-59z" />
<glyph unicode="&#xe9;" horiz-adv-x="1021" d="M53 522q0 253 130 399t345 146q112 0 198 -40t139.5 -112.5t80.5 -169t27 -213.5h-858v-12q0 -102 26.5 -190t77 -155t130.5 -105.5t181 -38.5q70 0 129 19t98.5 48t69.5 68.5t46 75.5t24 73h61q-36 -153 -144 -245.5t-284 -92.5q-219 0 -348 149t-129 396zM115 586h796 q0 82 -23.5 156.5t-68.5 136t-120.5 98.5t-170.5 37q-94 0 -172.5 -35.5t-129.5 -95t-80 -136t-31 -161.5zM510 1223l141 254h84l-166 -254h-59z" />
<glyph unicode="&#xea;" horiz-adv-x="1021" d="M53 522q0 253 130 399t345 146q112 0 198 -40t139.5 -112.5t80.5 -169t27 -213.5h-858v-12q0 -102 26.5 -190t77 -155t130.5 -105.5t181 -38.5q70 0 129 19t98.5 48t69.5 68.5t46 75.5t24 73h61q-36 -153 -144 -245.5t-284 -92.5q-219 0 -348 149t-129 396zM115 586h796 q0 82 -23.5 156.5t-68.5 136t-120.5 98.5t-170.5 37q-94 0 -172.5 -35.5t-129.5 -95t-80 -136t-31 -161.5zM315 1274l179 213h75l181 -213h-80l-135 166h-3l-135 -166h-82z" />
<glyph unicode="&#xeb;" horiz-adv-x="1021" d="M53 522q0 253 130 399t345 146q112 0 198 -40t139.5 -112.5t80.5 -169t27 -213.5h-858v-12q0 -102 26.5 -190t77 -155t130.5 -105.5t181 -38.5q70 0 129 19t98.5 48t69.5 68.5t46 75.5t24 73h61q-36 -153 -144 -245.5t-284 -92.5q-219 0 -348 149t-129 396zM115 586h796 q0 82 -23.5 156.5t-68.5 136t-120.5 98.5t-170.5 37q-94 0 -172.5 -35.5t-129.5 -95t-80 -136t-31 -161.5zM307 1376q0 28 17.5 47t48.5 19t49 -19t18 -47q0 -27 -18 -46t-49 -19t-48.5 18.5t-17.5 46.5zM625 1376q0 28 17 47t48 19t49.5 -19.5t18.5 -46.5t-18.5 -46 t-49.5 -19t-48 18.5t-17 46.5z" />
<glyph unicode="&#xec;" horiz-adv-x="184" d="M-115 1477h84l142 -254h-60zM61 0v1053h62v-1053h-62z" />
<glyph unicode="&#xed;" horiz-adv-x="288" d="M111 0v1053h61v-1053h-61zM117 1223l141 254h84l-166 -254h-59z" />
<glyph unicode="&#xee;" horiz-adv-x="184" d="M-127 1274l178 213h76l180 -213h-80l-135 166h-2l-135 -166h-82zM61 0v1053h62v-1053h-62z" />
<glyph unicode="&#xef;" horiz-adv-x="458" d="M4 1376q0 28 17.5 47t48.5 19t49 -19t18 -47q0 -27 -18 -46t-49 -19t-48.5 18.5t-17.5 46.5zM201 0v1053h61v-1053h-61zM322 1376q0 28 17 47t48 19t49.5 -19.5t18.5 -46.5t-18.5 -46t-49.5 -19t-48 18.5t-17 46.5z" />
<glyph unicode="&#xf0;" horiz-adv-x="1067" d="M53 528q0 153 56 276.5t164.5 197t250.5 73.5q122 0 225.5 -58.5t161.5 -160.5q-45 193 -262 418l-332 -88l-14 55l299 80q-95 89 -172 143h88q91 -74 146 -127l329 88l15 -55l-297 -80q305 -317 305 -737q0 -166 -55 -294.5t-164.5 -203.5t-259.5 -75 q-144 0 -255.5 73.5t-170 197.5t-58.5 277zM115 528q0 -136 50 -248t147.5 -179.5t224.5 -67.5q132 0 227.5 69t141.5 183t46 258q0 208 -117 343.5t-311 135.5q-126 0 -220.5 -67.5t-141.5 -178.5t-47 -248z" />
<glyph unicode="&#xf1;" horiz-adv-x="987" d="M109 0v1053h61v-191h4q28 42 59.5 75t78.5 64.5t112.5 48.5t144.5 17q131 0 223.5 -82.5t92.5 -230.5v-754h-62v713q0 50 -7 91.5t-26 81.5t-49.5 67.5t-81.5 44t-118 16.5q-83 0 -155.5 -29t-118 -73.5t-71.5 -93.5t-26 -93v-725h-61zM238 1284q4 79 40 120.5t97 41.5 q38 0 77.5 -17t67 -37.5t60.5 -37.5t59 -17q67 0 76 109h61q-4 -80 -40 -121t-97 -41q-46 0 -95 27t-92.5 54.5t-76.5 27.5q-67 0 -76 -109h-61z" />
<glyph unicode="&#xf2;" d="M53 522q0 247 130 396t352 149t351.5 -149t129.5 -396t-129.5 -396t-351.5 -149t-352 149t-130 396zM115 522q0 -219 111.5 -355t308.5 -136t308 136t111 355t-111 355.5t-308 136.5t-308.5 -136.5t-111.5 -355.5zM328 1477h84l141 -254h-59z" />
<glyph unicode="&#xf3;" d="M53 522q0 247 130 396t352 149t351.5 -149t129.5 -396t-129.5 -396t-351.5 -149t-352 149t-130 396zM115 522q0 -219 111.5 -355t308.5 -136t308 136t111 355t-111 355.5t-308 136.5t-308.5 -136.5t-111.5 -355.5zM510 1223l141 254h84l-166 -254h-59z" />
<glyph unicode="&#xf4;" d="M53 522q0 247 130 396t352 149t351.5 -149t129.5 -396t-129.5 -396t-351.5 -149t-352 149t-130 396zM115 522q0 -219 111.5 -355t308.5 -136t308 136t111 355t-111 355.5t-308 136.5t-308.5 -136.5t-111.5 -355.5zM315 1274l179 213h75l181 -213h-80l-135 166h-3 l-135 -166h-82z" />
<glyph unicode="&#xf5;" d="M53 522q0 247 130 396t352 149t351.5 -149t129.5 -396t-129.5 -396t-351.5 -149t-352 149t-130 396zM115 522q0 -219 111.5 -355t308.5 -136t308 136t111 355t-111 355.5t-308 136.5t-308.5 -136.5t-111.5 -355.5zM254 1284q4 79 40 120.5t97 41.5q38 0 77.5 -17 t67 -37.5t60.5 -37.5t59 -17q67 0 76 109h62q-4 -80 -40.5 -121t-97.5 -41q-46 0 -95 27t-92.5 54.5t-76.5 27.5q-67 0 -76 -109h-61z" />
<glyph unicode="&#xf6;" d="M53 522q0 247 130 396t352 149t351.5 -149t129.5 -396t-129.5 -396t-351.5 -149t-352 149t-130 396zM115 522q0 -219 111.5 -355t308.5 -136t308 136t111 355t-111 355.5t-308 136.5t-308.5 -136.5t-111.5 -355.5zM307 1376q0 28 17.5 47t48.5 19t49 -19t18 -47 q0 -27 -18 -46t-49 -19t-48.5 18.5t-17.5 46.5zM625 1376q0 28 17 47t48 19t49.5 -19.5t18.5 -46.5t-18.5 -46t-49.5 -19t-48 18.5t-17 46.5z" />
<glyph unicode="&#xf7;" horiz-adv-x="1073" d="M111 641v57h852v-57h-852zM467 346q0 64 70 64q32 0 50.5 -16.5t18.5 -47.5q0 -30 -18 -46.5t-51 -16.5q-70 0 -70 63zM467 993q0 64 70 64q32 0 50.5 -16.5t18.5 -47.5q0 -30 -18 -46.5t-51 -16.5q-70 0 -70 63z" />
<glyph unicode="&#xf8;" horiz-adv-x="1120" d="M49 23l131 139q-100 142 -100 360q0 247 129.5 396t351.5 149q211 0 342 -137l127 135l43 -43l-131 -139q100 -142 100 -361q0 -247 -129.5 -396t-351.5 -149q-212 0 -342 140l-129 -137zM141 522q0 -185 82 -315l639 680q-114 127 -301 127q-197 0 -308.5 -136.5 t-111.5 -355.5zM260 160q110 -129 301 -129q197 0 308.5 136t111.5 355q0 186 -82 316z" />
<glyph unicode="&#xf9;" horiz-adv-x="987" d="M102 299v754h62v-713q0 -50 7 -91.5t26 -81.5t49.5 -67.5t81.5 -44t118 -16.5q83 0 155.5 29t118 73.5t71.5 93.5t26 93v725h62v-1053h-62v190h-4q-28 -42 -59.5 -75t-78.5 -64t-112.5 -48t-144.5 -17q-131 0 -223.5 82.5t-92.5 230.5zM287 1477h84l141 -254h-59z" />
<glyph unicode="&#xfa;" horiz-adv-x="987" d="M102 299v754h62v-713q0 -50 7 -91.5t26 -81.5t49.5 -67.5t81.5 -44t118 -16.5q83 0 155.5 29t118 73.5t71.5 93.5t26 93v725h62v-1053h-62v190h-4q-28 -42 -59.5 -75t-78.5 -64t-112.5 -48t-144.5 -17q-131 0 -223.5 82.5t-92.5 230.5zM469 1223l141 254h84l-166 -254 h-59z" />
<glyph unicode="&#xfb;" horiz-adv-x="987" d="M102 299v754h62v-713q0 -50 7 -91.5t26 -81.5t49.5 -67.5t81.5 -44t118 -16.5q83 0 155.5 29t118 73.5t71.5 93.5t26 93v725h62v-1053h-62v190h-4q-28 -42 -59.5 -75t-78.5 -64t-112.5 -48t-144.5 -17q-131 0 -223.5 82.5t-92.5 230.5zM274 1274l179 213h75l181 -213h-80 l-135 166h-2l-136 -166h-82z" />
<glyph unicode="&#xfc;" horiz-adv-x="987" d="M102 299v754h62v-713q0 -50 7 -91.5t26 -81.5t49.5 -67.5t81.5 -44t118 -16.5q83 0 155.5 29t118 73.5t71.5 93.5t26 93v725h62v-1053h-62v190h-4q-28 -42 -59.5 -75t-78.5 -64t-112.5 -48t-144.5 -17q-131 0 -223.5 82.5t-92.5 230.5zM266 1376q0 28 17.5 47t48.5 19 t49 -19t18 -47q0 -27 -18 -46t-49 -19t-48.5 18.5t-17.5 46.5zM584 1376q0 28 17 47t48 19t49.5 -19.5t18.5 -46.5t-18.5 -46t-49.5 -19t-48 18.5t-17 46.5z" />
<glyph unicode="&#xfd;" horiz-adv-x="901" d="M10 1053h66l381 -965h2l366 965h66l-414 -1088q-23 -60 -36.5 -93t-34 -75t-37.5 -63.5t-40.5 -42.5t-51.5 -29t-62 -8q-61 0 -123 20v54h4q39 -8 57 -10.5t51 -4.5t56.5 6.5t40.5 26.5q32 32 67.5 106t57.5 152zM426 1223l141 254h84l-166 -254h-59z" />
<glyph unicode="&#xfe;" horiz-adv-x="1071" d="M109 -340v1804h61v-688h4q49 131 149 211t246 80q94 0 176 -37.5t143 -105.5t96.5 -172t35.5 -230t-35.5 -230t-96.5 -172t-143 -105.5t-176 -37.5q-145 0 -246 80.5t-149 204.5h-4v-602h-61zM170 522q0 -222 113 -356.5t272 -134.5q180 0 291.5 132t111.5 359 t-111.5 359.5t-291.5 132.5q-159 0 -272 -135t-113 -357z" />
<glyph unicode="&#xff;" horiz-adv-x="901" d="M10 1053h66l381 -965h2l366 965h66l-414 -1088q-23 -60 -36.5 -93t-34 -75t-37.5 -63.5t-40.5 -42.5t-51.5 -29t-62 -8q-61 0 -123 20v54h4q39 -8 57 -10.5t51 -4.5t56.5 6.5t40.5 26.5q32 32 67.5 106t57.5 152zM223 1376q0 28 17.5 47t48.5 19t49 -19t18 -47 q0 -27 -18 -46t-49 -19t-48.5 18.5t-17.5 46.5zM541 1376q0 28 17 47t48 19t49.5 -19.5t18.5 -46.5t-18.5 -46t-49.5 -19t-48 18.5t-17 46.5z" />
<glyph unicode="&#x152;" horiz-adv-x="2162" d="M76 733q0 163 44 303t121.5 239t186.5 155.5t236 56.5q175 0 312 -103.5t208 -283.5v364h882v-57h-817v-600h746v-57h-746v-693h838v-57h-903v367q-71 -181 -208 -285.5t-312 -104.5q-126 0 -235.5 56.5t-187 156t-121.5 240t-44 303.5zM141 733q0 -153 41.5 -284.5 t113 -221.5t167 -141t201.5 -51t201.5 51t165.5 141t111.5 221.5t41.5 284.5t-41.5 284.5t-111.5 221.5t-165.5 140.5t-201.5 50.5t-201.5 -50.5t-167 -140.5t-113 -221.5t-41.5 -284.5z" />
<glyph unicode="&#x153;" horiz-adv-x="1914" d="M53 522q0 247 130 396t352 149q167 0 283.5 -86.5t164.5 -239.5q49 155 166 240.5t278 85.5q112 0 198 -40t139.5 -112.5t80.5 -169t27 -213.5h-856v-10v-41q6 -128 53.5 -228t140.5 -161t220 -61q70 0 129 19t98 48t69 68.5t46 75.5t24 73h62q-36 -153 -144 -245.5 t-284 -92.5q-164 0 -280.5 86.5t-166.5 239.5q-48 -153 -164.5 -239.5t-283.5 -86.5q-222 0 -352 149t-130 396zM115 522q0 -219 111.5 -355t308.5 -136q188 0 298 123.5t119 326.5v41v41q-9 203 -119 327t-298 124q-197 0 -308.5 -136.5t-111.5 -355.5zM1014 586h796 q0 82 -23.5 156.5t-68.5 136t-120.5 98.5t-170.5 37q-94 0 -172.5 -35.5t-129.5 -95t-80 -136t-31 -161.5z" />
<glyph unicode="&#x178;" horiz-adv-x="1179" d="M16 1464h76l494 -792h4l497 792h76l-542 -864v-600h-66v600zM362 1788q0 28 17.5 46.5t48.5 18.5t49.5 -19t18.5 -46t-18.5 -46.5t-49.5 -19.5t-48.5 19t-17.5 47zM680 1788q0 28 17 46.5t48 18.5t49.5 -19t18.5 -46t-18.5 -46.5t-49.5 -19.5t-48 19t-17 47z" />
<glyph unicode="&#x2c6;" horiz-adv-x="944" d="M256 1274l178 213h76l180 -213h-80l-135 166h-2l-135 -166h-82z" />
<glyph unicode="&#x2dc;" horiz-adv-x="1007" d="M233 1284q4 79 40.5 120.5t97.5 41.5q38 0 77.5 -17t67 -37.5t60.5 -37.5t59 -17q67 0 76 109h61q-4 -80 -40 -121t-97 -41q-46 0 -95 27t-92.5 54.5t-76.5 27.5q-67 0 -76 -109h-62z" />
<glyph unicode="&#x2000;" horiz-adv-x="965" />
<glyph unicode="&#x2001;" horiz-adv-x="1931" />
<glyph unicode="&#x2002;" horiz-adv-x="965" />
<glyph unicode="&#x2003;" horiz-adv-x="1931" />
<glyph unicode="&#x2004;" horiz-adv-x="643" />
<glyph unicode="&#x2005;" horiz-adv-x="482" />
<glyph unicode="&#x2006;" horiz-adv-x="321" />
<glyph unicode="&#x2007;" horiz-adv-x="321" />
<glyph unicode="&#x2008;" horiz-adv-x="241" />
<glyph unicode="&#x2009;" horiz-adv-x="386" />
<glyph unicode="&#x200a;" horiz-adv-x="107" />
<glyph unicode="&#x2010;" horiz-adv-x="696" d="M74 494v57h549v-57h-549z" />
<glyph unicode="&#x2011;" horiz-adv-x="696" d="M74 494v57h549v-57h-549z" />
<glyph unicode="&#x2012;" horiz-adv-x="696" d="M74 494v57h549v-57h-549z" />
<glyph unicode="&#x2013;" horiz-adv-x="1024" d="M0 494v57h1024v-57h-1024z" />
<glyph unicode="&#x2014;" horiz-adv-x="2048" d="M205 494v57h1638v-57h-1638z" />
<glyph unicode="&#x2018;" horiz-adv-x="270" d="M72 1264q0 175 112 209v-58q-57 -27 -57 -139q28 9 48 -6.5t20 -52.5q0 -30 -17 -47t-43 -17q-63 0 -63 111z" />
<glyph unicode="&#x2019;" horiz-adv-x="272" d="M63 1409q0 30 17 47t43 17q63 0 63 -111q0 -175 -112 -209v57q57 27 57 140q-28 -9 -48 6.5t-20 52.5z" />
<glyph unicode="&#x201a;" horiz-adv-x="292" d="M84 51q0 30 16.5 47t42.5 17q64 0 64 -111q0 -175 -113 -209v58q58 27 58 139q-28 -9 -48 6.5t-20 52.5z" />
<glyph unicode="&#x201c;" horiz-adv-x="512" d="M63 1264q0 175 113 209v-58q-57 -27 -57 -139q27 9 47 -6.5t20 -52.5q0 -30 -16.5 -47t-42.5 -17q-64 0 -64 111zM313 1264q0 175 113 209v-58q-57 -27 -57 -139q27 9 47 -6.5t20 -52.5q0 -30 -16.5 -47t-42.5 -17q-64 0 -64 111z" />
<glyph unicode="&#x201d;" horiz-adv-x="534" d="M76 1409q0 30 16.5 47t42.5 17q64 0 64 -111q0 -175 -113 -209v57q57 27 57 140q-27 -9 -47 6.5t-20 52.5zM326 1409q0 30 16.5 47t42.5 17q64 0 64 -111q0 -175 -113 -209v57q57 27 57 140q-27 -9 -47 6.5t-20 52.5z" />
<glyph unicode="&#x201e;" horiz-adv-x="542" d="M84 51q0 30 16.5 47t42.5 17q64 0 64 -111q0 -175 -113 -209v58q58 27 58 139q-28 -9 -48 6.5t-20 52.5zM334 51q0 30 16.5 47t42.5 17q64 0 64 -111q0 -175 -113 -209v58q57 27 57 139q-27 -9 -47 6.5t-20 52.5z" />
<glyph unicode="&#x2022;" horiz-adv-x="434" d="M74 655q0 62 39 103t104 41t104 -41t39 -103q0 -60 -39.5 -101.5t-103.5 -41.5t-103.5 41.5t-39.5 101.5z" />
<glyph unicode="&#x2026;" horiz-adv-x="872" d="M88 53q0 29 19 45.5t43 16.5t42.5 -16.5t18.5 -45.5t-18.5 -45t-42.5 -16t-43 16t-19 45zM375 53q0 29 18.5 45.5t42.5 16.5t43 -16.5t19 -45.5t-19 -45t-43 -16t-42.5 16t-18.5 45zM662 53q0 29 18.5 45.5t42.5 16.5t42.5 -16.5t18.5 -45.5t-18.5 -45t-42.5 -16 t-42.5 16t-18.5 45z" />
<glyph unicode="&#x202f;" horiz-adv-x="386" />
<glyph unicode="&#x2039;" horiz-adv-x="505" d="M111 514v82l282 246v-82l-237 -203v-4l237 -203v-82z" />
<glyph unicode="&#x203a;" horiz-adv-x="505" d="M111 268v82l237 203v4l-237 203v82l282 -246v-82z" />
<glyph unicode="&#x205f;" horiz-adv-x="482" />
<glyph unicode="&#x20ac;" horiz-adv-x="1234" d="M61 549v57h129q-4 62 -4 96q0 35 4 97h-129v57h134q31 266 165 417.5t345 151.5q103 0 187.5 -35.5t141.5 -97.5t92 -141t48 -170h-66q-10 74 -39 141t-77 123t-122 89.5t-165 33.5q-186 0 -301.5 -137t-143.5 -375h504v-57h-508q-4 -62 -4 -97q0 -34 4 -96h508v-57h-504 q28 -239 143.5 -376.5t301.5 -137.5q122 0 213.5 63t137.5 163.5t52 225.5h66q-24 -236 -143 -373t-326 -137q-210 0 -344.5 153t-165.5 419h-134z" />
<glyph unicode="&#x2122;" horiz-adv-x="1323" d="M68 1407v57h483v-57h-209v-508h-66v508h-208zM647 899v565h103l190 -495h2l191 495h102v-565h-66v481h-4l-184 -481h-80l-184 481h-4v-481h-66z" />
<glyph unicode="&#x25fc;" horiz-adv-x="1054" d="M0 0v1055h1055v-1055h-1055z" />
<hkern u1="&#x23;" u2="&#x34;" k="31" />
<hkern u1="&#x23;" u2="&#x32;" k="-20" />
<hkern u1="&#x23;" u2="&#x31;" k="-20" />
<hkern u1="&#x24;" u2="&#x37;" k="51" />
<hkern u1="&#x26;" u2="v" k="82" />
<hkern u1="&#x26;" u2="V" k="154" />
<hkern u1="&#x26;" u2="&#x39;" k="20" />
<hkern u1="&#x26;" u2="&#x37;" k="61" />
<hkern u1="&#x26;" u2="&#x36;" k="20" />
<hkern u1="&#x26;" u2="&#x34;" k="31" />
<hkern u1="&#x26;" u2="&#x33;" k="20" />
<hkern u1="&#x26;" u2="&#x32;" k="-20" />
<hkern u1="&#x26;" u2="&#x31;" k="82" />
<hkern u1="&#x26;" u2="&#x30;" k="20" />
<hkern u1="&#x28;" u2="x" k="20" />
<hkern u1="&#x28;" u2="v" k="41" />
<hkern u1="&#x28;" u2="&#x38;" k="20" />
<hkern u1="&#x28;" u2="&#x37;" k="-20" />
<hkern u1="&#x28;" u2="&#x34;" k="61" />
<hkern u1="&#x28;" u2="&#x30;" k="20" />
<hkern u1="&#x2a;" u2="x" k="51" />
<hkern u1="&#x2a;" u2="v" k="-31" />
<hkern u1="&#x2a;" u2="p" k="20" />
<hkern u1="&#x2a;" u2="X" k="51" />
<hkern u1="&#x2a;" u2="&#x39;" k="20" />
<hkern u1="&#x2a;" u2="&#x38;" k="41" />
<hkern u1="&#x2a;" u2="&#x36;" k="51" />
<hkern u1="&#x2a;" u2="&#x35;" k="51" />
<hkern u1="&#x2a;" u2="&#x34;" k="195" />
<hkern u1="&#x2a;" u2="&#x33;" k="20" />
<hkern u1="&#x2a;" u2="&#x32;" k="20" />
<hkern u1="&#x2a;" u2="&#x30;" k="41" />
<hkern u1="&#x2c;" u2="&#xff;" k="82" />
<hkern u1="&#x2c;" u2="&#xfd;" k="82" />
<hkern u1="&#x2c;" u2="y" k="82" />
<hkern u1="&#x2c;" u2="j" k="-41" />
<hkern u1="&#x2f;" u2="&#xdf;" k="61" />
<hkern u1="&#x2f;" u2="x" k="143" />
<hkern u1="&#x2f;" u2="v" k="102" />
<hkern u1="&#x2f;" u2="p" k="123" />
<hkern u1="&#x2f;" u2="V" k="-20" />
<hkern u1="&#x2f;" u2="&#x39;" k="61" />
<hkern u1="&#x2f;" u2="&#x38;" k="82" />
<hkern u1="&#x2f;" u2="&#x36;" k="92" />
<hkern u1="&#x2f;" u2="&#x35;" k="61" />
<hkern u1="&#x2f;" u2="&#x34;" k="205" />
<hkern u1="&#x2f;" u2="&#x33;" k="82" />
<hkern u1="&#x2f;" u2="&#x32;" k="82" />
<hkern u1="&#x2f;" u2="&#x31;" k="41" />
<hkern u1="&#x2f;" u2="&#x30;" k="102" />
<hkern u1="&#x2f;" u2="&#x2f;" k="256" />
<hkern u1="&#x30;" u2="&#x2122;" k="61" />
<hkern u1="&#x30;" u2="&#xb0;" k="61" />
<hkern u1="&#x30;" u2="\" k="102" />
<hkern u1="&#x30;" u2="&#x39;" k="10" />
<hkern u1="&#x30;" u2="&#x38;" k="20" />
<hkern u1="&#x30;" u2="&#x37;" k="72" />
<hkern u1="&#x30;" u2="&#x32;" k="10" />
<hkern u1="&#x30;" u2="&#x31;" k="31" />
<hkern u1="&#x30;" u2="&#x2f;" k="82" />
<hkern u1="&#x30;" u2="&#x2a;" k="41" />
<hkern u1="&#x30;" u2="&#x29;" k="20" />
<hkern u1="&#x30;" u2="&#x26;" k="20" />
<hkern u1="&#x31;" u2="&#x40;" k="-20" />
<hkern u1="&#x31;" u2="&#x34;" k="20" />
<hkern u1="&#x31;" u2="&#x31;" k="41" />
<hkern u1="&#x31;" u2="&#x29;" k="-41" />
<hkern u1="&#x32;" u2="&#x2122;" k="41" />
<hkern u1="&#x32;" u2="&#xa2;" k="20" />
<hkern u1="&#x32;" u2="\" k="51" />
<hkern u1="&#x32;" u2="&#x3f;" k="-20" />
<hkern u1="&#x32;" u2="&#x3c;" k="20" />
<hkern u1="&#x32;" u2="&#x39;" k="-20" />
<hkern u1="&#x32;" u2="&#x38;" k="10" />
<hkern u1="&#x32;" u2="&#x37;" k="20" />
<hkern u1="&#x32;" u2="&#x34;" k="61" />
<hkern u1="&#x32;" u2="&#x2f;" k="20" />
<hkern u1="&#x32;" u2="&#x26;" k="20" />
<hkern u1="&#x33;" u2="&#x2122;" k="41" />
<hkern u1="&#x33;" u2="&#xb7;" k="-20" />
<hkern u1="&#x33;" u2="&#xb0;" k="41" />
<hkern u1="&#x33;" u2="\" k="72" />
<hkern u1="&#x33;" u2="&#x40;" k="-20" />
<hkern u1="&#x33;" u2="&#x37;" k="51" />
<hkern u1="&#x33;" u2="&#x2a;" k="31" />
<hkern u1="&#x33;" u2="&#x26;" k="-20" />
<hkern u1="&#x34;" u2="&#x2122;" k="82" />
<hkern u1="&#x34;" u2="&#xb0;" k="82" />
<hkern u1="&#x34;" u2="\" k="82" />
<hkern u1="&#x34;" u2="&#x3f;" k="41" />
<hkern u1="&#x34;" u2="&#x3c;" k="20" />
<hkern u1="&#x34;" u2="&#x39;" k="20" />
<hkern u1="&#x34;" u2="&#x37;" k="20" />
<hkern u1="&#x34;" u2="&#x31;" k="61" />
<hkern u1="&#x34;" u2="&#x2f;" k="20" />
<hkern u1="&#x34;" u2="&#x2a;" k="102" />
<hkern u1="&#x35;" u2="&#x2122;" k="20" />
<hkern u1="&#x35;" u2="&#xb0;" k="51" />
<hkern u1="&#x35;" u2="\" k="41" />
<hkern u1="&#x35;" u2="&#x3f;" k="20" />
<hkern u1="&#x35;" u2="&#x39;" k="20" />
<hkern u1="&#x35;" u2="&#x37;" k="31" />
<hkern u1="&#x35;" u2="&#x31;" k="61" />
<hkern u1="&#x35;" u2="&#x2f;" k="20" />
<hkern u1="&#x35;" u2="&#x2a;" k="41" />
<hkern u1="&#x35;" u2="&#x26;" k="-20" />
<hkern u1="&#x36;" u2="&#x2122;" k="41" />
<hkern u1="&#x36;" u2="&#xb7;" k="-20" />
<hkern u1="&#x36;" u2="&#xb0;" k="31" />
<hkern u1="&#x36;" u2="\" k="51" />
<hkern u1="&#x36;" u2="&#x3e;" k="31" />
<hkern u1="&#x36;" u2="&#x39;" k="20" />
<hkern u1="&#x36;" u2="&#x37;" k="51" />
<hkern u1="&#x36;" u2="&#x32;" k="20" />
<hkern u1="&#x36;" u2="&#x2f;" k="31" />
<hkern u1="&#x36;" u2="&#x2a;" k="31" />
<hkern u1="&#x37;" u2="&#x2122;" k="-41" />
<hkern u1="&#x37;" u2="&#xb7;" k="41" />
<hkern u1="&#x37;" u2="&#xb0;" k="-20" />
<hkern u1="&#x37;" u2="&#xae;" k="41" />
<hkern u1="&#x37;" u2="&#xa7;" k="31" />
<hkern u1="&#x37;" u2="&#xa2;" k="123" />
<hkern u1="&#x37;" u2="&#x40;" k="61" />
<hkern u1="&#x37;" u2="&#x3d;" k="61" />
<hkern u1="&#x37;" u2="&#x3c;" k="82" />
<hkern u1="&#x37;" u2="&#x39;" k="31" />
<hkern u1="&#x37;" u2="&#x38;" k="51" />
<hkern u1="&#x37;" u2="&#x37;" k="-20" />
<hkern u1="&#x37;" u2="&#x36;" k="41" />
<hkern u1="&#x37;" u2="&#x35;" k="41" />
<hkern u1="&#x37;" u2="&#x34;" k="143" />
<hkern u1="&#x37;" u2="&#x33;" k="31" />
<hkern u1="&#x37;" u2="&#x32;" k="20" />
<hkern u1="&#x37;" u2="&#x30;" k="41" />
<hkern u1="&#x37;" u2="&#x2f;" k="154" />
<hkern u1="&#x37;" u2="&#x2a;" k="-20" />
<hkern u1="&#x37;" u2="&#x29;" k="-20" />
<hkern u1="&#x37;" u2="&#x26;" k="92" />
<hkern u1="&#x37;" u2="&#x23;" k="61" />
<hkern u1="&#x38;" u2="&#x2122;" k="61" />
<hkern u1="&#x38;" u2="&#xb0;" k="41" />
<hkern u1="&#x38;" u2="&#xae;" k="20" />
<hkern u1="&#x38;" u2="\" k="82" />
<hkern u1="&#x38;" u2="&#x3f;" k="20" />
<hkern u1="&#x38;" u2="&#x39;" k="20" />
<hkern u1="&#x38;" u2="&#x37;" k="72" />
<hkern u1="&#x38;" u2="&#x36;" k="20" />
<hkern u1="&#x38;" u2="&#x32;" k="10" />
<hkern u1="&#x38;" u2="&#x31;" k="31" />
<hkern u1="&#x38;" u2="&#x30;" k="20" />
<hkern u1="&#x38;" u2="&#x2a;" k="41" />
<hkern u1="&#x38;" u2="&#x29;" k="20" />
<hkern u1="&#x38;" u2="&#x26;" k="-20" />
<hkern u1="&#x39;" u2="&#x2122;" k="41" />
<hkern u1="&#x39;" u2="&#xb7;" k="-20" />
<hkern u1="&#x39;" u2="&#xb0;" k="31" />
<hkern u1="&#x39;" u2="\" k="72" />
<hkern u1="&#x39;" u2="&#x37;" k="51" />
<hkern u1="&#x39;" u2="&#x2f;" k="61" />
<hkern u1="&#x39;" u2="&#x2a;" k="20" />
<hkern u1="&#x3b;" u2="j" k="-31" />
<hkern u1="&#x3c;" u2="&#x37;" k="20" />
<hkern u1="&#x3d;" u2="&#x37;" k="61" />
<hkern u1="&#x3d;" u2="&#x31;" k="82" />
<hkern u1="&#x3e;" u2="&#x39;" k="20" />
<hkern u1="&#x3e;" u2="&#x37;" k="102" />
<hkern u1="&#x3e;" u2="&#x33;" k="20" />
<hkern u1="&#x3e;" u2="&#x32;" k="41" />
<hkern u1="&#x3e;" u2="&#x31;" k="61" />
<hkern u1="&#x40;" u2="x" k="61" />
<hkern u1="&#x40;" u2="v" k="20" />
<hkern u1="&#x40;" u2="X" k="123" />
<hkern u1="&#x40;" u2="V" k="92" />
<hkern u1="&#x40;" u2="&#x38;" k="20" />
<hkern u1="&#x40;" u2="&#x37;" k="82" />
<hkern u1="&#x40;" u2="&#x32;" k="20" />
<hkern u1="&#x40;" u2="&#x31;" k="20" />
<hkern u1="B" u2="&#x2122;" k="41" />
<hkern u1="B" u2="x" k="41" />
<hkern u1="B" u2="v" k="20" />
<hkern u1="B" u2="\" k="72" />
<hkern u1="B" u2="X" k="41" />
<hkern u1="B" u2="V" k="41" />
<hkern u1="B" u2="&#x3f;" k="20" />
<hkern u1="B" u2="&#x2a;" k="20" />
<hkern u1="B" u2="&#x29;" k="20" />
<hkern u1="F" u2="&#xdf;" k="41" />
<hkern u1="F" u2="&#xb7;" k="41" />
<hkern u1="F" u2="&#xae;" k="20" />
<hkern u1="F" u2="x" k="102" />
<hkern u1="F" u2="v" k="82" />
<hkern u1="F" u2="p" k="61" />
<hkern u1="F" u2="&#x40;" k="72" />
<hkern u1="F" u2="&#x3f;" k="20" />
<hkern u1="F" u2="&#x2f;" k="154" />
<hkern u1="F" u2="&#x29;" k="-20" />
<hkern u1="F" u2="&#x26;" k="92" />
<hkern u1="P" u2="&#xae;" k="-20" />
<hkern u1="P" u2="x" k="20" />
<hkern u1="P" u2="v" k="-31" />
<hkern u1="P" u2="\" k="20" />
<hkern u1="P" u2="X" k="51" />
<hkern u1="P" u2="V" k="10" />
<hkern u1="P" u2="&#x40;" k="20" />
<hkern u1="P" u2="&#x3f;" k="-20" />
<hkern u1="P" u2="&#x2f;" k="154" />
<hkern u1="P" u2="&#x2a;" k="-20" />
<hkern u1="P" u2="&#x26;" k="82" />
<hkern u1="Q" u2="&#x2122;" k="61" />
<hkern u1="Q" u2="x" k="31" />
<hkern u1="Q" u2="v" k="10" />
<hkern u1="Q" u2="\" k="102" />
<hkern u1="Q" u2="X" k="61" />
<hkern u1="Q" u2="V" k="82" />
<hkern u1="Q" u2="&#x2f;" k="41" />
<hkern u1="Q" u2="&#x2a;" k="41" />
<hkern u1="V" u2="&#x2122;" k="-31" />
<hkern u1="V" u2="&#xdf;" k="31" />
<hkern u1="V" u2="&#xb7;" k="102" />
<hkern u1="V" u2="&#xae;" k="72" />
<hkern u1="V" u2="x" k="72" />
<hkern u1="V" u2="v" k="41" />
<hkern u1="V" u2="p" k="51" />
<hkern u1="V" u2="\" k="-20" />
<hkern u1="V" u2="V" k="-31" />
<hkern u1="V" u2="&#x40;" k="113" />
<hkern u1="V" u2="&#x3f;" k="20" />
<hkern u1="V" u2="&#x2f;" k="174" />
<hkern u1="V" u2="&#x26;" k="113" />
<hkern u1="X" u2="&#xdf;" k="20" />
<hkern u1="X" u2="&#xb7;" k="102" />
<hkern u1="X" u2="&#xae;" k="102" />
<hkern u1="X" u2="v" k="72" />
<hkern u1="X" u2="&#x40;" k="72" />
<hkern u1="X" u2="&#x3f;" k="41" />
<hkern u1="X" u2="&#x2a;" k="51" />
<hkern u1="X" u2="&#x26;" k="41" />
<hkern u1="\" u2="v" k="82" />
<hkern u1="\" u2="V" k="174" />
<hkern u1="\" u2="\" k="256" />
<hkern u1="\" u2="&#x39;" k="41" />
<hkern u1="\" u2="&#x37;" k="123" />
<hkern u1="\" u2="&#x36;" k="61" />
<hkern u1="\" u2="&#x34;" k="20" />
<hkern u1="\" u2="&#x33;" k="20" />
<hkern u1="\" u2="&#x31;" k="123" />
<hkern u1="\" u2="&#x30;" k="82" />
<hkern u1="q" u2="&#x2122;" k="61" />
<hkern u1="q" u2="\" k="143" />
<hkern u1="q" u2="&#x2a;" k="20" />
<hkern u1="v" u2="&#xb7;" k="41" />
<hkern u1="v" u2="x" k="20" />
<hkern u1="v" u2="v" k="-20" />
<hkern u1="v" u2="\" k="102" />
<hkern u1="v" u2="&#x40;" k="20" />
<hkern u1="v" u2="&#x2f;" k="82" />
<hkern u1="v" u2="&#x2a;" k="-31" />
<hkern u1="v" u2="&#x29;" k="41" />
<hkern u1="v" u2="&#x26;" k="72" />
<hkern u1="x" u2="&#x2122;" k="61" />
<hkern u1="x" u2="&#xb7;" k="82" />
<hkern u1="x" u2="&#xae;" k="51" />
<hkern u1="x" u2="v" k="20" />
<hkern u1="x" u2="\" k="143" />
<hkern u1="x" u2="&#x40;" k="72" />
<hkern u1="x" u2="&#x3f;" k="20" />
<hkern u1="x" u2="&#x2a;" k="51" />
<hkern u1="x" u2="&#x29;" k="20" />
<hkern u1="x" u2="&#x26;" k="61" />
<hkern u1="&#xa1;" u2="V" k="61" />
<hkern u1="&#xa1;" u2="&#x37;" k="20" />
<hkern u1="&#xa3;" u2="&#x38;" k="20" />
<hkern u1="&#xa3;" u2="&#x37;" k="31" />
<hkern u1="&#xa3;" u2="&#x36;" k="20" />
<hkern u1="&#xa3;" u2="&#x34;" k="92" />
<hkern u1="&#xa5;" u2="&#x39;" k="31" />
<hkern u1="&#xa5;" u2="&#x38;" k="20" />
<hkern u1="&#xa5;" u2="&#x36;" k="41" />
<hkern u1="&#xa5;" u2="&#x35;" k="20" />
<hkern u1="&#xa5;" u2="&#x34;" k="20" />
<hkern u1="&#xa5;" u2="&#x33;" k="20" />
<hkern u1="&#xa5;" u2="&#x32;" k="20" />
<hkern u1="&#xa5;" u2="&#x31;" k="41" />
<hkern u1="&#xa5;" u2="&#x30;" k="20" />
<hkern u1="&#xa7;" u2="&#x37;" k="51" />
<hkern u1="&#xa7;" u2="&#x33;" k="-20" />
<hkern u1="&#xa7;" u2="&#x31;" k="41" />
<hkern u1="&#xb0;" u2="&#x39;" k="20" />
<hkern u1="&#xb0;" u2="&#x38;" k="41" />
<hkern u1="&#xb0;" u2="&#x36;" k="31" />
<hkern u1="&#xb0;" u2="&#x35;" k="41" />
<hkern u1="&#xb0;" u2="&#x34;" k="205" />
<hkern u1="&#xb0;" u2="&#x33;" k="31" />
<hkern u1="&#xb0;" u2="&#x32;" k="31" />
<hkern u1="&#xb0;" u2="&#x30;" k="61" />
<hkern u1="&#xb7;" u2="x" k="82" />
<hkern u1="&#xb7;" u2="v" k="41" />
<hkern u1="&#xb7;" u2="X" k="102" />
<hkern u1="&#xb7;" u2="V" k="102" />
<hkern u1="&#xb7;" u2="&#x37;" k="51" />
<hkern u1="&#xb7;" u2="&#x36;" k="-20" />
<hkern u1="&#xb7;" u2="&#x34;" k="-20" />
<hkern u1="&#xb7;" u2="&#x31;" k="82" />
<hkern u1="&#xbf;" u2="v" k="154" />
<hkern u1="&#xbf;" u2="V" k="195" />
<hkern u1="&#xbf;" u2="&#x39;" k="31" />
<hkern u1="&#xbf;" u2="&#x38;" k="20" />
<hkern u1="&#xbf;" u2="&#x37;" k="82" />
<hkern u1="&#xbf;" u2="&#x36;" k="51" />
<hkern u1="&#xbf;" u2="&#x35;" k="20" />
<hkern u1="&#xbf;" u2="&#x34;" k="61" />
<hkern u1="&#xbf;" u2="&#x33;" k="20" />
<hkern u1="&#xbf;" u2="&#x32;" k="-31" />
<hkern u1="&#xbf;" u2="&#x31;" k="123" />
<hkern u1="&#xbf;" u2="&#x30;" k="51" />
<hkern u1="&#xdf;" u2="&#x2122;" k="61" />
<hkern u1="&#xdf;" u2="x" k="31" />
<hkern u1="&#xdf;" u2="v" k="20" />
<hkern u1="&#xdf;" u2="\" k="92" />
<hkern u1="&#xdf;" u2="&#x3f;" k="31" />
<hkern u1="&#xdf;" u2="&#x2f;" k="20" />
<hkern u1="&#xdf;" u2="&#x2a;" k="41" />
<hkern u1="&#xdf;" u2="&#x29;" k="20" />
<hkern g1="bracketleft,braceleft" 	g2="eight" 	k="20" />
<hkern g1="bracketleft,braceleft" 	g2="four" 	k="41" />
<hkern g1="bracketleft,braceleft" 	g2="seven" 	k="-31" />
<hkern g1="colon,semicolon" 	g2="seven" 	k="41" />
<hkern g1="colon,semicolon" 	g2="five" 	k="-20" />
<hkern g1="colon,semicolon" 	g2="one" 	k="72" />
<hkern g1="colon,semicolon" 	g2="six" 	k="-20" />
<hkern g1="colon,semicolon" 	g2="two" 	k="-20" />
<hkern g1="comma,period,ellipsis" 	g2="four" 	k="51" />
<hkern g1="comma,period,ellipsis" 	g2="seven" 	k="20" />
<hkern g1="comma,period,ellipsis" 	g2="one" 	k="133" />
<hkern g1="comma,period,ellipsis" 	g2="six" 	k="51" />
<hkern g1="comma,period,ellipsis" 	g2="two" 	k="-31" />
<hkern g1="comma,period,ellipsis" 	g2="nine" 	k="31" />
<hkern g1="comma,period,ellipsis" 	g2="zero" 	k="41" />
<hkern g1="guilsinglleft" 	g2="eight" 	k="20" />
<hkern g1="guilsinglleft" 	g2="seven" 	k="92" />
<hkern g1="guilsinglleft" 	g2="one" 	k="61" />
<hkern g1="guilsinglleft" 	g2="two" 	k="20" />
<hkern g1="guilsinglright" 	g2="eight" 	k="20" />
<hkern g1="guilsinglright" 	g2="seven" 	k="113" />
<hkern g1="guilsinglright" 	g2="one" 	k="143" />
<hkern g1="guilsinglright" 	g2="two" 	k="61" />
<hkern g1="guilsinglright" 	g2="nine" 	k="51" />
<hkern g1="hyphen,endash,emdash" 	g2="seven" 	k="82" />
<hkern g1="hyphen,endash,emdash" 	g2="one" 	k="154" />
<hkern g1="hyphen,endash,emdash" 	g2="nine" 	k="41" />
<hkern g1="quotedbl,quotesingle" 	g2="eight" 	k="20" />
<hkern g1="quotedbl,quotesingle" 	g2="four" 	k="113" />
<hkern g1="quotedbl,quotesingle" 	g2="seven" 	k="-20" />
<hkern g1="quotedbl,quotesingle" 	g2="five" 	k="-20" />
<hkern g1="quotedbl,quotesingle" 	g2="one" 	k="-41" />
<hkern g1="quotedbl,quotesingle" 	g2="three" 	k="-20" />
<hkern g1="quoteleft,quotedblleft" 	g2="eight" 	k="31" />
<hkern g1="quoteleft,quotedblleft" 	g2="four" 	k="154" />
<hkern g1="quoteleft,quotedblleft" 	g2="seven" 	k="-20" />
<hkern g1="quoteleft,quotedblleft" 	g2="five" 	k="20" />
<hkern g1="quoteleft,quotedblleft" 	g2="one" 	k="-41" />
<hkern g1="quoteleft,quotedblleft" 	g2="six" 	k="31" />
<hkern g1="quoteleft,quotedblleft" 	g2="zero" 	k="31" />
<hkern g1="quoteright,quotedblright" 	g2="eight" 	k="41" />
<hkern g1="quoteright,quotedblright" 	g2="four" 	k="174" />
<hkern g1="quoteright,quotedblright" 	g2="seven" 	k="-31" />
<hkern g1="quoteright,quotedblright" 	g2="five" 	k="31" />
<hkern g1="quoteright,quotedblright" 	g2="one" 	k="-20" />
<hkern g1="quoteright,quotedblright" 	g2="six" 	k="51" />
<hkern g1="quoteright,quotedblright" 	g2="two" 	k="31" />
<hkern g1="quoteright,quotedblright" 	g2="nine" 	k="20" />
<hkern g1="quoteright,quotedblright" 	g2="zero" 	k="41" />
<hkern g1="quoteright,quotedblright" 	g2="three" 	k="20" />
<hkern g1="quotesinglbase,quotedblbase" 	g2="four" 	k="51" />
<hkern g1="quotesinglbase,quotedblbase" 	g2="seven" 	k="20" />
<hkern g1="quotesinglbase,quotedblbase" 	g2="one" 	k="133" />
<hkern g1="quotesinglbase,quotedblbase" 	g2="six" 	k="51" />
<hkern g1="quotesinglbase,quotedblbase" 	g2="two" 	k="-31" />
<hkern g1="quotesinglbase,quotedblbase" 	g2="nine" 	k="31" />
<hkern g1="quotesinglbase,quotedblbase" 	g2="zero" 	k="41" />
<hkern g1="copyright" 	g2="eight" 	k="20" />
<hkern g1="copyright" 	g2="nine" 	k="20" />
<hkern g1="copyright" 	g2="one" 	k="20" />
<hkern g1="copyright" 	g2="seven" 	k="61" />
<hkern g1="copyright" 	g2="two" 	k="20" />
<hkern g1="plus,divide" 	g2="eight" 	k="20" />
<hkern g1="plus,divide" 	g2="nine" 	k="20" />
<hkern g1="plus,divide" 	g2="one" 	k="61" />
<hkern g1="plus,divide" 	g2="seven" 	k="61" />
<hkern g1="plus,divide" 	g2="two" 	k="31" />
<hkern g1="plus,divide" 	g2="three" 	k="20" />
<hkern g1="eight" 	g2="bracketright,braceright" 	k="20" />
<hkern g1="eight" 	g2="copyright" 	k="20" />
<hkern g1="eight" 	g2="guilsinglleft" 	k="20" />
<hkern g1="eight" 	g2="guilsinglright" 	k="20" />
<hkern g1="eight" 	g2="percent" 	k="20" />
<hkern g1="eight" 	g2="plus,divide" 	k="20" />
<hkern g1="eight" 	g2="quotedbl,quotesingle" 	k="31" />
<hkern g1="eight" 	g2="quoteleft,quotedblleft" 	k="41" />
<hkern g1="eight" 	g2="quoteright,quotedblright" 	k="31" />
<hkern g1="five" 	g2="percent" 	k="20" />
<hkern g1="five" 	g2="quotedbl,quotesingle" 	k="20" />
<hkern g1="five" 	g2="quoteleft,quotedblleft" 	k="31" />
<hkern g1="five" 	g2="quoteright,quotedblright" 	k="20" />
<hkern g1="five" 	g2="colon,semicolon" 	k="-20" />
<hkern g1="four" 	g2="guilsinglleft" 	k="20" />
<hkern g1="four" 	g2="percent" 	k="61" />
<hkern g1="four" 	g2="plus,divide" 	k="20" />
<hkern g1="four" 	g2="quotedbl,quotesingle" 	k="61" />
<hkern g1="four" 	g2="quoteleft,quotedblleft" 	k="72" />
<hkern g1="four" 	g2="quoteright,quotedblright" 	k="51" />
<hkern g1="nine" 	g2="quotedbl,quotesingle" 	k="20" />
<hkern g1="nine" 	g2="quoteleft,quotedblleft" 	k="31" />
<hkern g1="nine" 	g2="quoteright,quotedblright" 	k="20" />
<hkern g1="nine" 	g2="colon,semicolon" 	k="-20" />
<hkern g1="nine" 	g2="comma,period,ellipsis" 	k="31" />
<hkern g1="nine" 	g2="quotesinglbase,quotedblbase" 	k="31" />
<hkern g1="one" 	g2="bracketright,braceright" 	k="-31" />
<hkern g1="seven" 	g2="bracketright,braceright" 	k="-20" />
<hkern g1="seven" 	g2="copyright" 	k="41" />
<hkern g1="seven" 	g2="guilsinglleft" 	k="184" />
<hkern g1="seven" 	g2="guilsinglright" 	k="102" />
<hkern g1="seven" 	g2="plus,divide" 	k="41" />
<hkern g1="seven" 	g2="quotedbl,quotesingle" 	k="-41" />
<hkern g1="seven" 	g2="quoteleft,quotedblleft" 	k="-31" />
<hkern g1="seven" 	g2="quoteright,quotedblright" 	k="-31" />
<hkern g1="seven" 	g2="colon,semicolon" 	k="61" />
<hkern g1="seven" 	g2="comma,period,ellipsis" 	k="102" />
<hkern g1="seven" 	g2="quotesinglbase,quotedblbase" 	k="102" />
<hkern g1="seven" 	g2="hyphen,endash,emdash" 	k="133" />
<hkern g1="six" 	g2="quotedbl,quotesingle" 	k="10" />
<hkern g1="six" 	g2="quoteleft,quotedblleft" 	k="20" />
<hkern g1="six" 	g2="quoteright,quotedblright" 	k="10" />
<hkern g1="six" 	g2="colon,semicolon" 	k="-20" />
<hkern g1="six" 	g2="comma,period,ellipsis" 	k="20" />
<hkern g1="six" 	g2="quotesinglbase,quotedblbase" 	k="20" />
<hkern g1="three" 	g2="percent" 	k="20" />
<hkern g1="three" 	g2="quotedbl,quotesingle" 	k="20" />
<hkern g1="three" 	g2="quoteleft,quotedblleft" 	k="20" />
<hkern g1="three" 	g2="quoteright,quotedblright" 	k="20" />
<hkern g1="two" 	g2="guilsinglleft" 	k="72" />
<hkern g1="two" 	g2="guilsinglright" 	k="-10" />
<hkern g1="two" 	g2="percent" 	k="-41" />
<hkern g1="two" 	g2="colon,semicolon" 	k="-20" />
<hkern g1="two" 	g2="comma,period,ellipsis" 	k="-20" />
<hkern g1="two" 	g2="quotesinglbase,quotedblbase" 	k="-20" />
<hkern g1="two" 	g2="hyphen,endash,emdash" 	k="20" />
<hkern g1="zero" 	g2="quotedbl,quotesingle" 	k="31" />
<hkern g1="zero" 	g2="quoteleft,quotedblleft" 	k="31" />
<hkern g1="zero" 	g2="quoteright,quotedblright" 	k="31" />
<hkern g1="zero" 	g2="comma,period,ellipsis" 	k="41" />
<hkern g1="zero" 	g2="quotesinglbase,quotedblbase" 	k="41" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="-20" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="82" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="S" 	k="20" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="T" 	k="164" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	k="51" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="V" 	k="174" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="W" 	k="133" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="Y,Yacute,Ydieresis" 	k="164" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="ampersand" 	k="20" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="asterisk" 	k="215" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="at" 	k="41" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="backslash" 	k="195" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="bracketright,braceright" 	k="20" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="41" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="colon,semicolon" 	k="-20" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="comma,period,ellipsis" 	k="-31" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="copyright" 	k="82" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="d,q" 	k="41" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="f" 	k="31" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="g" 	k="41" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="guilsinglleft" 	k="82" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="guilsinglright" 	k="20" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="hyphen,endash,emdash" 	k="51" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="periodcentered" 	k="61" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="question" 	k="113" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="quotedbl,quotesingle" 	k="174" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="quoteleft,quotedblleft" 	k="143" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="quoteright,quotedblright" 	k="143" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="quotesinglbase,quotedblbase" 	k="-31" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="registered" 	k="82" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="s" 	k="10" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="slash" 	k="-20" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="t" 	k="61" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="trademark" 	k="174" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="u,ugrave,uacute,ucircumflex,udieresis" 	k="31" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="v" 	k="102" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="w" 	k="102" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="y,yacute,ydieresis" 	k="92" />
<hkern g1="B" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="10" />
<hkern g1="B" 	g2="T" 	k="61" />
<hkern g1="B" 	g2="W" 	k="31" />
<hkern g1="B" 	g2="Y,Yacute,Ydieresis" 	k="51" />
<hkern g1="B" 	g2="bracketright,braceright" 	k="20" />
<hkern g1="B" 	g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="-10" />
<hkern g1="B" 	g2="d,q" 	k="-10" />
<hkern g1="B" 	g2="f" 	k="20" />
<hkern g1="B" 	g2="g" 	k="-10" />
<hkern g1="B" 	g2="hyphen,endash,emdash" 	k="-31" />
<hkern g1="B" 	g2="quotedbl,quotesingle" 	k="41" />
<hkern g1="B" 	g2="quoteleft,quotedblleft" 	k="31" />
<hkern g1="B" 	g2="quoteright,quotedblright" 	k="31" />
<hkern g1="B" 	g2="t" 	k="20" />
<hkern g1="B" 	g2="w" 	k="20" />
<hkern g1="B" 	g2="y,yacute,ydieresis" 	k="20" />
<hkern g1="B" 	g2="AE" 	k="20" />
<hkern g1="B" 	g2="J" 	k="-31" />
<hkern g1="B" 	g2="Z" 	k="10" />
<hkern g1="B" 	g2="z" 	k="31" />
<hkern g1="C,Ccedilla" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="41" />
<hkern g1="C,Ccedilla" 	g2="T" 	k="61" />
<hkern g1="C,Ccedilla" 	g2="V" 	k="41" />
<hkern g1="C,Ccedilla" 	g2="W" 	k="20" />
<hkern g1="C,Ccedilla" 	g2="Y,Yacute,Ydieresis" 	k="92" />
<hkern g1="C,Ccedilla" 	g2="ampersand" 	k="-20" />
<hkern g1="C,Ccedilla" 	g2="backslash" 	k="61" />
<hkern g1="C,Ccedilla" 	g2="comma,period,ellipsis" 	k="61" />
<hkern g1="C,Ccedilla" 	g2="hyphen,endash,emdash" 	k="-20" />
<hkern g1="C,Ccedilla" 	g2="quoteleft,quotedblleft" 	k="-10" />
<hkern g1="C,Ccedilla" 	g2="quotesinglbase,quotedblbase" 	k="61" />
<hkern g1="C,Ccedilla" 	g2="slash" 	k="51" />
<hkern g1="C,Ccedilla" 	g2="AE" 	k="51" />
<hkern g1="C,Ccedilla" 	g2="Z" 	k="51" />
<hkern g1="C,Ccedilla" 	g2="z" 	k="31" />
<hkern g1="C,Ccedilla" 	g2="X" 	k="82" />
<hkern g1="C,Ccedilla" 	g2="x" 	k="31" />
<hkern g1="D,Eth" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="82" />
<hkern g1="D,Eth" 	g2="T" 	k="113" />
<hkern g1="D,Eth" 	g2="V" 	k="82" />
<hkern g1="D,Eth" 	g2="W" 	k="61" />
<hkern g1="D,Eth" 	g2="Y,Yacute,Ydieresis" 	k="123" />
<hkern g1="D,Eth" 	g2="asterisk" 	k="41" />
<hkern g1="D,Eth" 	g2="backslash" 	k="102" />
<hkern g1="D,Eth" 	g2="bracketright,braceright" 	k="20" />
<hkern g1="D,Eth" 	g2="comma,period,ellipsis" 	k="92" />
<hkern g1="D,Eth" 	g2="quotedbl,quotesingle" 	k="61" />
<hkern g1="D,Eth" 	g2="quoteleft,quotedblleft" 	k="51" />
<hkern g1="D,Eth" 	g2="quoteright,quotedblright" 	k="51" />
<hkern g1="D,Eth" 	g2="quotesinglbase,quotedblbase" 	k="92" />
<hkern g1="D,Eth" 	g2="slash" 	k="102" />
<hkern g1="D,Eth" 	g2="trademark" 	k="61" />
<hkern g1="D,Eth" 	g2="v" 	k="10" />
<hkern g1="D,Eth" 	g2="w" 	k="10" />
<hkern g1="D,Eth" 	g2="AE" 	k="102" />
<hkern g1="D,Eth" 	g2="J" 	k="20" />
<hkern g1="D,Eth" 	g2="Z" 	k="82" />
<hkern g1="D,Eth" 	g2="z" 	k="41" />
<hkern g1="D,Eth" 	g2="X" 	k="102" />
<hkern g1="D,Eth" 	g2="x" 	k="51" />
<hkern g1="D,Eth" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="20" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE" 	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="41" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE" 	g2="S" 	k="20" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE" 	g2="ampersand" 	k="20" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE" 	g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="51" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE" 	g2="comma,period,ellipsis" 	k="-20" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE" 	g2="d,q" 	k="31" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE" 	g2="f" 	k="31" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE" 	g2="g" 	k="31" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE" 	g2="guilsinglleft" 	k="61" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE" 	g2="hyphen,endash,emdash" 	k="20" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE" 	g2="question" 	k="20" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE" 	g2="quotesinglbase,quotedblbase" 	k="-20" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE" 	g2="s" 	k="20" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE" 	g2="t" 	k="31" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE" 	g2="u,ugrave,uacute,ucircumflex,udieresis" 	k="20" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE" 	g2="v" 	k="51" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE" 	g2="w" 	k="51" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE" 	g2="y,yacute,ydieresis" 	k="51" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE" 	g2="J" 	k="20" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE" 	g2="z" 	k="10" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="20" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE" 	g2="germandbls" 	k="20" />
<hkern g1="F" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="154" />
<hkern g1="F" 	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="41" />
<hkern g1="F" 	g2="S" 	k="51" />
<hkern g1="F" 	g2="T" 	k="-20" />
<hkern g1="F" 	g2="W" 	k="-10" />
<hkern g1="F" 	g2="bracketright,braceright" 	k="-31" />
<hkern g1="F" 	g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="92" />
<hkern g1="F" 	g2="colon,semicolon" 	k="41" />
<hkern g1="F" 	g2="comma,period,ellipsis" 	k="133" />
<hkern g1="F" 	g2="copyright" 	k="20" />
<hkern g1="F" 	g2="d,q" 	k="82" />
<hkern g1="F" 	g2="f" 	k="31" />
<hkern g1="F" 	g2="g" 	k="82" />
<hkern g1="F" 	g2="guilsinglleft" 	k="92" />
<hkern g1="F" 	g2="guilsinglright" 	k="61" />
<hkern g1="F" 	g2="hyphen,endash,emdash" 	k="51" />
<hkern g1="F" 	g2="quotedbl,quotesingle" 	k="-20" />
<hkern g1="F" 	g2="quoteleft,quotedblleft" 	k="-20" />
<hkern g1="F" 	g2="quoteright,quotedblright" 	k="-20" />
<hkern g1="F" 	g2="quotesinglbase,quotedblbase" 	k="133" />
<hkern g1="F" 	g2="s" 	k="82" />
<hkern g1="F" 	g2="t" 	k="31" />
<hkern g1="F" 	g2="u,ugrave,uacute,ucircumflex,udieresis" 	k="72" />
<hkern g1="F" 	g2="w" 	k="82" />
<hkern g1="F" 	g2="y,yacute,ydieresis" 	k="72" />
<hkern g1="F" 	g2="AE" 	k="174" />
<hkern g1="F" 	g2="J" 	k="143" />
<hkern g1="F" 	g2="Z" 	k="20" />
<hkern g1="F" 	g2="z" 	k="82" />
<hkern g1="F" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="102" />
<hkern g1="F" 	g2="m,n,r,ntilde" 	k="61" />
<hkern g1="G" 	g2="T" 	k="102" />
<hkern g1="G" 	g2="V" 	k="82" />
<hkern g1="G" 	g2="W" 	k="72" />
<hkern g1="G" 	g2="Y,Yacute,Ydieresis" 	k="113" />
<hkern g1="G" 	g2="asterisk" 	k="31" />
<hkern g1="G" 	g2="backslash" 	k="92" />
<hkern g1="G" 	g2="question" 	k="20" />
<hkern g1="G" 	g2="quotedbl,quotesingle" 	k="41" />
<hkern g1="G" 	g2="quoteleft,quotedblleft" 	k="41" />
<hkern g1="G" 	g2="quoteright,quotedblright" 	k="31" />
<hkern g1="G" 	g2="t" 	k="10" />
<hkern g1="G" 	g2="trademark" 	k="61" />
<hkern g1="G" 	g2="v" 	k="20" />
<hkern g1="G" 	g2="w" 	k="20" />
<hkern g1="G" 	g2="y,yacute,ydieresis" 	k="20" />
<hkern g1="G" 	g2="Z" 	k="10" />
<hkern g1="G" 	g2="X" 	k="31" />
<hkern g1="J" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="31" />
<hkern g1="J" 	g2="comma,period,ellipsis" 	k="31" />
<hkern g1="J" 	g2="quotesinglbase,quotedblbase" 	k="31" />
<hkern g1="J" 	g2="slash" 	k="31" />
<hkern g1="J" 	g2="AE" 	k="31" />
<hkern g1="J" 	g2="z" 	k="20" />
<hkern g1="J" 	g2="x" 	k="20" />
<hkern g1="K" 	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="133" />
<hkern g1="K" 	g2="S" 	k="82" />
<hkern g1="K" 	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	k="41" />
<hkern g1="K" 	g2="ampersand" 	k="41" />
<hkern g1="K" 	g2="asterisk" 	k="61" />
<hkern g1="K" 	g2="at" 	k="61" />
<hkern g1="K" 	g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="92" />
<hkern g1="K" 	g2="comma,period,ellipsis" 	k="-20" />
<hkern g1="K" 	g2="copyright" 	k="92" />
<hkern g1="K" 	g2="d,q" 	k="92" />
<hkern g1="K" 	g2="f" 	k="51" />
<hkern g1="K" 	g2="g" 	k="92" />
<hkern g1="K" 	g2="guilsinglleft" 	k="164" />
<hkern g1="K" 	g2="guilsinglright" 	k="61" />
<hkern g1="K" 	g2="hyphen,endash,emdash" 	k="154" />
<hkern g1="K" 	g2="periodcentered" 	k="143" />
<hkern g1="K" 	g2="question" 	k="82" />
<hkern g1="K" 	g2="quoteleft,quotedblleft" 	k="31" />
<hkern g1="K" 	g2="quotesinglbase,quotedblbase" 	k="-20" />
<hkern g1="K" 	g2="registered" 	k="92" />
<hkern g1="K" 	g2="s" 	k="41" />
<hkern g1="K" 	g2="t" 	k="61" />
<hkern g1="K" 	g2="u,ugrave,uacute,ucircumflex,udieresis" 	k="51" />
<hkern g1="K" 	g2="v" 	k="102" />
<hkern g1="K" 	g2="w" 	k="102" />
<hkern g1="K" 	g2="y,yacute,ydieresis" 	k="102" />
<hkern g1="K" 	g2="J" 	k="41" />
<hkern g1="K" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="31" />
<hkern g1="K" 	g2="germandbls" 	k="20" />
<hkern g1="L" 	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="113" />
<hkern g1="L" 	g2="S" 	k="41" />
<hkern g1="L" 	g2="T" 	k="215" />
<hkern g1="L" 	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	k="51" />
<hkern g1="L" 	g2="V" 	k="184" />
<hkern g1="L" 	g2="W" 	k="154" />
<hkern g1="L" 	g2="Y,Yacute,Ydieresis" 	k="205" />
<hkern g1="L" 	g2="ampersand" 	k="31" />
<hkern g1="L" 	g2="asterisk" 	k="215" />
<hkern g1="L" 	g2="at" 	k="31" />
<hkern g1="L" 	g2="backslash" 	k="215" />
<hkern g1="L" 	g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="61" />
<hkern g1="L" 	g2="colon,semicolon" 	k="-20" />
<hkern g1="L" 	g2="comma,period,ellipsis" 	k="-31" />
<hkern g1="L" 	g2="copyright" 	k="102" />
<hkern g1="L" 	g2="d,q" 	k="61" />
<hkern g1="L" 	g2="f" 	k="72" />
<hkern g1="L" 	g2="g" 	k="41" />
<hkern g1="L" 	g2="guilsinglleft" 	k="133" />
<hkern g1="L" 	g2="guilsinglright" 	k="31" />
<hkern g1="L" 	g2="hyphen,endash,emdash" 	k="82" />
<hkern g1="L" 	g2="periodcentered" 	k="61" />
<hkern g1="L" 	g2="question" 	k="133" />
<hkern g1="L" 	g2="quotedbl,quotesingle" 	k="174" />
<hkern g1="L" 	g2="quoteleft,quotedblleft" 	k="133" />
<hkern g1="L" 	g2="quoteright,quotedblright" 	k="143" />
<hkern g1="L" 	g2="quotesinglbase,quotedblbase" 	k="-31" />
<hkern g1="L" 	g2="registered" 	k="102" />
<hkern g1="L" 	g2="s" 	k="20" />
<hkern g1="L" 	g2="slash" 	k="-20" />
<hkern g1="L" 	g2="t" 	k="61" />
<hkern g1="L" 	g2="trademark" 	k="154" />
<hkern g1="L" 	g2="u,ugrave,uacute,ucircumflex,udieresis" 	k="41" />
<hkern g1="L" 	g2="v" 	k="123" />
<hkern g1="L" 	g2="w" 	k="123" />
<hkern g1="L" 	g2="y,yacute,ydieresis" 	k="113" />
<hkern g1="L" 	g2="AE" 	k="-20" />
<hkern g1="L" 	g2="J" 	k="20" />
<hkern g1="L" 	g2="Z" 	k="-31" />
<hkern g1="L" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="20" />
<hkern g1="O,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="82" />
<hkern g1="O,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="T" 	k="113" />
<hkern g1="O,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="V" 	k="82" />
<hkern g1="O,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="W" 	k="61" />
<hkern g1="O,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="Y,Yacute,Ydieresis" 	k="123" />
<hkern g1="O,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="asterisk" 	k="41" />
<hkern g1="O,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="backslash" 	k="102" />
<hkern g1="O,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="bracketright,braceright" 	k="20" />
<hkern g1="O,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="comma,period,ellipsis" 	k="92" />
<hkern g1="O,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="quotedbl,quotesingle" 	k="61" />
<hkern g1="O,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="quoteleft,quotedblleft" 	k="51" />
<hkern g1="O,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="quoteright,quotedblright" 	k="51" />
<hkern g1="O,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="quotesinglbase,quotedblbase" 	k="92" />
<hkern g1="O,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="slash" 	k="102" />
<hkern g1="O,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="trademark" 	k="61" />
<hkern g1="O,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="v" 	k="10" />
<hkern g1="O,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="w" 	k="10" />
<hkern g1="O,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="AE" 	k="102" />
<hkern g1="O,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="J" 	k="20" />
<hkern g1="O,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="Z" 	k="82" />
<hkern g1="O,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="z" 	k="41" />
<hkern g1="O,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="X" 	k="102" />
<hkern g1="O,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="x" 	k="51" />
<hkern g1="O,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="20" />
<hkern g1="P" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="143" />
<hkern g1="P" 	g2="T" 	k="41" />
<hkern g1="P" 	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	k="-10" />
<hkern g1="P" 	g2="W" 	k="-10" />
<hkern g1="P" 	g2="Y,Yacute,Ydieresis" 	k="20" />
<hkern g1="P" 	g2="bracketright,braceright" 	k="20" />
<hkern g1="P" 	g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="61" />
<hkern g1="P" 	g2="colon,semicolon" 	k="20" />
<hkern g1="P" 	g2="comma,period,ellipsis" 	k="164" />
<hkern g1="P" 	g2="copyright" 	k="-20" />
<hkern g1="P" 	g2="d,q" 	k="61" />
<hkern g1="P" 	g2="f" 	k="-31" />
<hkern g1="P" 	g2="g" 	k="61" />
<hkern g1="P" 	g2="guilsinglleft" 	k="92" />
<hkern g1="P" 	g2="guilsinglright" 	k="31" />
<hkern g1="P" 	g2="hyphen,endash,emdash" 	k="61" />
<hkern g1="P" 	g2="quotedbl,quotesingle" 	k="-20" />
<hkern g1="P" 	g2="quoteleft,quotedblleft" 	k="-20" />
<hkern g1="P" 	g2="quoteright,quotedblright" 	k="-20" />
<hkern g1="P" 	g2="quotesinglbase,quotedblbase" 	k="164" />
<hkern g1="P" 	g2="s" 	k="20" />
<hkern g1="P" 	g2="t" 	k="-20" />
<hkern g1="P" 	g2="w" 	k="-31" />
<hkern g1="P" 	g2="y,yacute,ydieresis" 	k="-31" />
<hkern g1="P" 	g2="AE" 	k="174" />
<hkern g1="P" 	g2="J" 	k="123" />
<hkern g1="P" 	g2="Z" 	k="61" />
<hkern g1="P" 	g2="z" 	k="20" />
<hkern g1="P" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="51" />
<hkern g1="Q" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="41" />
<hkern g1="Q" 	g2="T" 	k="113" />
<hkern g1="Q" 	g2="W" 	k="61" />
<hkern g1="Q" 	g2="Y,Yacute,Ydieresis" 	k="123" />
<hkern g1="Q" 	g2="bracketright,braceright" 	k="20" />
<hkern g1="Q" 	g2="comma,period,ellipsis" 	k="92" />
<hkern g1="Q" 	g2="quotedbl,quotesingle" 	k="61" />
<hkern g1="Q" 	g2="quoteleft,quotedblleft" 	k="51" />
<hkern g1="Q" 	g2="quoteright,quotedblright" 	k="51" />
<hkern g1="Q" 	g2="quotesinglbase,quotedblbase" 	k="92" />
<hkern g1="Q" 	g2="w" 	k="10" />
<hkern g1="Q" 	g2="AE" 	k="20" />
<hkern g1="Q" 	g2="J" 	k="20" />
<hkern g1="Q" 	g2="z" 	k="41" />
<hkern g1="Q" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="20" />
<hkern g1="R" 	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="10" />
<hkern g1="R" 	g2="T" 	k="31" />
<hkern g1="R" 	g2="V" 	k="20" />
<hkern g1="R" 	g2="Y,Yacute,Ydieresis" 	k="41" />
<hkern g1="R" 	g2="backslash" 	k="41" />
<hkern g1="R" 	g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="20" />
<hkern g1="R" 	g2="comma,period,ellipsis" 	k="-10" />
<hkern g1="R" 	g2="d,q" 	k="20" />
<hkern g1="R" 	g2="g" 	k="20" />
<hkern g1="R" 	g2="guilsinglleft" 	k="61" />
<hkern g1="R" 	g2="guilsinglright" 	k="20" />
<hkern g1="R" 	g2="hyphen,endash,emdash" 	k="20" />
<hkern g1="R" 	g2="quotedbl,quotesingle" 	k="20" />
<hkern g1="R" 	g2="quoteleft,quotedblleft" 	k="20" />
<hkern g1="R" 	g2="quoteright,quotedblright" 	k="20" />
<hkern g1="R" 	g2="quotesinglbase,quotedblbase" 	k="-10" />
<hkern g1="R" 	g2="s" 	k="10" />
<hkern g1="R" 	g2="trademark" 	k="20" />
<hkern g1="R" 	g2="Z" 	k="-20" />
<hkern g1="R" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="10" />
<hkern g1="S" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="20" />
<hkern g1="S" 	g2="S" 	k="-10" />
<hkern g1="S" 	g2="T" 	k="82" />
<hkern g1="S" 	g2="V" 	k="51" />
<hkern g1="S" 	g2="W" 	k="41" />
<hkern g1="S" 	g2="Y,Yacute,Ydieresis" 	k="92" />
<hkern g1="S" 	g2="asterisk" 	k="20" />
<hkern g1="S" 	g2="backslash" 	k="61" />
<hkern g1="S" 	g2="bracketright,braceright" 	k="20" />
<hkern g1="S" 	g2="comma,period,ellipsis" 	k="31" />
<hkern g1="S" 	g2="f" 	k="20" />
<hkern g1="S" 	g2="hyphen,endash,emdash" 	k="-20" />
<hkern g1="S" 	g2="question" 	k="20" />
<hkern g1="S" 	g2="quotedbl,quotesingle" 	k="41" />
<hkern g1="S" 	g2="quoteleft,quotedblleft" 	k="41" />
<hkern g1="S" 	g2="quoteright,quotedblright" 	k="31" />
<hkern g1="S" 	g2="quotesinglbase,quotedblbase" 	k="31" />
<hkern g1="S" 	g2="slash" 	k="20" />
<hkern g1="S" 	g2="t" 	k="20" />
<hkern g1="S" 	g2="trademark" 	k="61" />
<hkern g1="S" 	g2="v" 	k="41" />
<hkern g1="S" 	g2="w" 	k="41" />
<hkern g1="S" 	g2="y,yacute,ydieresis" 	k="31" />
<hkern g1="S" 	g2="AE" 	k="20" />
<hkern g1="S" 	g2="Z" 	k="20" />
<hkern g1="S" 	g2="z" 	k="41" />
<hkern g1="S" 	g2="X" 	k="51" />
<hkern g1="S" 	g2="x" 	k="41" />
<hkern g1="S" 	g2="parenright" 	k="10" />
<hkern g1="T" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="164" />
<hkern g1="T" 	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="113" />
<hkern g1="T" 	g2="S" 	k="51" />
<hkern g1="T" 	g2="T" 	k="-31" />
<hkern g1="T" 	g2="V" 	k="-20" />
<hkern g1="T" 	g2="W" 	k="-20" />
<hkern g1="T" 	g2="Y,Yacute,Ydieresis" 	k="-20" />
<hkern g1="T" 	g2="ampersand" 	k="123" />
<hkern g1="T" 	g2="at" 	k="154" />
<hkern g1="T" 	g2="backslash" 	k="-20" />
<hkern g1="T" 	g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="246" />
<hkern g1="T" 	g2="colon,semicolon" 	k="164" />
<hkern g1="T" 	g2="comma,period,ellipsis" 	k="133" />
<hkern g1="T" 	g2="copyright" 	k="113" />
<hkern g1="T" 	g2="d,q" 	k="225" />
<hkern g1="T" 	g2="f" 	k="82" />
<hkern g1="T" 	g2="g" 	k="225" />
<hkern g1="T" 	g2="guilsinglleft" 	k="276" />
<hkern g1="T" 	g2="guilsinglright" 	k="174" />
<hkern g1="T" 	g2="hyphen,endash,emdash" 	k="195" />
<hkern g1="T" 	g2="periodcentered" 	k="174" />
<hkern g1="T" 	g2="question" 	k="31" />
<hkern g1="T" 	g2="quotedbl,quotesingle" 	k="-20" />
<hkern g1="T" 	g2="quoteleft,quotedblleft" 	k="-20" />
<hkern g1="T" 	g2="quoteright,quotedblright" 	k="-20" />
<hkern g1="T" 	g2="quotesinglbase,quotedblbase" 	k="164" />
<hkern g1="T" 	g2="registered" 	k="113" />
<hkern g1="T" 	g2="s" 	k="205" />
<hkern g1="T" 	g2="slash" 	k="174" />
<hkern g1="T" 	g2="t" 	k="61" />
<hkern g1="T" 	g2="u,ugrave,uacute,ucircumflex,udieresis" 	k="174" />
<hkern g1="T" 	g2="v" 	k="102" />
<hkern g1="T" 	g2="w" 	k="102" />
<hkern g1="T" 	g2="y,yacute,ydieresis" 	k="102" />
<hkern g1="T" 	g2="AE" 	k="174" />
<hkern g1="T" 	g2="J" 	k="174" />
<hkern g1="T" 	g2="z" 	k="174" />
<hkern g1="T" 	g2="x" 	k="154" />
<hkern g1="T" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="246" />
<hkern g1="T" 	g2="germandbls" 	k="61" />
<hkern g1="T" 	g2="m,n,r,ntilde" 	k="154" />
<hkern g1="T" 	g2="b,h,i,k,l,igrave,iacute,icircumflex,idieresis" 	k="20" />
<hkern g1="T" 	g2="j" 	k="20" />
<hkern g1="T" 	g2="p" 	k="154" />
<hkern g1="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="51" />
<hkern g1="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	g2="V" 	k="-20" />
<hkern g1="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	g2="comma,period,ellipsis" 	k="61" />
<hkern g1="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	g2="quotesinglbase,quotedblbase" 	k="61" />
<hkern g1="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	g2="slash" 	k="61" />
<hkern g1="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	g2="AE" 	k="41" />
<hkern g1="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	g2="Z" 	k="20" />
<hkern g1="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	g2="z" 	k="31" />
<hkern g1="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	g2="X" 	k="20" />
<hkern g1="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	g2="x" 	k="20" />
<hkern g1="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="20" />
<hkern g1="V" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="174" />
<hkern g1="V" 	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="82" />
<hkern g1="V" 	g2="S" 	k="31" />
<hkern g1="V" 	g2="T" 	k="-20" />
<hkern g1="V" 	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	k="-20" />
<hkern g1="V" 	g2="W" 	k="-31" />
<hkern g1="V" 	g2="Y,Yacute,Ydieresis" 	k="-20" />
<hkern g1="V" 	g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="133" />
<hkern g1="V" 	g2="colon,semicolon" 	k="82" />
<hkern g1="V" 	g2="comma,period,ellipsis" 	k="123" />
<hkern g1="V" 	g2="copyright" 	k="72" />
<hkern g1="V" 	g2="d,q" 	k="123" />
<hkern g1="V" 	g2="f" 	k="31" />
<hkern g1="V" 	g2="g" 	k="123" />
<hkern g1="V" 	g2="guilsinglleft" 	k="154" />
<hkern g1="V" 	g2="guilsinglright" 	k="102" />
<hkern g1="V" 	g2="hyphen,endash,emdash" 	k="154" />
<hkern g1="V" 	g2="quotedbl,quotesingle" 	k="-20" />
<hkern g1="V" 	g2="quoteright,quotedblright" 	k="-20" />
<hkern g1="V" 	g2="quotesinglbase,quotedblbase" 	k="143" />
<hkern g1="V" 	g2="s" 	k="92" />
<hkern g1="V" 	g2="t" 	k="31" />
<hkern g1="V" 	g2="u,ugrave,uacute,ucircumflex,udieresis" 	k="61" />
<hkern g1="V" 	g2="w" 	k="41" />
<hkern g1="V" 	g2="y,yacute,ydieresis" 	k="41" />
<hkern g1="V" 	g2="AE" 	k="174" />
<hkern g1="V" 	g2="J" 	k="154" />
<hkern g1="V" 	g2="Z" 	k="20" />
<hkern g1="V" 	g2="z" 	k="92" />
<hkern g1="V" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="143" />
<hkern g1="V" 	g2="m,n,r,ntilde" 	k="51" />
<hkern g1="W" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="133" />
<hkern g1="W" 	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="61" />
<hkern g1="W" 	g2="S" 	k="20" />
<hkern g1="W" 	g2="T" 	k="-20" />
<hkern g1="W" 	g2="V" 	k="-31" />
<hkern g1="W" 	g2="W" 	k="-20" />
<hkern g1="W" 	g2="Y,Yacute,Ydieresis" 	k="-20" />
<hkern g1="W" 	g2="ampersand" 	k="82" />
<hkern g1="W" 	g2="at" 	k="72" />
<hkern g1="W" 	g2="backslash" 	k="-20" />
<hkern g1="W" 	g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="102" />
<hkern g1="W" 	g2="colon,semicolon" 	k="51" />
<hkern g1="W" 	g2="comma,period,ellipsis" 	k="113" />
<hkern g1="W" 	g2="copyright" 	k="61" />
<hkern g1="W" 	g2="d,q" 	k="102" />
<hkern g1="W" 	g2="f" 	k="20" />
<hkern g1="W" 	g2="g" 	k="102" />
<hkern g1="W" 	g2="guilsinglleft" 	k="143" />
<hkern g1="W" 	g2="guilsinglright" 	k="82" />
<hkern g1="W" 	g2="hyphen,endash,emdash" 	k="82" />
<hkern g1="W" 	g2="periodcentered" 	k="61" />
<hkern g1="W" 	g2="quotedbl,quotesingle" 	k="-20" />
<hkern g1="W" 	g2="quoteleft,quotedblleft" 	k="-10" />
<hkern g1="W" 	g2="quoteright,quotedblright" 	k="-20" />
<hkern g1="W" 	g2="quotesinglbase,quotedblbase" 	k="113" />
<hkern g1="W" 	g2="registered" 	k="61" />
<hkern g1="W" 	g2="s" 	k="72" />
<hkern g1="W" 	g2="slash" 	k="143" />
<hkern g1="W" 	g2="t" 	k="20" />
<hkern g1="W" 	g2="u,ugrave,uacute,ucircumflex,udieresis" 	k="61" />
<hkern g1="W" 	g2="v" 	k="20" />
<hkern g1="W" 	g2="w" 	k="20" />
<hkern g1="W" 	g2="AE" 	k="154" />
<hkern g1="W" 	g2="J" 	k="123" />
<hkern g1="W" 	g2="z" 	k="72" />
<hkern g1="W" 	g2="x" 	k="61" />
<hkern g1="W" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="92" />
<hkern g1="W" 	g2="germandbls" 	k="20" />
<hkern g1="W" 	g2="m,n,r,ntilde" 	k="51" />
<hkern g1="W" 	g2="p" 	k="51" />
<hkern g1="X" 	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="102" />
<hkern g1="X" 	g2="S" 	k="51" />
<hkern g1="X" 	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	k="20" />
<hkern g1="X" 	g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="72" />
<hkern g1="X" 	g2="copyright" 	k="102" />
<hkern g1="X" 	g2="d,q" 	k="61" />
<hkern g1="X" 	g2="f" 	k="20" />
<hkern g1="X" 	g2="g" 	k="61" />
<hkern g1="X" 	g2="guilsinglleft" 	k="133" />
<hkern g1="X" 	g2="guilsinglright" 	k="72" />
<hkern g1="X" 	g2="hyphen,endash,emdash" 	k="123" />
<hkern g1="X" 	g2="quotedbl,quotesingle" 	k="10" />
<hkern g1="X" 	g2="quoteleft,quotedblleft" 	k="31" />
<hkern g1="X" 	g2="quoteright,quotedblright" 	k="10" />
<hkern g1="X" 	g2="s" 	k="41" />
<hkern g1="X" 	g2="t" 	k="41" />
<hkern g1="X" 	g2="u,ugrave,uacute,ucircumflex,udieresis" 	k="41" />
<hkern g1="X" 	g2="w" 	k="72" />
<hkern g1="X" 	g2="J" 	k="20" />
<hkern g1="X" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="20" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="164" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="123" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="S" 	k="72" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="T" 	k="-20" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="V" 	k="-20" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="W" 	k="-20" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="Y,Yacute,Ydieresis" 	k="-20" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="ampersand" 	k="154" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="asterisk" 	k="41" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="at" 	k="174" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="backslash" 	k="-20" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="205" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="colon,semicolon" 	k="113" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="comma,period,ellipsis" 	k="154" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="copyright" 	k="133" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="d,q" 	k="195" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="f" 	k="72" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="g" 	k="195" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="guilsinglleft" 	k="236" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="guilsinglright" 	k="164" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="hyphen,endash,emdash" 	k="215" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="periodcentered" 	k="154" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="question" 	k="41" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="quotedbl,quotesingle" 	k="-20" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="quoteleft,quotedblleft" 	k="10" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="quoteright,quotedblright" 	k="-20" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="quotesinglbase,quotedblbase" 	k="174" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="registered" 	k="133" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="s" 	k="174" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="slash" 	k="195" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="t" 	k="72" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="trademark" 	k="-20" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="u,ugrave,uacute,ucircumflex,udieresis" 	k="113" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="v" 	k="113" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="w" 	k="113" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="y,yacute,ydieresis" 	k="92" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="AE" 	k="184" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="J" 	k="195" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="Z" 	k="20" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="z" 	k="133" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="x" 	k="123" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="184" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="germandbls" 	k="72" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="m,n,r,ntilde" 	k="102" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="b,h,i,k,l,igrave,iacute,icircumflex,idieresis" 	k="20" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="j" 	k="20" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="p" 	k="102" />
<hkern g1="Z" 	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="102" />
<hkern g1="Z" 	g2="S" 	k="31" />
<hkern g1="Z" 	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	k="20" />
<hkern g1="Z" 	g2="V" 	k="20" />
<hkern g1="Z" 	g2="Y,Yacute,Ydieresis" 	k="20" />
<hkern g1="Z" 	g2="ampersand" 	k="51" />
<hkern g1="Z" 	g2="asterisk" 	k="20" />
<hkern g1="Z" 	g2="at" 	k="61" />
<hkern g1="Z" 	g2="backslash" 	k="20" />
<hkern g1="Z" 	g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="72" />
<hkern g1="Z" 	g2="colon,semicolon" 	k="-20" />
<hkern g1="Z" 	g2="comma,period,ellipsis" 	k="-20" />
<hkern g1="Z" 	g2="copyright" 	k="92" />
<hkern g1="Z" 	g2="d,q" 	k="72" />
<hkern g1="Z" 	g2="f" 	k="41" />
<hkern g1="Z" 	g2="g" 	k="72" />
<hkern g1="Z" 	g2="guilsinglleft" 	k="113" />
<hkern g1="Z" 	g2="guilsinglright" 	k="20" />
<hkern g1="Z" 	g2="hyphen,endash,emdash" 	k="102" />
<hkern g1="Z" 	g2="periodcentered" 	k="61" />
<hkern g1="Z" 	g2="question" 	k="41" />
<hkern g1="Z" 	g2="quotesinglbase,quotedblbase" 	k="-20" />
<hkern g1="Z" 	g2="registered" 	k="92" />
<hkern g1="Z" 	g2="s" 	k="20" />
<hkern g1="Z" 	g2="t" 	k="51" />
<hkern g1="Z" 	g2="u,ugrave,uacute,ucircumflex,udieresis" 	k="41" />
<hkern g1="Z" 	g2="v" 	k="61" />
<hkern g1="Z" 	g2="w" 	k="61" />
<hkern g1="Z" 	g2="y,yacute,ydieresis" 	k="72" />
<hkern g1="Z" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="20" />
<hkern g1="Z" 	g2="germandbls" 	k="20" />
<hkern g1="a,agrave,aacute,acircumflex,atilde,adieresis,aring" 	g2="asterisk" 	k="102" />
<hkern g1="a,agrave,aacute,acircumflex,atilde,adieresis,aring" 	g2="backslash" 	k="174" />
<hkern g1="a,agrave,aacute,acircumflex,atilde,adieresis,aring" 	g2="bracketright,braceright" 	k="31" />
<hkern g1="a,agrave,aacute,acircumflex,atilde,adieresis,aring" 	g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="20" />
<hkern g1="a,agrave,aacute,acircumflex,atilde,adieresis,aring" 	g2="colon,semicolon" 	k="-20" />
<hkern g1="a,agrave,aacute,acircumflex,atilde,adieresis,aring" 	g2="comma,period,ellipsis" 	k="-31" />
<hkern g1="a,agrave,aacute,acircumflex,atilde,adieresis,aring" 	g2="d,q" 	k="20" />
<hkern g1="a,agrave,aacute,acircumflex,atilde,adieresis,aring" 	g2="f" 	k="31" />
<hkern g1="a,agrave,aacute,acircumflex,atilde,adieresis,aring" 	g2="g" 	k="20" />
<hkern g1="a,agrave,aacute,acircumflex,atilde,adieresis,aring" 	g2="guilsinglleft" 	k="20" />
<hkern g1="a,agrave,aacute,acircumflex,atilde,adieresis,aring" 	g2="guilsinglright" 	k="20" />
<hkern g1="a,agrave,aacute,acircumflex,atilde,adieresis,aring" 	g2="parenright" 	k="20" />
<hkern g1="a,agrave,aacute,acircumflex,atilde,adieresis,aring" 	g2="question" 	k="72" />
<hkern g1="a,agrave,aacute,acircumflex,atilde,adieresis,aring" 	g2="quotedbl,quotesingle" 	k="92" />
<hkern g1="a,agrave,aacute,acircumflex,atilde,adieresis,aring" 	g2="quoteleft,quotedblleft" 	k="82" />
<hkern g1="a,agrave,aacute,acircumflex,atilde,adieresis,aring" 	g2="quoteright,quotedblright" 	k="72" />
<hkern g1="a,agrave,aacute,acircumflex,atilde,adieresis,aring" 	g2="quotesinglbase,quotedblbase" 	k="-31" />
<hkern g1="a,agrave,aacute,acircumflex,atilde,adieresis,aring" 	g2="s" 	k="10" />
<hkern g1="a,agrave,aacute,acircumflex,atilde,adieresis,aring" 	g2="slash" 	k="-20" />
<hkern g1="a,agrave,aacute,acircumflex,atilde,adieresis,aring" 	g2="t" 	k="41" />
<hkern g1="a,agrave,aacute,acircumflex,atilde,adieresis,aring" 	g2="trademark" 	k="133" />
<hkern g1="a,agrave,aacute,acircumflex,atilde,adieresis,aring" 	g2="v" 	k="51" />
<hkern g1="a,agrave,aacute,acircumflex,atilde,adieresis,aring" 	g2="w" 	k="51" />
<hkern g1="a,agrave,aacute,acircumflex,atilde,adieresis,aring" 	g2="y,yacute,ydieresis" 	k="51" />
<hkern g1="b,p,thorn" 	g2="asterisk" 	k="102" />
<hkern g1="b,p,thorn" 	g2="backslash" 	k="174" />
<hkern g1="b,p,thorn" 	g2="bracketright,braceright" 	k="20" />
<hkern g1="b,p,thorn" 	g2="comma,period,ellipsis" 	k="41" />
<hkern g1="b,p,thorn" 	g2="f" 	k="20" />
<hkern g1="b,p,thorn" 	g2="parenright" 	k="20" />
<hkern g1="b,p,thorn" 	g2="question" 	k="61" />
<hkern g1="b,p,thorn" 	g2="quotedbl,quotesingle" 	k="113" />
<hkern g1="b,p,thorn" 	g2="quoteleft,quotedblleft" 	k="92" />
<hkern g1="b,p,thorn" 	g2="quoteright,quotedblright" 	k="92" />
<hkern g1="b,p,thorn" 	g2="quotesinglbase,quotedblbase" 	k="41" />
<hkern g1="b,p,thorn" 	g2="slash" 	k="31" />
<hkern g1="b,p,thorn" 	g2="t" 	k="31" />
<hkern g1="b,p,thorn" 	g2="trademark" 	k="102" />
<hkern g1="b,p,thorn" 	g2="v" 	k="41" />
<hkern g1="b,p,thorn" 	g2="w" 	k="41" />
<hkern g1="b,p,thorn" 	g2="y,yacute,ydieresis" 	k="31" />
<hkern g1="b,p,thorn" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="10" />
<hkern g1="b,p,thorn" 	g2="x" 	k="61" />
<hkern g1="b,p,thorn" 	g2="z" 	k="51" />
<hkern g1="c,ccedilla" 	g2="asterisk" 	k="51" />
<hkern g1="c,ccedilla" 	g2="backslash" 	k="154" />
<hkern g1="c,ccedilla" 	g2="f" 	k="10" />
<hkern g1="c,ccedilla" 	g2="question" 	k="41" />
<hkern g1="c,ccedilla" 	g2="quotedbl,quotesingle" 	k="51" />
<hkern g1="c,ccedilla" 	g2="quoteleft,quotedblleft" 	k="41" />
<hkern g1="c,ccedilla" 	g2="quoteright,quotedblright" 	k="41" />
<hkern g1="c,ccedilla" 	g2="s" 	k="-20" />
<hkern g1="c,ccedilla" 	g2="trademark" 	k="72" />
<hkern g1="c,ccedilla" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="-20" />
<hkern g1="c,ccedilla" 	g2="x" 	k="31" />
<hkern g1="c,ccedilla" 	g2="z" 	k="31" />
<hkern g1="c,ccedilla" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="20" />
<hkern g1="c,ccedilla" 	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="-10" />
<hkern g1="c,ccedilla" 	g2="J" 	k="-20" />
<hkern g1="c,ccedilla" 	g2="S" 	k="-10" />
<hkern g1="c,ccedilla" 	g2="T" 	k="195" />
<hkern g1="c,ccedilla" 	g2="V" 	k="102" />
<hkern g1="c,ccedilla" 	g2="W" 	k="61" />
<hkern g1="c,ccedilla" 	g2="X" 	k="31" />
<hkern g1="c,ccedilla" 	g2="Y,Yacute,Ydieresis" 	k="154" />
<hkern g1="c,ccedilla" 	g2="ampersand" 	k="-20" />
<hkern g1="c,ccedilla" 	g2="at" 	k="-20" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe" 	g2="asterisk" 	k="102" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe" 	g2="backslash" 	k="154" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe" 	g2="comma,period,ellipsis" 	k="20" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe" 	g2="f" 	k="20" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe" 	g2="question" 	k="72" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe" 	g2="quotedbl,quotesingle" 	k="92" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe" 	g2="quoteleft,quotedblleft" 	k="82" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe" 	g2="quoteright,quotedblright" 	k="72" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe" 	g2="quotesinglbase,quotedblbase" 	k="20" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe" 	g2="slash" 	k="20" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe" 	g2="t" 	k="31" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe" 	g2="trademark" 	k="102" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe" 	g2="v" 	k="31" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe" 	g2="w" 	k="31" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe" 	g2="y,yacute,ydieresis" 	k="31" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe" 	g2="x" 	k="61" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe" 	g2="z" 	k="51" />
<hkern g1="f" 	g2="asterisk" 	k="-20" />
<hkern g1="f" 	g2="bracketright,braceright" 	k="-20" />
<hkern g1="f" 	g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="61" />
<hkern g1="f" 	g2="colon,semicolon" 	k="51" />
<hkern g1="f" 	g2="comma,period,ellipsis" 	k="82" />
<hkern g1="f" 	g2="d,q" 	k="51" />
<hkern g1="f" 	g2="f" 	k="20" />
<hkern g1="f" 	g2="g" 	k="51" />
<hkern g1="f" 	g2="guilsinglleft" 	k="92" />
<hkern g1="f" 	g2="guilsinglright" 	k="20" />
<hkern g1="f" 	g2="parenright" 	k="-20" />
<hkern g1="f" 	g2="question" 	k="-20" />
<hkern g1="f" 	g2="quotedbl,quotesingle" 	k="-31" />
<hkern g1="f" 	g2="quoteleft,quotedblleft" 	k="-20" />
<hkern g1="f" 	g2="quoteright,quotedblright" 	k="-31" />
<hkern g1="f" 	g2="quotesinglbase,quotedblbase" 	k="82" />
<hkern g1="f" 	g2="s" 	k="20" />
<hkern g1="f" 	g2="slash" 	k="102" />
<hkern g1="f" 	g2="t" 	k="20" />
<hkern g1="f" 	g2="trademark" 	k="-41" />
<hkern g1="f" 	g2="v" 	k="-20" />
<hkern g1="f" 	g2="w" 	k="-20" />
<hkern g1="f" 	g2="y,yacute,ydieresis" 	k="-20" />
<hkern g1="f" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="51" />
<hkern g1="f" 	g2="x" 	k="20" />
<hkern g1="f" 	g2="z" 	k="31" />
<hkern g1="f" 	g2="ampersand" 	k="72" />
<hkern g1="f" 	g2="hyphen,endash,emdash" 	k="61" />
<hkern g1="f" 	g2="periodcentered" 	k="61" />
<hkern g1="g" 	g2="asterisk" 	k="20" />
<hkern g1="g" 	g2="backslash" 	k="143" />
<hkern g1="g" 	g2="trademark" 	k="61" />
<hkern g1="g" 	g2="j" 	k="-20" />
<hkern g1="germandbls" 	g2="bracketright,braceright" 	k="20" />
<hkern g1="germandbls" 	g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="-20" />
<hkern g1="germandbls" 	g2="f" 	k="20" />
<hkern g1="germandbls" 	g2="quotedbl,quotesingle" 	k="51" />
<hkern g1="germandbls" 	g2="quoteleft,quotedblleft" 	k="31" />
<hkern g1="germandbls" 	g2="quoteright,quotedblright" 	k="41" />
<hkern g1="germandbls" 	g2="t" 	k="31" />
<hkern g1="germandbls" 	g2="w" 	k="20" />
<hkern g1="germandbls" 	g2="y,yacute,ydieresis" 	k="20" />
<hkern g1="germandbls" 	g2="z" 	k="20" />
<hkern g1="germandbls" 	g2="hyphen,endash,emdash" 	k="-20" />
<hkern g1="h,m,n,ntilde" 	g2="asterisk" 	k="72" />
<hkern g1="h,m,n,ntilde" 	g2="backslash" 	k="154" />
<hkern g1="h,m,n,ntilde" 	g2="f" 	k="10" />
<hkern g1="h,m,n,ntilde" 	g2="question" 	k="51" />
<hkern g1="h,m,n,ntilde" 	g2="quotedbl,quotesingle" 	k="72" />
<hkern g1="h,m,n,ntilde" 	g2="quoteleft,quotedblleft" 	k="51" />
<hkern g1="h,m,n,ntilde" 	g2="quoteright,quotedblright" 	k="61" />
<hkern g1="h,m,n,ntilde" 	g2="t" 	k="20" />
<hkern g1="h,m,n,ntilde" 	g2="trademark" 	k="92" />
<hkern g1="h,m,n,ntilde" 	g2="v" 	k="20" />
<hkern g1="h,m,n,ntilde" 	g2="w" 	k="20" />
<hkern g1="h,m,n,ntilde" 	g2="y,yacute,ydieresis" 	k="20" />
<hkern g1="j" 	g2="j" 	k="-20" />
<hkern g1="k" 	g2="asterisk" 	k="20" />
<hkern g1="k" 	g2="backslash" 	k="92" />
<hkern g1="k" 	g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="82" />
<hkern g1="k" 	g2="comma,period,ellipsis" 	k="-20" />
<hkern g1="k" 	g2="d,q" 	k="82" />
<hkern g1="k" 	g2="f" 	k="20" />
<hkern g1="k" 	g2="g" 	k="82" />
<hkern g1="k" 	g2="guilsinglleft" 	k="123" />
<hkern g1="k" 	g2="guilsinglright" 	k="41" />
<hkern g1="k" 	g2="quotedbl,quotesingle" 	k="31" />
<hkern g1="k" 	g2="quoteleft,quotedblleft" 	k="20" />
<hkern g1="k" 	g2="quoteright,quotedblright" 	k="31" />
<hkern g1="k" 	g2="quotesinglbase,quotedblbase" 	k="-20" />
<hkern g1="k" 	g2="s" 	k="41" />
<hkern g1="k" 	g2="slash" 	k="-20" />
<hkern g1="k" 	g2="t" 	k="20" />
<hkern g1="k" 	g2="trademark" 	k="61" />
<hkern g1="k" 	g2="v" 	k="20" />
<hkern g1="k" 	g2="w" 	k="20" />
<hkern g1="k" 	g2="y,yacute,ydieresis" 	k="20" />
<hkern g1="k" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="31" />
<hkern g1="k" 	g2="ampersand" 	k="41" />
<hkern g1="k" 	g2="at" 	k="41" />
<hkern g1="k" 	g2="hyphen,endash,emdash" 	k="102" />
<hkern g1="k" 	g2="periodcentered" 	k="82" />
<hkern g1="k" 	g2="copyright" 	k="20" />
<hkern g1="k" 	g2="registered" 	k="20" />
<hkern g1="k" 	g2="u,ugrave,uacute,ucircumflex,udieresis" 	k="41" />
<hkern g1="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash" 	g2="asterisk" 	k="102" />
<hkern g1="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash" 	g2="backslash" 	k="174" />
<hkern g1="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash" 	g2="bracketright,braceright" 	k="31" />
<hkern g1="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash" 	g2="comma,period,ellipsis" 	k="41" />
<hkern g1="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash" 	g2="f" 	k="31" />
<hkern g1="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash" 	g2="parenright" 	k="20" />
<hkern g1="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash" 	g2="question" 	k="82" />
<hkern g1="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash" 	g2="quotedbl,quotesingle" 	k="113" />
<hkern g1="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash" 	g2="quoteleft,quotedblleft" 	k="82" />
<hkern g1="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash" 	g2="quoteright,quotedblright" 	k="92" />
<hkern g1="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash" 	g2="quotesinglbase,quotedblbase" 	k="41" />
<hkern g1="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash" 	g2="slash" 	k="31" />
<hkern g1="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash" 	g2="t" 	k="31" />
<hkern g1="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash" 	g2="trademark" 	k="154" />
<hkern g1="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash" 	g2="v" 	k="31" />
<hkern g1="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash" 	g2="w" 	k="31" />
<hkern g1="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash" 	g2="y,yacute,ydieresis" 	k="31" />
<hkern g1="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="10" />
<hkern g1="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash" 	g2="x" 	k="72" />
<hkern g1="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash" 	g2="z" 	k="51" />
<hkern g1="q" 	g2="j" 	k="-41" />
<hkern g1="r" 	g2="asterisk" 	k="-31" />
<hkern g1="r" 	g2="backslash" 	k="72" />
<hkern g1="r" 	g2="bracketright,braceright" 	k="20" />
<hkern g1="r" 	g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="61" />
<hkern g1="r" 	g2="colon,semicolon" 	k="20" />
<hkern g1="r" 	g2="comma,period,ellipsis" 	k="133" />
<hkern g1="r" 	g2="d,q" 	k="61" />
<hkern g1="r" 	g2="f" 	k="-20" />
<hkern g1="r" 	g2="g" 	k="61" />
<hkern g1="r" 	g2="guilsinglleft" 	k="72" />
<hkern g1="r" 	g2="parenright" 	k="31" />
<hkern g1="r" 	g2="question" 	k="-20" />
<hkern g1="r" 	g2="quotedbl,quotesingle" 	k="-31" />
<hkern g1="r" 	g2="quoteleft,quotedblleft" 	k="-20" />
<hkern g1="r" 	g2="quoteright,quotedblright" 	k="-31" />
<hkern g1="r" 	g2="quotesinglbase,quotedblbase" 	k="133" />
<hkern g1="r" 	g2="s" 	k="20" />
<hkern g1="r" 	g2="slash" 	k="123" />
<hkern g1="r" 	g2="t" 	k="-20" />
<hkern g1="r" 	g2="v" 	k="-20" />
<hkern g1="r" 	g2="w" 	k="-20" />
<hkern g1="r" 	g2="y,yacute,ydieresis" 	k="-20" />
<hkern g1="r" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="61" />
<hkern g1="r" 	g2="z" 	k="20" />
<hkern g1="r" 	g2="ampersand" 	k="82" />
<hkern g1="r" 	g2="at" 	k="20" />
<hkern g1="r" 	g2="hyphen,endash,emdash" 	k="51" />
<hkern g1="r" 	g2="periodcentered" 	k="41" />
<hkern g1="r" 	g2="copyright" 	k="-31" />
<hkern g1="r" 	g2="registered" 	k="-31" />
<hkern g1="s" 	g2="asterisk" 	k="92" />
<hkern g1="s" 	g2="backslash" 	k="164" />
<hkern g1="s" 	g2="bracketright,braceright" 	k="20" />
<hkern g1="s" 	g2="f" 	k="31" />
<hkern g1="s" 	g2="guilsinglleft" 	k="20" />
<hkern g1="s" 	g2="parenright" 	k="20" />
<hkern g1="s" 	g2="question" 	k="51" />
<hkern g1="s" 	g2="quotedbl,quotesingle" 	k="82" />
<hkern g1="s" 	g2="quoteleft,quotedblleft" 	k="61" />
<hkern g1="s" 	g2="quoteright,quotedblright" 	k="72" />
<hkern g1="s" 	g2="t" 	k="31" />
<hkern g1="s" 	g2="trademark" 	k="61" />
<hkern g1="s" 	g2="v" 	k="31" />
<hkern g1="s" 	g2="w" 	k="31" />
<hkern g1="s" 	g2="y,yacute,ydieresis" 	k="31" />
<hkern g1="s" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="-10" />
<hkern g1="s" 	g2="x" 	k="41" />
<hkern g1="s" 	g2="z" 	k="20" />
<hkern g1="t" 	g2="asterisk" 	k="20" />
<hkern g1="t" 	g2="backslash" 	k="82" />
<hkern g1="t" 	g2="bracketright,braceright" 	k="20" />
<hkern g1="t" 	g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="31" />
<hkern g1="t" 	g2="d,q" 	k="31" />
<hkern g1="t" 	g2="f" 	k="41" />
<hkern g1="t" 	g2="g" 	k="31" />
<hkern g1="t" 	g2="guilsinglleft" 	k="82" />
<hkern g1="t" 	g2="guilsinglright" 	k="20" />
<hkern g1="t" 	g2="parenright" 	k="20" />
<hkern g1="t" 	g2="quotedbl,quotesingle" 	k="20" />
<hkern g1="t" 	g2="quoteleft,quotedblleft" 	k="31" />
<hkern g1="t" 	g2="quoteright,quotedblright" 	k="20" />
<hkern g1="t" 	g2="s" 	k="20" />
<hkern g1="t" 	g2="t" 	k="41" />
<hkern g1="t" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="10" />
<hkern g1="t" 	g2="z" 	k="20" />
<hkern g1="t" 	g2="ampersand" 	k="10" />
<hkern g1="t" 	g2="hyphen,endash,emdash" 	k="41" />
<hkern g1="t" 	g2="periodcentered" 	k="20" />
<hkern g1="u,ugrave,uacute,ucircumflex,udieresis" 	g2="asterisk" 	k="20" />
<hkern g1="u,ugrave,uacute,ucircumflex,udieresis" 	g2="backslash" 	k="143" />
<hkern g1="u,ugrave,uacute,ucircumflex,udieresis" 	g2="trademark" 	k="61" />
<hkern g1="v" 	g2="bracketright,braceright" 	k="41" />
<hkern g1="v" 	g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="31" />
<hkern g1="v" 	g2="colon,semicolon" 	k="31" />
<hkern g1="v" 	g2="comma,period,ellipsis" 	k="113" />
<hkern g1="v" 	g2="d,q" 	k="41" />
<hkern g1="v" 	g2="f" 	k="-20" />
<hkern g1="v" 	g2="g" 	k="41" />
<hkern g1="v" 	g2="guilsinglleft" 	k="82" />
<hkern g1="v" 	g2="guilsinglright" 	k="20" />
<hkern g1="v" 	g2="quotedbl,quotesingle" 	k="-41" />
<hkern g1="v" 	g2="quoteleft,quotedblleft" 	k="-31" />
<hkern g1="v" 	g2="quoteright,quotedblright" 	k="-31" />
<hkern g1="v" 	g2="quotesinglbase,quotedblbase" 	k="113" />
<hkern g1="v" 	g2="s" 	k="20" />
<hkern g1="v" 	g2="t" 	k="-20" />
<hkern g1="v" 	g2="w" 	k="-20" />
<hkern g1="v" 	g2="y,yacute,ydieresis" 	k="-20" />
<hkern g1="v" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="51" />
<hkern g1="v" 	g2="z" 	k="31" />
<hkern g1="v" 	g2="hyphen,endash,emdash" 	k="61" />
<hkern g1="w" 	g2="asterisk" 	k="-31" />
<hkern g1="w" 	g2="backslash" 	k="102" />
<hkern g1="w" 	g2="bracketright,braceright" 	k="41" />
<hkern g1="w" 	g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="31" />
<hkern g1="w" 	g2="colon,semicolon" 	k="31" />
<hkern g1="w" 	g2="comma,period,ellipsis" 	k="113" />
<hkern g1="w" 	g2="d,q" 	k="41" />
<hkern g1="w" 	g2="f" 	k="-20" />
<hkern g1="w" 	g2="g" 	k="41" />
<hkern g1="w" 	g2="guilsinglleft" 	k="82" />
<hkern g1="w" 	g2="guilsinglright" 	k="20" />
<hkern g1="w" 	g2="parenright" 	k="41" />
<hkern g1="w" 	g2="quotedbl,quotesingle" 	k="-41" />
<hkern g1="w" 	g2="quoteleft,quotedblleft" 	k="-31" />
<hkern g1="w" 	g2="quoteright,quotedblright" 	k="-31" />
<hkern g1="w" 	g2="quotesinglbase,quotedblbase" 	k="113" />
<hkern g1="w" 	g2="s" 	k="20" />
<hkern g1="w" 	g2="slash" 	k="82" />
<hkern g1="w" 	g2="t" 	k="-20" />
<hkern g1="w" 	g2="v" 	k="-20" />
<hkern g1="w" 	g2="w" 	k="-20" />
<hkern g1="w" 	g2="y,yacute,ydieresis" 	k="-20" />
<hkern g1="w" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="51" />
<hkern g1="w" 	g2="x" 	k="20" />
<hkern g1="w" 	g2="z" 	k="31" />
<hkern g1="w" 	g2="ampersand" 	k="72" />
<hkern g1="w" 	g2="at" 	k="20" />
<hkern g1="w" 	g2="hyphen,endash,emdash" 	k="61" />
<hkern g1="w" 	g2="periodcentered" 	k="41" />
<hkern g1="x" 	g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="72" />
<hkern g1="x" 	g2="d,q" 	k="61" />
<hkern g1="x" 	g2="f" 	k="20" />
<hkern g1="x" 	g2="g" 	k="61" />
<hkern g1="x" 	g2="guilsinglleft" 	k="113" />
<hkern g1="x" 	g2="guilsinglright" 	k="41" />
<hkern g1="x" 	g2="quotedbl,quotesingle" 	k="20" />
<hkern g1="x" 	g2="quoteleft,quotedblleft" 	k="20" />
<hkern g1="x" 	g2="quoteright,quotedblright" 	k="20" />
<hkern g1="x" 	g2="s" 	k="51" />
<hkern g1="x" 	g2="t" 	k="20" />
<hkern g1="x" 	g2="w" 	k="20" />
<hkern g1="x" 	g2="y,yacute,ydieresis" 	k="20" />
<hkern g1="x" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="31" />
<hkern g1="x" 	g2="hyphen,endash,emdash" 	k="102" />
<hkern g1="x" 	g2="copyright" 	k="51" />
<hkern g1="x" 	g2="u,ugrave,uacute,ucircumflex,udieresis" 	k="31" />
<hkern g1="y,yacute,ydieresis" 	g2="asterisk" 	k="-31" />
<hkern g1="y,yacute,ydieresis" 	g2="backslash" 	k="102" />
<hkern g1="y,yacute,ydieresis" 	g2="bracketright,braceright" 	k="41" />
<hkern g1="y,yacute,ydieresis" 	g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="31" />
<hkern g1="y,yacute,ydieresis" 	g2="colon,semicolon" 	k="31" />
<hkern g1="y,yacute,ydieresis" 	g2="comma,period,ellipsis" 	k="113" />
<hkern g1="y,yacute,ydieresis" 	g2="d,q" 	k="41" />
<hkern g1="y,yacute,ydieresis" 	g2="f" 	k="-20" />
<hkern g1="y,yacute,ydieresis" 	g2="g" 	k="41" />
<hkern g1="y,yacute,ydieresis" 	g2="guilsinglleft" 	k="82" />
<hkern g1="y,yacute,ydieresis" 	g2="guilsinglright" 	k="20" />
<hkern g1="y,yacute,ydieresis" 	g2="parenright" 	k="41" />
<hkern g1="y,yacute,ydieresis" 	g2="quotedbl,quotesingle" 	k="-41" />
<hkern g1="y,yacute,ydieresis" 	g2="quoteleft,quotedblleft" 	k="-31" />
<hkern g1="y,yacute,ydieresis" 	g2="quoteright,quotedblright" 	k="-31" />
<hkern g1="y,yacute,ydieresis" 	g2="quotesinglbase,quotedblbase" 	k="113" />
<hkern g1="y,yacute,ydieresis" 	g2="s" 	k="20" />
<hkern g1="y,yacute,ydieresis" 	g2="slash" 	k="82" />
<hkern g1="y,yacute,ydieresis" 	g2="t" 	k="-20" />
<hkern g1="y,yacute,ydieresis" 	g2="v" 	k="-20" />
<hkern g1="y,yacute,ydieresis" 	g2="w" 	k="-20" />
<hkern g1="y,yacute,ydieresis" 	g2="y,yacute,ydieresis" 	k="-20" />
<hkern g1="y,yacute,ydieresis" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="51" />
<hkern g1="y,yacute,ydieresis" 	g2="x" 	k="20" />
<hkern g1="y,yacute,ydieresis" 	g2="z" 	k="31" />
<hkern g1="y,yacute,ydieresis" 	g2="ampersand" 	k="72" />
<hkern g1="y,yacute,ydieresis" 	g2="at" 	k="20" />
<hkern g1="y,yacute,ydieresis" 	g2="hyphen,endash,emdash" 	k="61" />
<hkern g1="y,yacute,ydieresis" 	g2="periodcentered" 	k="41" />
<hkern g1="z" 	g2="asterisk" 	k="31" />
<hkern g1="z" 	g2="backslash" 	k="123" />
<hkern g1="z" 	g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="72" />
<hkern g1="z" 	g2="comma,period,ellipsis" 	k="-20" />
<hkern g1="z" 	g2="d,q" 	k="51" />
<hkern g1="z" 	g2="g" 	k="51" />
<hkern g1="z" 	g2="guilsinglleft" 	k="72" />
<hkern g1="z" 	g2="quotesinglbase,quotedblbase" 	k="-20" />
<hkern g1="z" 	g2="s" 	k="20" />
<hkern g1="z" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="20" />
<hkern g1="z" 	g2="ampersand" 	k="41" />
<hkern g1="z" 	g2="at" 	k="31" />
<hkern g1="z" 	g2="hyphen,endash,emdash" 	k="61" />
<hkern g1="z" 	g2="periodcentered" 	k="20" />
<hkern g1="ampersand" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="-31" />
<hkern g1="ampersand" 	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="20" />
<hkern g1="ampersand" 	g2="S" 	k="10" />
<hkern g1="ampersand" 	g2="T" 	k="154" />
<hkern g1="ampersand" 	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	k="20" />
<hkern g1="ampersand" 	g2="W" 	k="92" />
<hkern g1="ampersand" 	g2="Y,Yacute,Ydieresis" 	k="174" />
<hkern g1="ampersand" 	g2="Z" 	k="-31" />
<hkern g1="ampersand" 	g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="20" />
<hkern g1="ampersand" 	g2="d,q" 	k="20" />
<hkern g1="ampersand" 	g2="f" 	k="41" />
<hkern g1="ampersand" 	g2="g" 	k="20" />
<hkern g1="ampersand" 	g2="j" 	k="-41" />
<hkern g1="ampersand" 	g2="s" 	k="20" />
<hkern g1="ampersand" 	g2="t" 	k="31" />
<hkern g1="ampersand" 	g2="u,ugrave,uacute,ucircumflex,udieresis" 	k="20" />
<hkern g1="ampersand" 	g2="w" 	k="82" />
<hkern g1="ampersand" 	g2="y,yacute,ydieresis" 	k="82" />
<hkern g1="ampersand" 	g2="z" 	k="-20" />
<hkern g1="at" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="82" />
<hkern g1="at" 	g2="T" 	k="154" />
<hkern g1="at" 	g2="W" 	k="72" />
<hkern g1="at" 	g2="Y,Yacute,Ydieresis" 	k="174" />
<hkern g1="at" 	g2="Z" 	k="61" />
<hkern g1="at" 	g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="20" />
<hkern g1="at" 	g2="s" 	k="10" />
<hkern g1="at" 	g2="w" 	k="20" />
<hkern g1="at" 	g2="y,yacute,ydieresis" 	k="20" />
<hkern g1="at" 	g2="z" 	k="51" />
<hkern g1="at" 	g2="AE" 	k="92" />
<hkern g1="at" 	g2="J" 	k="20" />
<hkern g1="at" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="20" />
<hkern g1="copyright" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="82" />
<hkern g1="copyright" 	g2="T" 	k="113" />
<hkern g1="copyright" 	g2="W" 	k="61" />
<hkern g1="copyright" 	g2="Y,Yacute,Ydieresis" 	k="133" />
<hkern g1="copyright" 	g2="Z" 	k="82" />
<hkern g1="copyright" 	g2="z" 	k="41" />
<hkern g1="copyright" 	g2="AE" 	k="82" />
<hkern g1="copyright" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="20" />
<hkern g1="copyright" 	g2="V" 	k="72" />
<hkern g1="copyright" 	g2="X" 	k="102" />
<hkern g1="copyright" 	g2="x" 	k="51" />
<hkern g1="asterisk" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="215" />
<hkern g1="asterisk" 	g2="AE" 	k="236" />
<hkern g1="asterisk" 	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="41" />
<hkern g1="asterisk" 	g2="J" 	k="236" />
<hkern g1="asterisk" 	g2="S" 	k="20" />
<hkern g1="asterisk" 	g2="Y,Yacute,Ydieresis" 	k="41" />
<hkern g1="asterisk" 	g2="Z" 	k="51" />
<hkern g1="asterisk" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="102" />
<hkern g1="asterisk" 	g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="102" />
<hkern g1="asterisk" 	g2="d,q" 	k="102" />
<hkern g1="asterisk" 	g2="g" 	k="102" />
<hkern g1="asterisk" 	g2="m,n,r,ntilde" 	k="20" />
<hkern g1="asterisk" 	g2="s" 	k="72" />
<hkern g1="asterisk" 	g2="u,ugrave,uacute,ucircumflex,udieresis" 	k="20" />
<hkern g1="asterisk" 	g2="w" 	k="-31" />
<hkern g1="asterisk" 	g2="y,yacute,ydieresis" 	k="-31" />
<hkern g1="asterisk" 	g2="z" 	k="61" />
<hkern g1="backslash" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="-20" />
<hkern g1="backslash" 	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="102" />
<hkern g1="backslash" 	g2="S" 	k="20" />
<hkern g1="backslash" 	g2="Y,Yacute,Ydieresis" 	k="195" />
<hkern g1="backslash" 	g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="31" />
<hkern g1="backslash" 	g2="d,q" 	k="31" />
<hkern g1="backslash" 	g2="g" 	k="31" />
<hkern g1="backslash" 	g2="s" 	k="10" />
<hkern g1="backslash" 	g2="u,ugrave,uacute,ucircumflex,udieresis" 	k="41" />
<hkern g1="backslash" 	g2="w" 	k="82" />
<hkern g1="backslash" 	g2="y,yacute,ydieresis" 	k="102" />
<hkern g1="backslash" 	g2="z" 	k="-20" />
<hkern g1="backslash" 	g2="T" 	k="174" />
<hkern g1="backslash" 	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	k="61" />
<hkern g1="backslash" 	g2="W" 	k="143" />
<hkern g1="backslash" 	g2="f" 	k="61" />
<hkern g1="backslash" 	g2="t" 	k="82" />
<hkern g1="bracketleft,braceleft" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="20" />
<hkern g1="bracketleft,braceleft" 	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="20" />
<hkern g1="bracketleft,braceleft" 	g2="J" 	k="20" />
<hkern g1="bracketleft,braceleft" 	g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="31" />
<hkern g1="bracketleft,braceleft" 	g2="d,q" 	k="20" />
<hkern g1="bracketleft,braceleft" 	g2="g" 	k="20" />
<hkern g1="bracketleft,braceleft" 	g2="s" 	k="10" />
<hkern g1="bracketleft,braceleft" 	g2="w" 	k="41" />
<hkern g1="bracketleft,braceleft" 	g2="y,yacute,ydieresis" 	k="20" />
<hkern g1="bracketleft,braceleft" 	g2="f" 	k="20" />
<hkern g1="bracketleft,braceleft" 	g2="t" 	k="20" />
<hkern g1="bracketleft,braceleft" 	g2="j" 	k="-72" />
<hkern g1="bracketleft,braceleft" 	g2="v" 	k="41" />
<hkern g1="colon,semicolon" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="-20" />
<hkern g1="colon,semicolon" 	g2="Y,Yacute,Ydieresis" 	k="113" />
<hkern g1="colon,semicolon" 	g2="Z" 	k="-20" />
<hkern g1="colon,semicolon" 	g2="w" 	k="31" />
<hkern g1="colon,semicolon" 	g2="y,yacute,ydieresis" 	k="31" />
<hkern g1="colon,semicolon" 	g2="T" 	k="164" />
<hkern g1="colon,semicolon" 	g2="W" 	k="51" />
<hkern g1="colon,semicolon" 	g2="f" 	k="20" />
<hkern g1="colon,semicolon" 	g2="t" 	k="20" />
<hkern g1="colon,semicolon" 	g2="j" 	k="-20" />
<hkern g1="colon,semicolon" 	g2="v" 	k="31" />
<hkern g1="colon,semicolon" 	g2="V" 	k="82" />
<hkern g1="comma,period,ellipsis" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="-31" />
<hkern g1="comma,period,ellipsis" 	g2="AE" 	k="-20" />
<hkern g1="comma,period,ellipsis" 	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="92" />
<hkern g1="comma,period,ellipsis" 	g2="J" 	k="-20" />
<hkern g1="comma,period,ellipsis" 	g2="S" 	k="41" />
<hkern g1="comma,period,ellipsis" 	g2="Y,Yacute,Ydieresis" 	k="154" />
<hkern g1="comma,period,ellipsis" 	g2="Z" 	k="-20" />
<hkern g1="comma,period,ellipsis" 	g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="41" />
<hkern g1="comma,period,ellipsis" 	g2="d,q" 	k="41" />
<hkern g1="comma,period,ellipsis" 	g2="g" 	k="41" />
<hkern g1="comma,period,ellipsis" 	g2="u,ugrave,uacute,ucircumflex,udieresis" 	k="41" />
<hkern g1="comma,period,ellipsis" 	g2="w" 	k="113" />
<hkern g1="comma,period,ellipsis" 	g2="y,yacute,ydieresis" 	k="113" />
<hkern g1="comma,period,ellipsis" 	g2="z" 	k="-20" />
<hkern g1="comma,period,ellipsis" 	g2="T" 	k="133" />
<hkern g1="comma,period,ellipsis" 	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	k="61" />
<hkern g1="comma,period,ellipsis" 	g2="W" 	k="113" />
<hkern g1="comma,period,ellipsis" 	g2="f" 	k="51" />
<hkern g1="comma,period,ellipsis" 	g2="t" 	k="72" />
<hkern g1="comma,period,ellipsis" 	g2="j" 	k="-20" />
<hkern g1="comma,period,ellipsis" 	g2="v" 	k="113" />
<hkern g1="comma,period,ellipsis" 	g2="V" 	k="123" />
<hkern g1="exclamdown" 	g2="Y,Yacute,Ydieresis" 	k="61" />
<hkern g1="exclamdown" 	g2="T" 	k="61" />
<hkern g1="exclamdown" 	g2="W" 	k="31" />
<hkern g1="guilsinglleft" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="20" />
<hkern g1="guilsinglleft" 	g2="Y,Yacute,Ydieresis" 	k="164" />
<hkern g1="guilsinglleft" 	g2="w" 	k="20" />
<hkern g1="guilsinglleft" 	g2="y,yacute,ydieresis" 	k="41" />
<hkern g1="guilsinglleft" 	g2="T" 	k="195" />
<hkern g1="guilsinglleft" 	g2="W" 	k="82" />
<hkern g1="guilsinglleft" 	g2="f" 	k="20" />
<hkern g1="guilsinglleft" 	g2="t" 	k="20" />
<hkern g1="guilsinglleft" 	g2="v" 	k="20" />
<hkern g1="guilsinglleft" 	g2="V" 	k="102" />
<hkern g1="guilsinglleft" 	g2="X" 	k="72" />
<hkern g1="guilsinglleft" 	g2="x" 	k="41" />
<hkern g1="guilsinglright" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="82" />
<hkern g1="guilsinglright" 	g2="AE" 	k="72" />
<hkern g1="guilsinglright" 	g2="Y,Yacute,Ydieresis" 	k="236" />
<hkern g1="guilsinglright" 	g2="Z" 	k="41" />
<hkern g1="guilsinglright" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="20" />
<hkern g1="guilsinglright" 	g2="s" 	k="20" />
<hkern g1="guilsinglright" 	g2="w" 	k="82" />
<hkern g1="guilsinglright" 	g2="y,yacute,ydieresis" 	k="72" />
<hkern g1="guilsinglright" 	g2="z" 	k="61" />
<hkern g1="guilsinglright" 	g2="T" 	k="276" />
<hkern g1="guilsinglright" 	g2="W" 	k="143" />
<hkern g1="guilsinglright" 	g2="f" 	k="41" />
<hkern g1="guilsinglright" 	g2="t" 	k="61" />
<hkern g1="guilsinglright" 	g2="v" 	k="82" />
<hkern g1="guilsinglright" 	g2="V" 	k="154" />
<hkern g1="guilsinglright" 	g2="X" 	k="133" />
<hkern g1="guilsinglright" 	g2="x" 	k="113" />
<hkern g1="hyphen,endash,emdash" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="51" />
<hkern g1="hyphen,endash,emdash" 	g2="AE" 	k="41" />
<hkern g1="hyphen,endash,emdash" 	g2="S" 	k="-20" />
<hkern g1="hyphen,endash,emdash" 	g2="Y,Yacute,Ydieresis" 	k="215" />
<hkern g1="hyphen,endash,emdash" 	g2="Z" 	k="20" />
<hkern g1="hyphen,endash,emdash" 	g2="w" 	k="61" />
<hkern g1="hyphen,endash,emdash" 	g2="y,yacute,ydieresis" 	k="61" />
<hkern g1="hyphen,endash,emdash" 	g2="z" 	k="41" />
<hkern g1="hyphen,endash,emdash" 	g2="T" 	k="195" />
<hkern g1="hyphen,endash,emdash" 	g2="W" 	k="82" />
<hkern g1="hyphen,endash,emdash" 	g2="f" 	k="20" />
<hkern g1="hyphen,endash,emdash" 	g2="v" 	k="61" />
<hkern g1="hyphen,endash,emdash" 	g2="V" 	k="154" />
<hkern g1="hyphen,endash,emdash" 	g2="X" 	k="123" />
<hkern g1="hyphen,endash,emdash" 	g2="x" 	k="102" />
<hkern g1="parenleft" 	g2="J" 	k="31" />
<hkern g1="parenleft" 	g2="S" 	k="20" />
<hkern g1="parenleft" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="20" />
<hkern g1="parenleft" 	g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="20" />
<hkern g1="parenleft" 	g2="d,q" 	k="20" />
<hkern g1="parenleft" 	g2="g" 	k="20" />
<hkern g1="parenleft" 	g2="s" 	k="41" />
<hkern g1="parenleft" 	g2="u,ugrave,uacute,ucircumflex,udieresis" 	k="20" />
<hkern g1="parenleft" 	g2="w" 	k="41" />
<hkern g1="parenleft" 	g2="y,yacute,ydieresis" 	k="20" />
<hkern g1="parenleft" 	g2="f" 	k="20" />
<hkern g1="parenleft" 	g2="t" 	k="41" />
<hkern g1="parenleft" 	g2="j" 	k="-72" />
<hkern g1="periodcentered" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="61" />
<hkern g1="periodcentered" 	g2="AE" 	k="41" />
<hkern g1="periodcentered" 	g2="Y,Yacute,Ydieresis" 	k="154" />
<hkern g1="periodcentered" 	g2="Z" 	k="20" />
<hkern g1="periodcentered" 	g2="w" 	k="41" />
<hkern g1="periodcentered" 	g2="y,yacute,ydieresis" 	k="41" />
<hkern g1="periodcentered" 	g2="z" 	k="20" />
<hkern g1="periodcentered" 	g2="T" 	k="174" />
<hkern g1="periodcentered" 	g2="W" 	k="61" />
<hkern g1="periodcentered" 	g2="f" 	k="20" />
<hkern g1="questiondown" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="-20" />
<hkern g1="questiondown" 	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="61" />
<hkern g1="questiondown" 	g2="S" 	k="20" />
<hkern g1="questiondown" 	g2="Y,Yacute,Ydieresis" 	k="246" />
<hkern g1="questiondown" 	g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="41" />
<hkern g1="questiondown" 	g2="d,q" 	k="41" />
<hkern g1="questiondown" 	g2="g" 	k="20" />
<hkern g1="questiondown" 	g2="s" 	k="20" />
<hkern g1="questiondown" 	g2="u,ugrave,uacute,ucircumflex,udieresis" 	k="31" />
<hkern g1="questiondown" 	g2="w" 	k="154" />
<hkern g1="questiondown" 	g2="y,yacute,ydieresis" 	k="82" />
<hkern g1="questiondown" 	g2="T" 	k="195" />
<hkern g1="questiondown" 	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	k="51" />
<hkern g1="questiondown" 	g2="W" 	k="154" />
<hkern g1="questiondown" 	g2="f" 	k="51" />
<hkern g1="questiondown" 	g2="t" 	k="72" />
<hkern g1="quoteleft,quotedblleft" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="133" />
<hkern g1="quoteleft,quotedblleft" 	g2="AE" 	k="164" />
<hkern g1="quoteleft,quotedblleft" 	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="31" />
<hkern g1="quoteleft,quotedblleft" 	g2="J" 	k="174" />
<hkern g1="quoteleft,quotedblleft" 	g2="S" 	k="20" />
<hkern g1="quoteleft,quotedblleft" 	g2="Y,Yacute,Ydieresis" 	k="-20" />
<hkern g1="quoteleft,quotedblleft" 	g2="Z" 	k="20" />
<hkern g1="quoteleft,quotedblleft" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="72" />
<hkern g1="quoteleft,quotedblleft" 	g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="92" />
<hkern g1="quoteleft,quotedblleft" 	g2="d,q" 	k="102" />
<hkern g1="quoteleft,quotedblleft" 	g2="g" 	k="102" />
<hkern g1="quoteleft,quotedblleft" 	g2="s" 	k="61" />
<hkern g1="quoteleft,quotedblleft" 	g2="w" 	k="-31" />
<hkern g1="quoteleft,quotedblleft" 	g2="y,yacute,ydieresis" 	k="-31" />
<hkern g1="quoteleft,quotedblleft" 	g2="T" 	k="-20" />
<hkern g1="quoteleft,quotedblleft" 	g2="W" 	k="-20" />
<hkern g1="quoteleft,quotedblleft" 	g2="v" 	k="-31" />
<hkern g1="quoteleft,quotedblleft" 	g2="V" 	k="-20" />
<hkern g1="quoteleft,quotedblleft" 	g2="X" 	k="20" />
<hkern g1="quoteleft,quotedblleft" 	g2="germandbls" 	k="10" />
<hkern g1="quoteright,quotedblright" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="154" />
<hkern g1="quoteright,quotedblright" 	g2="AE" 	k="174" />
<hkern g1="quoteright,quotedblright" 	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="72" />
<hkern g1="quoteright,quotedblright" 	g2="J" 	k="174" />
<hkern g1="quoteright,quotedblright" 	g2="S" 	k="31" />
<hkern g1="quoteright,quotedblright" 	g2="Y,Yacute,Ydieresis" 	k="-20" />
<hkern g1="quoteright,quotedblright" 	g2="Z" 	k="20" />
<hkern g1="quoteright,quotedblright" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="113" />
<hkern g1="quoteright,quotedblright" 	g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="113" />
<hkern g1="quoteright,quotedblright" 	g2="d,q" 	k="143" />
<hkern g1="quoteright,quotedblright" 	g2="g" 	k="143" />
<hkern g1="quoteright,quotedblright" 	g2="m,n,r,ntilde" 	k="20" />
<hkern g1="quoteright,quotedblright" 	g2="s" 	k="92" />
<hkern g1="quoteright,quotedblright" 	g2="u,ugrave,uacute,ucircumflex,udieresis" 	k="20" />
<hkern g1="quoteright,quotedblright" 	g2="y,yacute,ydieresis" 	k="-20" />
<hkern g1="quoteright,quotedblright" 	g2="z" 	k="31" />
<hkern g1="quoteright,quotedblright" 	g2="T" 	k="-20" />
<hkern g1="quoteright,quotedblright" 	g2="W" 	k="-20" />
<hkern g1="quoteright,quotedblright" 	g2="f" 	k="20" />
<hkern g1="quoteright,quotedblright" 	g2="t" 	k="31" />
<hkern g1="quoteright,quotedblright" 	g2="V" 	k="-20" />
<hkern g1="quoteright,quotedblright" 	g2="X" 	k="20" />
<hkern g1="quoteright,quotedblright" 	g2="x" 	k="31" />
<hkern g1="quoteright,quotedblright" 	g2="germandbls" 	k="20" />
<hkern g1="quoteright,quotedblright" 	g2="p" 	k="20" />
<hkern g1="quotesinglbase,quotedblbase" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="-31" />
<hkern g1="quotesinglbase,quotedblbase" 	g2="AE" 	k="-20" />
<hkern g1="quotesinglbase,quotedblbase" 	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="92" />
<hkern g1="quotesinglbase,quotedblbase" 	g2="J" 	k="-20" />
<hkern g1="quotesinglbase,quotedblbase" 	g2="S" 	k="41" />
<hkern g1="quotesinglbase,quotedblbase" 	g2="Y,Yacute,Ydieresis" 	k="174" />
<hkern g1="quotesinglbase,quotedblbase" 	g2="Z" 	k="-20" />
<hkern g1="quotesinglbase,quotedblbase" 	g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="41" />
<hkern g1="quotesinglbase,quotedblbase" 	g2="d,q" 	k="41" />
<hkern g1="quotesinglbase,quotedblbase" 	g2="g" 	k="41" />
<hkern g1="quotesinglbase,quotedblbase" 	g2="u,ugrave,uacute,ucircumflex,udieresis" 	k="41" />
<hkern g1="quotesinglbase,quotedblbase" 	g2="w" 	k="113" />
<hkern g1="quotesinglbase,quotedblbase" 	g2="y,yacute,ydieresis" 	k="82" />
<hkern g1="quotesinglbase,quotedblbase" 	g2="z" 	k="-20" />
<hkern g1="quotesinglbase,quotedblbase" 	g2="T" 	k="164" />
<hkern g1="quotesinglbase,quotedblbase" 	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	k="61" />
<hkern g1="quotesinglbase,quotedblbase" 	g2="W" 	k="113" />
<hkern g1="quotesinglbase,quotedblbase" 	g2="f" 	k="51" />
<hkern g1="quotesinglbase,quotedblbase" 	g2="t" 	k="72" />
<hkern g1="quotesinglbase,quotedblbase" 	g2="j" 	k="-41" />
<hkern g1="quotesinglbase,quotedblbase" 	g2="v" 	k="113" />
<hkern g1="quotesinglbase,quotedblbase" 	g2="V" 	k="143" />
<hkern g1="slash" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="195" />
<hkern g1="slash" 	g2="AE" 	k="174" />
<hkern g1="slash" 	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="102" />
<hkern g1="slash" 	g2="J" 	k="236" />
<hkern g1="slash" 	g2="S" 	k="41" />
<hkern g1="slash" 	g2="Y,Yacute,Ydieresis" 	k="-20" />
<hkern g1="slash" 	g2="Z" 	k="20" />
<hkern g1="slash" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="174" />
<hkern g1="slash" 	g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="174" />
<hkern g1="slash" 	g2="d,q" 	k="174" />
<hkern g1="slash" 	g2="g" 	k="174" />
<hkern g1="slash" 	g2="m,n,r,ntilde" 	k="123" />
<hkern g1="slash" 	g2="s" 	k="164" />
<hkern g1="slash" 	g2="u,ugrave,uacute,ucircumflex,udieresis" 	k="123" />
<hkern g1="slash" 	g2="w" 	k="102" />
<hkern g1="slash" 	g2="y,yacute,ydieresis" 	k="82" />
<hkern g1="slash" 	g2="z" 	k="154" />
<hkern g1="slash" 	g2="T" 	k="-20" />
<hkern g1="slash" 	g2="W" 	k="-20" />
<hkern g1="slash" 	g2="f" 	k="61" />
<hkern g1="slash" 	g2="t" 	k="72" />
</font>
</defs></svg> 